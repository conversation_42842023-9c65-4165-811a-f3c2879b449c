<?php
/*
* Single Live TV Channel Page
* @since 2.5.0
*/

get_header();

if (have_posts()) : while (have_posts()) : the_post();

$stream_url = get_post_meta(get_the_ID(), '_live_tv_stream_url', true);
$channel_logo = get_post_meta(get_the_ID(), '_live_tv_channel_logo', true);
$channel_category = get_post_meta(get_the_ID(), '_live_tv_category', true);
$is_active = get_post_meta(get_the_ID(), '_live_tv_is_active', true);

$categories = array(
    'news' => __('News', 'dooplay'),
    'entertainment' => __('Entertainment', 'dooplay'),
    'sports' => __('Sports', 'dooplay'),
    'music' => __('Music', 'dooplay'),
    'kids' => __('Kids', 'dooplay'),
    'religious' => __('Religious', 'dooplay'),
    'other' => __('Other', 'dooplay')
);
?>

<div id="single" class="live-tv-single">
    <div class="container">
        <div class="live-tv-single-header">
            <div class="channel-info-header">
                <?php if ($channel_logo): ?>
                    <div class="channel-logo">
                        <img src="<?php echo esc_url($channel_logo); ?>" alt="<?php the_title(); ?>" />
                    </div>
                <?php endif; ?>
                
                <div class="channel-details">
                    <h1 class="channel-title"><?php the_title(); ?></h1>
                    <div class="channel-meta">
                        <span class="channel-category">
                            <i class="fas fa-tag"></i>
                            <?php echo isset($categories[$channel_category]) ? $categories[$channel_category] : ucfirst($channel_category); ?>
                        </span>
                        <span class="channel-status <?php echo $is_active ? 'active' : 'inactive'; ?>">
                            <i class="fas fa-circle"></i>
                            <?php echo $is_active ? __('LIVE', 'dooplay') : __('OFFLINE', 'dooplay'); ?>
                        </span>
                    </div>
                    
                    <?php if ($is_active && $stream_url): ?>
                    <button class="play-channel-btn" data-stream="<?php echo esc_attr($stream_url); ?>">
                        <i class="fas fa-play"></i>
                        <?php _e('Watch Live', 'dooplay'); ?>
                    </button>
                    <?php else: ?>
                    <button class="play-channel-btn disabled" disabled>
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php _e('Channel Offline', 'dooplay'); ?>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="live-tv-content">
            <div class="main-content">
                <div class="channel-description">
                    <h3><?php _e('About This Channel', 'dooplay'); ?></h3>
                    <?php if (get_the_content()): ?>
                        <div class="description-text">
                            <?php the_content(); ?>
                        </div>
                    <?php else: ?>
                        <p><?php _e('No description available for this channel.', 'dooplay'); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Related Channels -->
                <?php
                $related_channels = get_posts(array(
                    'post_type' => 'live_tv_channels',
                    'posts_per_page' => 6,
                    'post__not_in' => array(get_the_ID()),
                    'meta_query' => array(
                        'relation' => 'AND',
                        array(
                            'key' => '_live_tv_is_active',
                            'value' => '1',
                            'compare' => '='
                        ),
                        array(
                            'key' => '_live_tv_category',
                            'value' => $channel_category,
                            'compare' => '='
                        )
                    )
                ));

                if ($related_channels): ?>
                <div class="related-channels">
                    <h3><?php _e('Related Channels', 'dooplay'); ?></h3>
                    <div class="related-channels-grid">
                        <?php foreach ($related_channels as $related): 
                            $related_stream = get_post_meta($related->ID, '_live_tv_stream_url', true);
                            $related_logo = get_post_meta($related->ID, '_live_tv_channel_logo', true);
                        ?>
                        <div class="related-channel-card">
                            <a href="<?php echo get_permalink($related->ID); ?>" class="channel-link">
                                <div class="channel-thumbnail">
                                    <?php if ($related_logo): ?>
                                        <img src="<?php echo esc_url($related_logo); ?>" alt="<?php echo esc_attr($related->post_title); ?>" />
                                    <?php else: ?>
                                        <div class="channel-placeholder">
                                            <i class="fas fa-tv"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="channel-overlay">
                                        <div class="play-icon">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>
                                </div>
                                <h4 class="channel-name"><?php echo esc_html($related->post_title); ?></h4>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <div class="sidebar">
                <div class="channel-actions">
                    <h4><?php _e('Channel Actions', 'dooplay'); ?></h4>
                    <div class="action-buttons">
                        <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="action-btn">
                            <i class="fas fa-arrow-left"></i>
                            <?php _e('Back to Channels', 'dooplay'); ?>
                        </a>
                        
                        <button class="action-btn share-btn" onclick="shareChannel()">
                            <i class="fas fa-share-alt"></i>
                            <?php _e('Share Channel', 'dooplay'); ?>
                        </button>
                        
                        <?php if ($is_active && $stream_url): ?>
                        <button class="action-btn fullscreen-btn" onclick="openFullscreenPlayer()">
                            <i class="fas fa-expand"></i>
                            <?php _e('Fullscreen Player', 'dooplay'); ?>
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Channel Stats -->
                <div class="channel-stats">
                    <h4><?php _e('Channel Information', 'dooplay'); ?></h4>
                    <div class="stats-list">
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Category:', 'dooplay'); ?></span>
                            <span class="stat-value"><?php echo isset($categories[$channel_category]) ? $categories[$channel_category] : ucfirst($channel_category); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Status:', 'dooplay'); ?></span>
                            <span class="stat-value <?php echo $is_active ? 'active' : 'inactive'; ?>">
                                <?php echo $is_active ? __('Live', 'dooplay') : __('Offline', 'dooplay'); ?>
                            </span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label"><?php _e('Added:', 'dooplay'); ?></span>
                            <span class="stat-value"><?php echo get_the_date(); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Live TV Player Modal -->
<div id="live-tv-player-modal" class="live-tv-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="player-channel-title"><?php the_title(); ?></h3>
            <button class="modal-close" id="close-player">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="live-tv-player-container">
                <video id="live-tv-player" controls autoplay>
                    <source src="" type="application/x-mpegURL">
                    <?php _e('Your browser does not support the video tag.', 'dooplay'); ?>
                </video>
            </div>
            <div class="player-controls">
                <button id="player-fullscreen" class="control-btn">
                    <i class="fas fa-expand"></i>
                    <?php _e('Fullscreen', 'dooplay'); ?>
                </button>
                <button id="player-volume" class="control-btn">
                    <i class="fas fa-volume-up"></i>
                    <?php _e('Volume', 'dooplay'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.live-tv-single {
    padding: 20px 0;
}

.live-tv-single-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 40px 0;
    margin-bottom: 30px;
    border-radius: 10px;
}

.channel-info-header {
    display: flex;
    align-items: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.channel-logo {
    flex-shrink: 0;
}

.channel-logo img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    background: white;
    border-radius: 10px;
    padding: 10px;
}

.channel-details h1 {
    font-size: 2.5em;
    margin: 0 0 15px 0;
    color: white;
}

.channel-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.channel-category,
.channel-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: 500;
}

.channel-category {
    background: rgba(255,255,255,0.2);
}

.channel-status.active {
    background: #27ae60;
}

.channel-status.inactive {
    background: #e74c3c;
}

.channel-status i {
    animation: pulse 1.5s infinite;
}

.play-channel-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.play-channel-btn:hover:not(.disabled) {
    background: #c0392b;
    transform: translateY(-2px);
}

.play-channel-btn.disabled {
    background: #95a5a6;
    cursor: not-allowed;
}

.live-tv-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.channel-description {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.channel-description h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
}

.related-channels {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.related-channels h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
}

.related-channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.related-channel-card {
    text-align: center;
}

.related-channel-card .channel-thumbnail {
    position: relative;
    height: 100px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.related-channel-card img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.related-channel-card .channel-placeholder {
    font-size: 2em;
    color: #dee2e6;
}

.related-channel-card .channel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-channel-card:hover .channel-overlay {
    opacity: 1;
}

.related-channel-card .play-icon {
    background: #e74c3c;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.related-channel-card .channel-name {
    margin: 0;
    font-size: 0.9em;
    color: #333;
}

.related-channel-card .channel-link {
    text-decoration: none;
    color: inherit;
}

.sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.channel-actions,
.channel-stats {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.channel-actions h4,
.channel-stats h4 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 10px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-btn:hover {
    background: #e74c3c;
    border-color: #e74c3c;
    color: white;
    text-decoration: none;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.stat-label {
    font-weight: 500;
    color: #666;
}

.stat-value {
    font-weight: 600;
    color: #333;
}

.stat-value.active {
    color: #27ae60;
}

.stat-value.inactive {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .channel-info-header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .live-tv-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .channel-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .related-channels-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
}
</style>

<script>
// Play channel functionality
document.addEventListener('DOMContentLoaded', function() {
    const playBtn = document.querySelector('.play-channel-btn');
    if (playBtn && !playBtn.classList.contains('disabled')) {
        playBtn.addEventListener('click', function() {
            const streamUrl = this.dataset.stream;
            const channelTitle = document.querySelector('.channel-title').textContent;
            
            if (typeof playChannel === 'function') {
                playChannel(streamUrl, channelTitle);
            }
        });
    }
});

// Share channel function
function shareChannel() {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('<?php _e("Channel link copied to clipboard!", "dooplay"); ?>');
        });
    }
}

// Fullscreen player function
function openFullscreenPlayer() {
    const playBtn = document.querySelector('.play-channel-btn');
    if (playBtn && !playBtn.classList.contains('disabled')) {
        playBtn.click();
        setTimeout(function() {
            const fullscreenBtn = document.getElementById('player-fullscreen');
            if (fullscreenBtn) {
                fullscreenBtn.click();
            }
        }, 1000);
    }
}
</script>

<?php endwhile; endif; ?>

<?php get_footer(); ?>
