<?php
/*
Template Name: Account Page
Description: Custom account page template for ARMember integration
*/

// Redirect if user is not logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url('/login'));
    exit;
}

get_header(); 

// Get current user info
$current_user = wp_get_current_user();
$user_id = $current_user->ID;

// Check if user is premium (you can customize this based on your ARMember setup)
$is_premium = false;
if (function_exists('arm_is_member_active')) {
    $is_premium = arm_is_member_active($user_id);
}
?>

<style>
/* Custom Account Page Styles */
.account-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 40px 0;
}

.account-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.account-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.account-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="white" opacity="0.1"><circle cx="80" cy="20" r="15"/><circle cx="20" cy="80" r="10"/></svg>');
    background-size: cover;
}

.account-header-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 30px;
    align-items: center;
}

.account-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    border: 3px solid rgba(255,255,255,0.3);
}

.account-info h1 {
    font-size: 2rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.account-info p {
    opacity: 0.9;
    margin: 0;
}

.account-status {
    text-align: right;
}

.status-badge {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.status-badge.free {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.account-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
}

.account-sidebar {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    height: fit-content;
}

.account-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.account-nav li {
    margin-bottom: 10px;
}

.account-nav a {
    display: block;
    padding: 15px 20px;
    color: #666;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.account-nav a:hover,
.account-nav a.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateX(5px);
}

.account-nav a i {
    margin-right: 10px;
    width: 20px;
}

.account-main {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.account-section {
    display: none;
}

.account-section.active {
    display: block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    border: 1px solid #e1e5e9;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.membership-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.membership-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.membership-info {
    position: relative;
    z-index: 2;
}

.membership-type {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.membership-details {
    opacity: 0.9;
    margin-bottom: 20px;
}

.upgrade-btn {
    background: white;
    color: #667eea;
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.upgrade-btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #667eea;
    text-decoration: none;
}

@media (max-width: 768px) {
    .account-content {
        grid-template-columns: 1fr;
    }
    
    .account-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .account-status {
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
    }
}
</style>

<div class="account-page">
    <div class="account-container">
        <div class="account-header">
            <div class="account-header-content">
                <div class="account-avatar">
                    <?php echo strtoupper(substr($current_user->display_name, 0, 1)); ?>
                </div>
                <div class="account-info">
                    <h1>Welcome, <?php echo esc_html($current_user->display_name); ?>!</h1>
                    <p><?php echo esc_html($current_user->user_email); ?></p>
                </div>
                <div class="account-status">
                    <?php if ($is_premium): ?>
                        <span class="status-badge premium">
                            <i class="fas fa-crown"></i> Premium Member
                        </span>
                    <?php else: ?>
                        <span class="status-badge free">
                            <i class="fas fa-user"></i> Free Member
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="account-content">
            <div class="account-sidebar">
                <ul class="account-nav">
                    <li><a href="#dashboard" class="nav-link active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="#profile" class="nav-link"><i class="fas fa-user"></i> Profile</a></li>
                    <li><a href="#membership" class="nav-link"><i class="fas fa-crown"></i> Membership</a></li>
                    <li><a href="#downloads" class="nav-link"><i class="fas fa-download"></i> Downloads</a></li>
                    <li><a href="#settings" class="nav-link"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="<?php echo wp_logout_url(home_url()); ?>" class="nav-link"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <div class="account-main">
                <!-- Dashboard Section -->
                <div id="dashboard" class="account-section active">
                    <h2 class="section-title">Dashboard</h2>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">156</div>
                            <div class="stat-label">Movies Watched</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">42</div>
                            <div class="stat-label">Downloads</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8.5</div>
                            <div class="stat-label">Hours This Week</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">23</div>
                            <div class="stat-label">Favorites</div>
                        </div>
                    </div>
                    
                    <div class="membership-card">
                        <div class="membership-info">
                            <?php if ($is_premium): ?>
                                <div class="membership-type">Premium Membership</div>
                                <div class="membership-details">
                                    Enjoy unlimited access to all content, ad-free experience, and direct downloads.
                                </div>
                                <a href="#membership" class="upgrade-btn nav-link">Manage Subscription</a>
                            <?php else: ?>
                                <div class="membership-type">Free Membership</div>
                                <div class="membership-details">
                                    Upgrade to Premium for unlimited access, no ads, and instant downloads.
                                </div>
                                <a href="<?php echo home_url('/membership'); ?>" class="upgrade-btn">Upgrade to Premium</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Section -->
                <div id="profile" class="account-section">
                    <h2 class="section-title">Profile Information</h2>
                    <?php
                    // Display ARMember profile form
                    if (function_exists('arm_get_form_by_id')) {
                        echo do_shortcode('[arm_form id="103"]'); // Replace with your profile form ID
                    } else {
                        echo '<p>Profile editing form will appear here when ARMember is properly configured.</p>';
                    }
                    ?>
                </div>
                
                <!-- Membership Section -->
                <div id="membership" class="account-section">
                    <h2 class="section-title">Membership Details</h2>
                    <?php
                    // Display ARMember account details
                    if (function_exists('arm_get_form_by_id')) {
                        echo do_shortcode('[arm_account_detail]');
                    } else {
                        echo '<p>Membership details will appear here when ARMember is properly configured.</p>';
                    }
                    ?>
                </div>
                
                <!-- Downloads Section -->
                <div id="downloads" class="account-section">
                    <h2 class="section-title">Download History</h2>
                    <p>Your recent downloads and download history will appear here.</p>
                </div>
                
                <!-- Settings Section -->
                <div id="settings" class="account-section">
                    <h2 class="section-title">Account Settings</h2>
                    <p>Account settings and preferences will appear here.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Navigation functionality
    $('.nav-link').on('click', function(e) {
        var target = $(this).attr('href');
        
        // Skip if it's a logout link or external link
        if (target.indexOf('wp_logout') !== -1 || target.indexOf('http') === 0) {
            return;
        }
        
        e.preventDefault();
        
        // Remove active class from all nav links and sections
        $('.nav-link').removeClass('active');
        $('.account-section').removeClass('active');
        
        // Add active class to clicked nav link
        $(this).addClass('active');
        
        // Show corresponding section
        $(target).addClass('active');
    });
});
</script>

<?php get_footer(); ?>
