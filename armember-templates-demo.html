<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARMember Templates Demo - DeshiFlix</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .demo-header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 20px;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }
        
        .template-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .template-preview {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .template-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="white" opacity="0.1"><circle cx="20" cy="20" r="10"/><circle cx="80" cy="80" r="15"/></svg>');
            background-size: cover;
        }
        
        .template-preview i {
            position: relative;
            z-index: 2;
        }
        
        .template-info {
            padding: 30px;
        }
        
        .template-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }
        
        .template-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .template-features {
            list-style: none;
            margin-bottom: 25px;
        }
        
        .template-features li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .template-features li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .template-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            width: 100%;
            text-align: center;
        }
        
        .template-button:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .setup-guide {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .setup-guide h2 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .setup-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .setup-step {
            text-align: center;
            padding: 20px;
        }
        
        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        
        .step-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .step-description {
            color: #666;
            line-height: 1.6;
        }
        
        .features-showcase {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .features-showcase h2 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            flex-shrink: 0;
        }
        
        .feature-content h3 {
            color: #333;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .feature-content p {
            color: #666;
            line-height: 1.6;
        }
        
        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-header p {
                font-size: 1.1rem;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .setup-steps {
                grid-template-columns: 1fr;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>🎬 ARMember Templates for DeshiFlix</h1>
        <p>Beautiful, responsive page templates designed specifically for your movie streaming site</p>
    </div>
    
    <div class="demo-container">
        <div class="templates-grid">
            <!-- Membership Page -->
            <div class="template-card">
                <div class="template-preview">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="template-info">
                    <h3 class="template-title">Membership Page</h3>
                    <p class="template-description">Showcase your premium plans with beautiful pricing cards and feature comparisons.</p>
                    <ul class="template-features">
                        <li>3-tier pricing display</li>
                        <li>Feature comparison table</li>
                        <li>Animated hover effects</li>
                        <li>Mobile responsive design</li>
                    </ul>
                    <a href="#" class="template-button">View Template</a>
                </div>
            </div>
            
            <!-- Login Page -->
            <div class="template-card">
                <div class="template-preview">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <div class="template-info">
                    <h3 class="template-title">Login Page</h3>
                    <p class="template-description">Elegant split-screen login design with branding and feature highlights.</p>
                    <ul class="template-features">
                        <li>Split-screen layout</li>
                        <li>ARMember form integration</li>
                        <li>Loading animations</li>
                        <li>Forgot password links</li>
                    </ul>
                    <a href="#" class="template-button">View Template</a>
                </div>
            </div>
            
            <!-- Registration Page -->
            <div class="template-card">
                <div class="template-preview">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="template-info">
                    <h3 class="template-title">Registration Page</h3>
                    <p class="template-description">User-friendly signup form with plan selection and benefits showcase.</p>
                    <ul class="template-features">
                        <li>Plan selection interface</li>
                        <li>Benefits highlighting</li>
                        <li>Form validation</li>
                        <li>Social proof elements</li>
                    </ul>
                    <a href="#" class="template-button">View Template</a>
                </div>
            </div>
            
            <!-- Account Page -->
            <div class="template-card">
                <div class="template-preview">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="template-info">
                    <h3 class="template-title">Account Dashboard</h3>
                    <p class="template-description">Complete user dashboard with statistics, profile management, and settings.</p>
                    <ul class="template-features">
                        <li>Usage statistics</li>
                        <li>Sidebar navigation</li>
                        <li>Premium status display</li>
                        <li>Profile management</li>
                    </ul>
                    <a href="#" class="template-button">View Template</a>
                </div>
            </div>
        </div>
        
        <div class="setup-guide">
            <h2>Quick Setup Guide</h2>
            <div class="setup-steps">
                <div class="setup-step">
                    <div class="step-number">1</div>
                    <h3 class="step-title">Upload Templates</h3>
                    <p class="step-description">Copy the template files to your theme directory and activate them.</p>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">2</div>
                    <h3 class="step-title">Create Pages</h3>
                    <p class="step-description">Create new pages in WordPress and assign the respective templates.</p>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">3</div>
                    <h3 class="step-title">Configure ARMember</h3>
                    <p class="step-description">Set up your membership plans and forms in the ARMember plugin.</p>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">4</div>
                    <h3 class="step-title">Customize & Launch</h3>
                    <p class="step-description">Update form IDs, customize colors, and launch your membership site.</p>
                </div>
            </div>
        </div>
        
        <div class="features-showcase">
            <h2>Key Features</h2>
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Mobile Responsive</h3>
                        <p>All templates are fully responsive and optimized for mobile devices with touch-friendly interfaces.</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Custom Styling</h3>
                        <p>Beautiful custom CSS that overrides default ARMember styling for a cohesive brand experience.</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Performance Optimized</h3>
                        <p>Lightweight code with conditional loading and optimized animations for fast page loads.</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Security Features</h3>
                        <p>Built-in security measures including nonce verification and proper user authentication checks.</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Easy Integration</h3>
                        <p>Seamless integration with ARMember plugin and DeshiFlix theme with minimal configuration required.</p>
                    </div>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Conversion Optimized</h3>
                        <p>Designed with conversion in mind, featuring clear CTAs and persuasive design elements.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
