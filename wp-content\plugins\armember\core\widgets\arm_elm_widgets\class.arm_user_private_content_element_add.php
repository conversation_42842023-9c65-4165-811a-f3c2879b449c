<?php
namespace ElementorARMELEMENT\Widgets;

use <PERSON>ementor\Widget_Base;
use Elementor\Controls_Manager;
use <PERSON>ementor\Controls_Stack;

if(! defined('ABSPATH')) exit;

class arm_user_private_content_element_shortcode extends Widget_Base
{
	public function get_categories() {
		return array( 'armember' );
	}
	
    public function get_name()
    {
        return 'arm-user-private-content-shortcode';
    }

    public function get_title()
    {
        return esc_html('ARMember User Private Content','ARMemeber').'<style>
        .arm_element_icon{
			display: inline-block;
		    width: 28px;
		    height: 28px;
		    background-image: url('.MEMBERSHIP_IMAGES_URL.'/armember_icon.png);
		    background-repeat: no-repeat;
		    background-position: bottom;
			border-radius: 5px;
		}
        </style>';
    }
    public function get_icon() {
		return 'arm_element_icon';
	}

    public function get_script_depends() {
		return [ 'elementor-arm-element' ];
	}
    protected function register_controls()
    {
        global $ARMember,$wp,$wpdb,$armainhelper,$arm_member_forms,$arm_subscription_plans;
		
        /**START Fetch all shortcode controls from DB */
        /*END*/
        $this->start_controls_section(
			'section_content',
			[
				'label' => esc_html__( 'ARMember User Private Content', 'ARMember' ),
			]
		);

		$this->add_control(
			'title',
			[
				'label' => esc_html__( 'Title', 'ARMember' ),
				'type' => Controls_Manager::TEXT,
				'label_block' => true,
                'default'=>'User private Content',
				'label_block' => true,
			]
		);
		$this->end_controls_section();
    }

	protected function render()
	{
		global $wp, $wpdb, $current_user, $arm_errors, $ARMember, $arm_global_settings, $arm_subscription_plans, $arm_member_forms, $arm_members_activity,$is_globalcss_added,$arm_social_feature,$arm_members_directory;
		$settings = $this->get_settings_for_display();

		echo '<h5 class="title">';
		echo $settings['title']; //phpcs:ignore
		echo '</h5>';
		echo '<div class="arm_select">';					
		echo do_shortcode('[arm_user_private_content]');
		echo '</div>';
	}
}
