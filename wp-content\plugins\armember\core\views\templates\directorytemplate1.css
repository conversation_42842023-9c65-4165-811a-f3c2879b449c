@charset "utf-8";
.arm_template_wrapper.arm_template_wrapper_directorytemplate1{
    float:none;
    width:100%;
    max-width:1100px;
    border: 1px solid #e0e0e0;
    padding: 30px;
    border-radius: 6px;
    -webkit-border-radius:6px;
    -o-border-radius:6px;
    -moz-border-radius:6px;
    margin:0 auto;
    display:block;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_filters_wrapper{
    float:left;
    width: 100%;
    margin-bottom: 50px;
    padding: 0 15px 0 15px;
}

.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper{
    float:left;
    min-width: 35%;
    width: auto;
    margin-right: 8px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper .arm_directory_search_box, .arm_search_filter_field_item_top input[type="text"],.arm_search_filter_field_item_top input[type="email"]{
    float:left;
    max-width: 100%;
    width: 100%;
    height: 32px;
    border: 1px #e0e0e0 solid;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_btn{
    float:left;
    background:#ececec;
    border:1px solid #e0e0e0;
    border-left:none;
    color:#000000;
    font-size:12px;
    position: relative;
    height: 38px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_btn{
    width: 26px;
    height:30px;
    padding: 4px 7px 4px 9px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_clear_wrapper
{
    float: left;
    padding: 3px 0;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_template_container{
    display: inline-block;
}
.arm_template_wrapper_directorytemplate1 .arm_user_block{
    width: 25%;
    min-width: 175px;
    display: inline-block;
    margin: 0;
    padding: 5px 20px;
    vertical-align: top;
    position: relative;
    border-left:1px solid #e2e9ed;
}
.arm_template_wrapper_directorytemplate1 .arm_user_block.arm_3_column{ width: 33%; }
.arm_template_wrapper_directorytemplate1 .arm_user_block.arm_2_column{ width: 50%; }
.arm_template_wrapper_directorytemplate1 .arm_user_block.arm_1_column{ width: 100%; }
.arm_template_wrapper_directorytemplate1 .arm_user_block:first-child{
    border-left: 0;
}
.arm_template_wrapper_directorytemplate1 .arm_directorytemplate1_seperator + .arm_user_block{
    border-left: 0;
}
.arm_template_wrapper_directorytemplate1 .arm_directorytemplate1_seperator{
    float:none;
    display: block;
    margin:0 auto !important;
    width: 98% !important;
    height:1px !important;
    padding:0 !important;
    border-top:1px solid #e2e9ed;
}
.arm_template_wrapper_directorytemplate1 .arm_user_block{}

.arm_template_wrapper_directorytemplate1 .arm_user_block.arm_user_block_with_follow{
    padding: 15px 5px 30px;
}
.arm_search_filter_container_type_1 #arm_loader_img
{
    display: none;
    position: inherit;
    width: 28px;
    height: 28px;
    margin-left: 10px;
    top: 3px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_avatar{
    display: block;
    max-width: 75%;
    width: 84px;
    height: 84px;
    margin: 10px auto 10px auto;
    vertical-align: middle;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    -o-border-radius:20px;
    border-radius: 20px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_avatar:before{
    content: '';
    position: absolute;
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    -o-border-radius:20px;
    border-radius: 20px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_block:hover .arm_user_avatar:before{
    -webkit-animation-name: hvr-ripple-out;
    animation-name: hvr-ripple-out;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-iteration-count: 1;
}

.arm_template_wrapper_directorytemplate1 .arm_dp_user_link{display: block;border: 0;}
.arm_template_wrapper_directorytemplate1 .arm_dp_user_link:hover,
.arm_template_wrapper_directorytemplate1 .arm_user_link:hover,
.arm_template_wrapper_directorytemplate1 .arm_view_profile_btn_wrapper .arm_view_profile_user_link:hover,
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields a:hover
{
    box-shadow: none;
}
.arm_template_wrapper_directorytemplate1 .arm_user_avatar img {
    width: 100%;
    height: 100%;
    -moz-border-radius: 15px;
    -webkit-border-radius: 15px;
    -o-border-radius:15px;
    border-radius: 15px;
    border: 0 !important;
    padding: 0px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_link{
    display: block;
    font-size: 17px;
    font-weight: bold;
    text-align: center;
    color: #565765;
    margin: 5px 0 8px 0;
    text-transform: capitalize;
    float:left;
    width:100%;
    line-break: anywhere;
}
.arm_template_wrapper_directorytemplate1 .arm_member_since_detail_wrapper {
    width: 100%;
    float: left;
    margin-bottom: 10px;
}
.arm_template_wrapper_directorytemplate1 ul.arm_memeber_field_wrapper li {
    margin-bottom: 15px;
}
.arm_template_wrapper_directorytemplate1 .arm_member_since_detail_wrapper span {
    text-align: center;
    display: block;
}
.arm_template_wrapper_directorytemplate1 .arm_badges_detail{
    float:left;
    width:100%;
    text-align: center;
    margin-bottom: 10px;
}
.arm_template_wrapper_directorytemplate1 .arm_badges_detail .arm-user-badge{
    float:none;
    display: inline-block;
    width:30px !important;
    height:30px !important;
    margin: 0 5px 5px 0;
}
.arm_template_wrapper_directorytemplate1 .arm_badges_detail .arm-user-badge img{
    width:100% !important;
    height:100% !important;
    box-shadow: none;
    border: none;
    padding: 0px;
}
.arm_template_wrapper_directorytemplate1 .arm_directory_container ul { margin-bottom: 10px; }

.arm_template_wrapper_directorytemplate1 .arm_template_container.arm_directory_container .arm_user_block .arm_view_profile_btn_wrapper a { 
    margin-bottom: 10px;
    border: 1px solid #C6C9DF;
    box-sizing: border-box;
    border-radius: 6px;
}

.arm_template_wrapper_directorytemplate1 .arm_last_active_text{
    float:left;
    width:100%;
    text-align:center;
    margin-bottom:20px;
}

.arm_template_wrapper_directorytemplate1 .arm_view_profile_btn_wrapper{
    float:left;
    width:100%;
    text-align: center;
    margin-top: 15px;
}
.arm_template_wrapper_directorytemplate1 .arm_view_profile_btn_wrapper .arm_view_profile_user_link,
.arm_template_wrapper_directorytemplate1 .arm_directory_paging_container .arm_directory_load_more_link{
    float:none;
    display:inline-block;
    font-size: 14px;
    border: 1px solid #CED4DE;
    padding-top: 0px;
    height: 40px;
    padding-left: 32px;
    padding-right:32px;
    margin:0 auto 20px;
    border-radius: 4px;
    -webkit-border-radius:4px;
    -o-border-radius:4px;
    -moz-border-radius:4px;
    width:auto;
    cursor: pointer;
    line-height:40px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields{
    float:left;
    width:26px;
    height:26px;
    margin-right:5px;
    margin-top:8px;
    border-radius:50%;
    -webkit-border-radius:50%;
    -o-border-radius:50%;
    -moz-border-radius:50%;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks{
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields{
    float:none;
    width:22px;
    height:22px;
    background: #cccccc;
    margin-right: 10px;
    margin-bottom: 10px;
    -webkit-border-radius:50%;
    -o-border-radius:50%;
    -moz-border-radius:50%;
    display: inline-block;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_prof_div > a {
    background-position: 15px center;
    border-radius: 30px;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    height: 25px;
    line-height: normal;
    margin: 5px 9px 5px 0;
    min-height: 25px;
    min-width: 25px;
    padding: 0px;
    position: relative;
    text-align: center;
    text-transform: lowercase !important;
    vertical-align: middle;
    width: 25px;
    text-align: center;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields > a{
    color:#ffffff;
    padding-right:2px;
    display: inline-block;
    border-radius:50%;
    width:100%;
    height:100%;
    margin-top: -1px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields > a::before {
    position:relative;
    top:0px;
    left: 0px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_facebook > a{
    background-color: #3b5998;
    border: 2px solid #3b5998;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_facebook > a:hover{
    background-color: #ffffff;
    border: 2px solid #3b5998;
    color: #3b5998;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_twitter > a{
     background-color: #00abf0;
    border: 2px solid #00abf0;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_twitter > a:hover{
    background-color: #ffffff;
    border: 2px solid #00abf0;
    color: #00abf0;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_linkedin > a{
    background-color: #0177b5;
    border: 2px solid #0177b5;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_linkedin > a:hover{
     background-color: #ffffff;
    border: 2px solid #0177b5;
    color: #0177b5;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_googleplush > a{
    background-color: #e94738;
    border: 2px solid #e94738;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_googleplush > a:hover{
     background-color: #ffffff;
    border: 2px solid #e94738;
    color: #e94738;

}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_pinterest > a{
    background-color: #ca2026;
    border: 2px solid #ca2026;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_pinterest > a:hover{
     background-color: #ffffff;
    border: 2px solid #ca2026;
    color: #ca2026;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_youtube > a{
    background-color: #E32C28;
    border: 2px solid #E32C28;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_youtube > a:hover{
    background-color: #ffffff;
    border: 2px solid #E32C28;
    color: #E32C28;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_reddit > a{
    background-color: #ff4500;
    border: 2px solid #ff4500;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_reddit > a:hover{
    background-color: #ffffff;
    border: 2px solid #ff4500;
    color: #ff4500;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_delicious > a{
    background-color: #2a96ff;
    border: 2px solid #2a96ff;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_delicious > a:hover{
     background-color: #ffffff;
    border: 2px solid #2a96ff;
    color: #2a96ff;

}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_tumblr > a {
     background-color: #36465d;
    border: 2px solid #36465d;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_tumblr > a:hover{
     background-color: #ffffff;
    border: 2px solid #36465d;
    color: #36465d;

}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_vk > a{
    background-color: #324f77;
    border: 2px solid #324f77;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_vk > a:hover{
    background-color: #ffffff;
    border: 2px solid #324f77;
    color: #324f77;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_instagram > a{
    background-color: #2a5b83;
    border: 2px solid #2a5b83;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_instagram > a:hover{
    background-color: #ffffff;
    border: 2px solid #2a5b83;
    color: #2a5b83;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_dribbble > a{
    background-color: #ea4c89;
    border: 2px solid #ea4c89;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_dribbble > a:hover{
    background-color: #ffffff;
    border: 2px solid #ea4c89;
    color: #ea4c89;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_vine > a{
    background-color: #1cce94;
    border: 2px solid #1cce94;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_vine > a:hover{
    background-color: #ffffff;
    border: 2px solid #1cce94;
    color: #1cce94;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_skype > a{
     background-color: #00aff0;
    border: 2px solid #00aff0;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_skype > a:hover{
    background-color: #ffffff;
    border: 2px solid #00aff0;
    color: #00aff0;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_whatsapp > a{
     background-color: #00e676;
    border: 2px solid #00e676;

}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_whatsapp > a:hover{
    background-color: #ffffff;
    border: 2px solid #00e676;
    color: #00e676;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_tiktok > a{
    background-color: #010101;
    border: 2px solid #010101;
}
.arm_template_wrapper_directorytemplate1 .arm_social_prof_div.arm_social_field_tiktok > a:hover{
    background-color: #ffffff;
    border: 2px solid #010101;
    color: #010101;
}
.arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_social_field_tiktok > a:before{
    margin-top: 3px;
    margin-left: 4px;
}

.arm_template_wrapper_directorytemplate1 .arm_user_btns{
    text-align: center;
    margin: 10px auto;
    display: block;
    width: 100%;
    min-height: 35px;
}
.arm_template_wrapper_directorytemplate1 .arm_user_block.arm_user_block_with_follow .arm_user_btns{
    margin: 0 auto 10px;
    position: absolute;
    left: 0;
    bottom: 0;
}
.arm_template_wrapper_directorytemplate1 a.disabled{cursor: not-allowed;}
.arm_template_wrapper_directorytemplate1 .arm_user_badges_detail {
    text-align: center;
    display: inline-block;
}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_search_wrapper{float: right;right: 0;direction:rtl;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_list_of_filters{right: 0;direction:rtl;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_list_by_filters {direction: ltr;float: left;left: 0;}

.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_user_block_right {direction: rtl;right: 0; text-align:right; float:right;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_user_block_left {float: right;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_empty_list {text-align: right;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_listby_select {direction: rtl;right: 0;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_search_wrapper .arm_directory_search_btn{ border-radius: 3px 0 0 3px; float: right !important;}
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directory_search_wrapper .arm_directory_search_box{float: right !important;border-radius: 0px 3px 3px 0px; }

.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_user_block { border-left: none; border-right: 1px solid #e2e9ed; }
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_user_block:first-child { border-left: none; border-right: 0; }
.arm_template_wrapper_directorytemplate1 .arm_directory_form_rtl .arm_directorytemplate1_seperator + .arm_user_block{ border-right: 0; }

.arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper, .arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper .arm_display_member_profile 
{
    width: 100%;
}
.arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper .arm_member_field_label
{
    text-align: left; 
    width: 100%;
    display: inline-block;
    vertical-align: middle;
    word-break: break-all;
    word-break: break-word;
    line-height: 22px;
}
.arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper .arm_member_field_value
{
    text-align: left; 
    width: 100%;
    vertical-align: middle;
    display: inline-block;
    word-break: break-all;
    word-break: break-word;
    line-height: 22px;
    font-weight: bold !important;
}
.arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper li:last-child
{
    border: none;
}
.arm_template_wrapper_directorytemplate1 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper .arm_member_field_value .arm_old_uploaded_file
{
    float: none;
    margin: 10px 10px;
}

.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper {
    width: 100%;
    display: flex;
    max-width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
    width: 52%;
    margin-right: 2%;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_button_search_filter_btn_div .arm_directory_search_btn {
    margin-left: 0;
    border-radius: 5px;
    line-height: initial;
    padding: 0px 30px !important;
    height: 38px;
    margin-right: 15px;
    text-transform: none;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper .arm_button_search_filter_btn_div_top {
    display: flex;
    margin-left: 10px;
    margin-right: -7px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_button_search_filter_btn_div .arm_directory_clear_btn {
    padding: 0px 30px!important;
}
@media (max-width: 1024px){
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
        width: 31%;
        margin-right: 2%;
    }
}
@media (max-width: 980px){
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_filters_wrapper
    {
        padding: 0 0px 0 15px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper
    {
        margin-right: 0px;
        margin-bottom: 15px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper .arm_directory_search_box
    {
        max-width: 100%;
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_field_list_filter
    {
        width: 45%;
        margin-right: 5%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_field_list_filter select
    {
        width: 100%;
        float: left;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_list_by_filters{width: 50%;}
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_list_by_filters select
    {
        width: 90%; 
        float: left;
    }
}
@media (max-width:768px){
	.arm_template_wrapper_directorytemplate1 .arm_user_block{
        width:33% !important;
        margin:0 auto !important;
        float:none !important;
        display: inline-block !important;
    }
    .arm_template_wrapper_directorytemplate1 .arm_directorytemplate1_seperator{
    	margin-bottom:0 !important;
    	width:100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_field_list_filter {
        width: 45%;
        margin-right: 5%;
    }
    .arm_search_filter_container_type_0 .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper
    {
        width: 50%;
        min-width: 50%;
    }
    .arm_search_filter_container_type_0 .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper
    {
        width: 50%;
        min-width: 50%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_filters_wrapper
    {
        padding: 0 0px 0 15px;
    }
    .arm_template_wrapper .arm_directory_list_by_filters select{ max-width: 100%; }
}
@media (max-width:600px){
    .arm_template_wrapper_directorytemplate1 .arm_directory_filters_wrapper{
        padding:0 !important;
        border-bottom:none !important;
    }
    .arm_template_wrapper_directorytemplate1 .arm_directorytemplate1_seperator{
        display: none !important;
    }
    .arm_template_wrapper_directorytemplate1 .arm_user_block{
        border:none !important;
        border-bottom:1px solid #DBE1E8 !important;
        margin-bottom:15px !important;
        float:none !important;
        margin:0 auto !important;
        display:block !important;
    }
    .arm_template_wrapper_directorytemplate1 .remove_bottom_border{
        border-bottom:none !important;
    }
    .arm_template_wrapper_directorytemplate1 .arm_directory_list_by_filters,
    .arm_template_wrapper_directorytemplate1 .arm_directory_field_list_filter{
		float:left;
		width: 100% !important;
		text-align: center;
        max-width: 100%;
        margin-bottom: 20px;
	}
    .arm_template_wrapper_directorytemplate1 .arm_directory_field_list_filter select,
    .arm_template_wrapper_directorytemplate1 .arm_directory_list_by_filters select{
        width: 100% !important;
        max-width: 100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_0 .arm_directory_search_wrapper
    {
        width: 80%;
        min-width: 80%;
        max-width: 80% !important;
        margin-left:0px;
        margin-right: 8px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_wrapper, .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_list_by_filters
    {
        width: 45%;
        min-width: 45%;
        max-width: 45% !important;
        margin-left:0px;
        margin-right: 8px;
    }
	.arm_template_wrapper_directorytemplate1 .arm_user_block{
		width:70% !important;
        min-width: 165px;
	}
    .arm_template_wrapper_directorytemplate1 .arm_user_social_blocks .arm_user_social_fields > a::before {
        top:5px;
    }
}
.arm_template_preview_popup.arm_mobile_wrapper .arm_template_wrapper_directorytemplate1 .remove_bottom_border_preview{
    border-bottom:none !important;
}
.arm_template_preview_popup.arm_mobile_wrapper .arm_template_wrapper_directorytemplate1 .arm_directorytemplate1_seperator{
    display:none !important;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_0 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box
{
    width: 80%;
}
@media( max-width: 768px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper input.arm_directory_search_box {
         max-width: 100% !important; 
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 #arm_loader_img
    {
        display: none;
        position: relative;
        width: 32px;
        height: 32px;
        margin-left: 10px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_user_top{
        margin-top: 35px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper
    {
        max-width: 100%;
        display: flex;
        width: 100%;
        flex-wrap: wrap;
        flex-direction: column !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper_top
    {
        display: block;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_search_wrapper .arm_directory_search_box, .arm_search_filter_field_item_top input[type="text"], .arm_search_filter_field_item_top input[type="email"]
    {
        max-width: 100%;
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top input{
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box
    {
        width: 100%;
    }
    
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters select
    {
        width: 100%;
        max-width: 100%;
        margin-top: 10px;
        margin-left: 0px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_wrapper
    {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_wrapper > .arm_button_search_filter_btn_div_top
    {
        align-self: center;
    }
}
@media(max-width: 961px) and (min-width: 769px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box
    {
        width:50%
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_directory_list_by_filters
    {
        min-width: 19%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box
    {
        width: 46%;
        margin-right: 20px;
    }
    .arm_template_wrapper_directorytemplate1 .arm_user_block {
        width: 33%;
        min-width: 175px;
        display: inline-block;
        margin: 0;
        padding: 5px;
        vertical-align: top;
        position: relative;
        border-left: 1px solid #e2e9ed;
    }
}
@media(max-width: 480px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 #arm_loader_img
    {
        display: none;
        position: relative !important;
        width: 32px;
        height: 32px;
    }
}
@media(max-width: 425px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top input
    {
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters select
    {
        width: 100% !important;
        max-width: 100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_wrapper > .arm_button_search_filter_btn_div_top {
        display: flex;
        align-self: center;
    }
}
@media (max-width: 500px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_search_wrapper,
    .arm_template_wrapper.arm_template_wrapper_directorytemplate1 .arm_search_filter_container_type_1 .arm_directory_list_by_filters{
        min-width: 45%;
        max-width: 100% !important;
        margin-left: 0px;
        margin-right: 8px;
    }
    .arm_template_wrapper_directorytemplate1 .arm_user_block.arm_directorytemplate1_last_field{
        border-bottom: 1px solid #e2e9ed !important;
    }    
}
