<?php
/*
Template Name: Registration Page
Description: Custom registration page template for ARMember integration
*/

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

get_header(); ?>

<style>
/* Custom Registration Page Styles */
.register-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40px 20px;
    position: relative;
    overflow: hidden;
}

.register-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.05"><polygon points="0,0 100,50 0,100"/><polygon points="200,0 300,50 200,100"/><polygon points="400,0 500,50 400,100"/></svg>');
    background-size: cover;
    animation: slide 30s linear infinite;
}

@keyframes slide {
    0% { transform: translateX(0); }
    100% { transform: translateX(-100px); }
}

.register-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    position: relative;
    z-index: 2;
}

.register-left {
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.register-right {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: relative;
}

.register-right::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="white" opacity="0.1"><circle cx="20" cy="20" r="10"/><circle cx="80" cy="80" r="15"/><circle cx="50" cy="50" r="8"/></svg>');
    background-size: cover;
}

.register-form-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
}

.register-form-subtitle {
    color: #666;
    text-align: center;
    margin-bottom: 40px;
    font-size: 1.1rem;
    line-height: 1.6;
}

.register-logo {
    font-size: 4rem;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.register-welcome {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.register-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.register-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 2;
}

.register-benefits li {
    padding: 15px 0;
    font-size: 1.1rem;
    opacity: 0.9;
    position: relative;
    padding-left: 40px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.register-benefits li:before {
    content: '🎬';
    position: absolute;
    left: 0;
    font-size: 1.5rem;
}

.register-benefits li:nth-child(2):before { content: '⚡'; }
.register-benefits li:nth-child(3):before { content: '🛡️'; }
.register-benefits li:nth-child(4):before { content: '👑'; }
.register-benefits li:nth-child(5):before { content: '📱'; }

/* Override ARMember form styles */
.arm_form_wrapper {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.arm_form_wrapper .arm_form {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.arm_form_wrapper .arm_form_input_container {
    margin-bottom: 25px !important;
}

.arm_form_wrapper .arm_form_input_container input[type="text"],
.arm_form_wrapper .arm_form_input_container input[type="email"],
.arm_form_wrapper .arm_form_input_container input[type="password"],
.arm_form_wrapper .arm_form_input_container select {
    width: 100% !important;
    padding: 15px 20px !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
}

.arm_form_wrapper .arm_form_input_container input:focus,
.arm_form_wrapper .arm_form_input_container select:focus {
    border-color: #667eea !important;
    background: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

.arm_form_wrapper .arm_form_input_container label {
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_input_box button,
.arm_form_wrapper .arm_form_input_container input[type="submit"] {
    width: 100% !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 18px 20px !important;
    border-radius: 10px !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-top: 20px !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_input_box button:hover,
.arm_form_wrapper .arm_form_input_container input[type="submit"]:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
}

.register-form-container {
    width: 100%;
}

.plan-selection {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid #e1e5e9;
}

.plan-selection h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.plan-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.plan-option {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.plan-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.plan-option.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

.plan-option .plan-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.plan-option .plan-price {
    color: #667eea;
    font-weight: 700;
    font-size: 1.2rem;
}

.login-prompt {
    text-align: center;
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #e1e5e9;
    color: #666;
}

.login-prompt a {
    color: #667eea;
    font-weight: 600;
    text-decoration: none;
}

.login-prompt a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .register-container {
        grid-template-columns: 1fr;
    }
    
    .register-left {
        padding: 40px 30px;
        order: 2;
    }
    
    .register-right {
        padding: 40px 30px;
        order: 1;
    }
    
    .register-form-title {
        font-size: 2rem;
    }
    
    .register-welcome {
        font-size: 2rem;
    }
    
    .register-logo {
        font-size: 3rem;
    }
    
    .plan-options {
        grid-template-columns: 1fr;
    }
}

/* Loading animation */
.register-loading {
    display: none;
    text-align: center;
    margin-top: 20px;
}

.register-loading.active {
    display: block;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<div class="register-page">
    <div class="register-container">
        <div class="register-left">
            <h1 class="register-form-title">Join DeshiFlix</h1>
            <p class="register-form-subtitle">Create your account and start enjoying unlimited entertainment today!</p>
            
            <div class="plan-selection">
                <h3>Choose Your Plan</h3>
                <div class="plan-options">
                    <div class="plan-option selected" data-plan="free">
                        <div class="plan-name">Free</div>
                        <div class="plan-price">$0/month</div>
                    </div>
                    <div class="plan-option" data-plan="premium">
                        <div class="plan-name">Premium</div>
                        <div class="plan-price">$9.99/month</div>
                    </div>
                </div>
            </div>
            
            <div class="register-form-container">
                <?php
                // Display ARMember registration form
                // You can replace with your actual ARMember registration form ID
                if (function_exists('arm_get_form_by_id')) {
                    echo do_shortcode('[arm_form id="102"]'); // Replace with your registration form ID
                } else {
                    // Fallback if ARMember is not active
                    ?>
                    <form method="post" action="<?php echo wp_registration_url(); ?>">
                        <div style="margin-bottom: 20px;">
                            <label for="user_login">Username</label>
                            <input type="text" name="user_login" id="user_login" required style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="user_email">Email</label>
                            <input type="email" name="user_email" id="user_email" required style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="user_pass">Password</label>
                            <input type="password" name="user_pass" id="user_pass" required style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem;">
                        </div>
                        
                        <input type="submit" value="Create Account" style="width: 100%; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 18px; border-radius: 10px; font-size: 1.2rem; font-weight: 600; cursor: pointer;">
                        
                        <?php wp_nonce_field('register'); ?>
                    </form>
                    <?php
                }
                ?>
            </div>
            
            <div class="login-prompt">
                Already have an account? <a href="<?php echo home_url('/login'); ?>">Sign in here</a>
            </div>
            
            <div class="register-loading">
                <div class="spinner"></div>
                <p>Creating your account...</p>
            </div>
        </div>
        
        <div class="register-right">
            <div class="register-logo">🎬</div>
            <h2 class="register-welcome">Start Your Journey</h2>
            <p class="register-subtitle">Join thousands of users who are already enjoying the best entertainment experience</p>
            
            <ul class="register-benefits">
                <li>Unlimited access to movies & TV shows</li>
                <li>Lightning-fast direct downloads</li>
                <li>Ad-free premium experience</li>
                <li>Exclusive premium content</li>
                <li>Multi-device streaming support</li>
            </ul>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Plan selection functionality
    $('.plan-option').on('click', function() {
        $('.plan-option').removeClass('selected');
        $(this).addClass('selected');
        
        var selectedPlan = $(this).data('plan');
        // You can add logic here to update the form based on selected plan
        console.log('Selected plan:', selectedPlan);
    });
    
    // Add loading animation on form submit
    $('.arm_form_wrapper form, form').on('submit', function() {
        $('.register-loading').addClass('active');
    });
    
    // Add focus effects to form inputs
    $('.arm_form_wrapper input, form input').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
});
</script>

<?php get_footer(); ?>
