function validate_all_fields(e,r){var a,t,e=jQuery(e).parents(".arm-wizard-setup-container").attr("id"),_=0;4==r?(t=jQuery("input#arm_membership_plan_amount").val(),a=jQuery('input[name="arm_subscription_plan_type"]').val(),new RegExp("^[0-9]+$"),"free"!=a&&""==t&&(jQuery("input#arm_membership_plan_amount").addClass("error"),a=jQuery("input#arm_membership_plan_amount").attr("id"),t=jQuery("input#arm_membership_plan_amount").attr("data-msg-required"),jQuery(document).find("span#"+a+"-error").html(t),_+=1),jQuery("#"+e+" input:required").each(function(){var e,r;""===jQuery(this).val()&&(jQuery(this).addClass("error"),e=jQuery(this).attr("id"),r=jQuery(this).attr("data-msg-required"),jQuery(document).find("span#"+e+"-error").html(r),_+=1)})):jQuery("#"+e+" input:required").each(function(){var e,r;""===jQuery(this).val()&&(jQuery(this).addClass("error"),e=jQuery(this).attr("id"),r=jQuery(this).attr("data-msg-required"),jQuery(document).find("span#"+e+"-error").html(r),_+=1)}),focus_id=jQuery("input.error:first").attr("id"),jQuery("#"+focus_id).focus(),0==_&&(jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+r+")").hide(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_"+r).show(),jQuery("#total_completed_page").val(r))}function armopensetupvideos(){var e,r,a=jQuery(window).width();a<="1350"&&(e="720",r="400",jQuery("#arm_document_setup_video_popup").css("width","760"),jQuery(".popup_content_text iframe").css("width",e),jQuery(".popup_content_text iframe").css("height",r)),"1350"<a&&a<="1600"&&(e="750",r="430",jQuery("#arm_document_setup_video_popup").css("width","790"),jQuery(".popup_content_text iframe").css("width",e),jQuery(".popup_content_text iframe").css("height",r)),"1600"<a&&(e="800",r="450",jQuery("#arm_document_setup_video_popup").css("width","840"),jQuery(".popup_content_text iframe").css("width",e),jQuery(".popup_content_text iframe").css("height",r));var t="<iframe src='"+jQuery("#arm_document_setup_video_popup").find("iframe").attr("src")+"' allowfullscreen='' frameborder='0'></iframe>";jQuery("#arm_document_setup_video_popup").bPopup({modalClose:!1,closeClass:"popup_close_btn",onClose:function(){jQuery(this).find(".popup_wrapper_inner .popup_content_text").html(t)}})}function armHideDocumentSetupVideo(){jQuery("#arm_document_setup_video_popup").bPopup().close()}jQuery(function(e){jQuery("body").addClass("arm_fullscreen_wizard_setup_container"),jQuery(".arm-wizard-setup-container:not(.arm-ws-is-landingpage)").hide(),jQuery("#arm_lic_activation-error").hide();jQuery("#total_completed_page").val();jQuery(document).on("click",".arm_next_wizard_step",function(){jQuery(this).parents(".arm-ws-is-landingpage").hide(),1!=jQuery('input[name="arm_package_actvted"]').val()?(jQuery("#total_completed_page").val(0),jQuery(document).find(".arm_license_activation").show()):(jQuery("#total_completed_page").val(1),jQuery(document).find(".arm_package_actvted").show()),jQuery(".arm_setup_skip_div").show()}),jQuery(".arm_setup_configuration_form").validate({ignore:"",errorClass:"error arm_invalid",validClass:"valid arm_valid",errorPlacement:function(e,r){e.appendTo(r.parents("td"))},focusInvalid:!1,invalidHandler:function(e,r){r.numberOfInvalids()&&jQuery("html, body").animate({scrollTop:jQuery(r.errorList[0].element).offset().top-150},0)},submitHandler:function(e){var e=jQuery(e);e.hasClass("arm_already_clicked")||(e.find("input[type=submit], button[type=submit]").addClass("arm_already_clicked").attr("disabled","disabled"),e=e.serialize(),jQuery(".arm_setup_skip_div").hide(),jQuery(".arm-ws-back-btn").hide(),jQuery("#arm_setup_completion_loader").show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_complete_setup_data&"+e,dataType:"json",success:function(e){"success"==e.type?(jQuery("#arm_setup_completion_loader").hide(),jQuery(".arm-wizard-setup-container:not(.arm-ws-is-landingpage)").hide(),jQuery(window).scrollTop(0),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_6").show(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_6 .arm-ws-steps-belt .arm-ws-step-box").addClass("arm-ws-step-box-disabled"),setTimeout(()=>{jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_6").find(".arm-setup-comp-video").removeClass("arm-setup-wizard-celebration-show")},3e3),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_6").find(".arm_setupform").html(e.setup_url),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_6").find(".arm_setup_form_copy_text").attr("data-code",e.setup_url)):armToast(e.msg,"error")}}))}})}),jQuery(document).on("click",".arm_submit_validate_license",function(){jQuery("#arm_lic_activation-error").hide();var e=jQuery(this).parents(".arm-wizard-setup-container").attr("id"),a=jQuery("#arm_lic_activation_name").val(),t=jQuery("#arm_lic_activation_email").val(),_=jQuery("#arm_lic_activation_key").val(),r=jQuery("#arm_domain_name").val(),i=0;jQuery("#"+e+" input:required").each(function(){var e,r;""===jQuery(this).val()&&(e=jQuery(this).attr("id"),r=jQuery(this).attr("data-msg-required"),jQuery(document).find("span#"+e+"-error").html(r),jQuery(this).addClass("error"))}),""!=t&&(e=jQuery("#arm_lic_activation_email").attr("data-validation-regex-regex"),i=(e=new RegExp(e)).test(t)?1:(jQuery("#arm_lic_activation_email").addClass("error"),message=jQuery("#arm_lic_activation_email").attr("data-validation-regex-message"),jQuery(document).find("span#arm_lic_activation_email-error").html(message),0)),focus_id=jQuery(".error:first").attr("id"),jQuery(".error:first").focus(),""!=a&&0!=i&&""!=_&&(jQuery("#license_loader").css("display","inline"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=armactivatelicense&cust_name="+a+"&cust_email="+t+"&license_key="+_+"&domain_name="+r+"&_wpnonce="+jQuery('input[name="arm_wp_nonce"]').val(),success:function(e){jQuery("#license_loader").css("display","none");var e=e.split("||"),r=e[0],e=e[1];"VERIFIED"==r?(jQuery('input[name="arm_package_actvted"]').val(1),jQuery(".arm_customer_name").html(a),jQuery(".arm_customer_email").html(t),jQuery(".arm_license_key").html(_),jQuery(".arm_license_activation").hide(),jQuery("#total_completed_page").val(1),jQuery(".arm_package_actvted").show()):("THIS PURCHASED CODE IS ALREADY USED FOR ANOTHER DOMAIN"==r.trim()&&(r="This purchase code is already activated at <strong>"+e+"</strong> . Please deactivate license from your currently activated domain by clicking <strong>Remove License</strong> button."),jQuery("#arm_lic_activation-error").html(r),jQuery("#arm_lic_activation-error").css("display",""))}}))}),jQuery(document).on("click",".arm_skip_license",function(){jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_2)").hide(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_2").show(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_1 input").removeAttr("required"),jQuery("#total_completed_page").val(2)}),jQuery(document).on("keypress",".arm_setup_configuration_form",function(e){if(13==(e.keyCode||e.which))return e.preventDefault(),!1}),jQuery(document).on("keyup",".arm_setup_configuration_form",function(e){13==(e.keyCode||e.which)&&(e.preventDefault(),jQuery(".arm_autocomplete").is(":focus")||jQuery(".arm-wizard-setup-container:visible>.arm-ws-footer-wrapper").find(".arm-ws-next-btn:not(.arm_submit_validate_license,.arm_complete_setup_step,.arm_check_is_activated)").click())}),jQuery(document).on("click",".arm-ws-next-btn:not(.arm_submit_validate_license,.arm_complete_setup_step,.arm_check_is_activated)",function(){var e=jQuery("#total_completed_page").val(),r=parseInt(e)+1;e<6&&jQuery(".arm_setup_skip_div").show(),1<e?(require_count=0,5==r&&(jQuery("#arm_setup_stripe_status").is(":checked")||jQuery("#arm_setup_paypal_status").is(":checked")||jQuery("#arm_setup_bank_status").is(":checked")||(jQuery("#arm_no_payment_gateway-error").css("display","block").delay(3e3).fadeOut(),require_count=1),e=jQuery('input[name="arm_membership_plan_name"]').val(),jQuery(".arm_plan_name_setup").html(e),jQuery(".arm-cont-access-plan-name").html(e),jQuery("#arm_setup_stripe_status").is(":checked")&&(jQuery(".arm_plan_name_setup").html(e),stripe_verified=jQuery("#arm_stripe_webhook_verified").val(),stripe_secret_key=jQuery("#arm_stripe_secret_key").val(),stripe_secret_key_msg=jQuery("#arm_stripe_secret_key").attr("required_msg"),stripe_publishable_key=jQuery("#arm_stripe_publishable_key").val(),stripe_publish_key_msg=jQuery("#arm_stripe_publishable_key").attr("required_msg"),""==stripe_verified?(require_count=1,jQuery("#arm_stripe_webhook_error_setup").removeClass("hidden_section")):(jQuery("#arm_stripe_webhook_error_setup").addClass("hidden_section"),jQuery("#arm_stripe_webhook_verify_setup").removeClass("hidden_section")),""==stripe_secret_key?(require_count=1,jQuery("#arm_stripe_secret_key").addClass("error"),jQuery("#arm_stripe_secret_key-error").html(stripe_secret_key_msg)):(jQuery("#arm_stripe_secret_key").removeClass("error"),jQuery("#arm_stripe_secret_key-error").html("")),""==stripe_publishable_key?(require_count=1,jQuery("#arm_stripe_publishable_key").addClass("error"),jQuery("#arm_stripe_publishable_key-error").html(stripe_publish_key_msg)):(jQuery("#arm_stripe_publishable_key").removeClass("error"),jQuery("#arm_stripe_publishable_key-error").html(""))),jQuery("#arm_setup_paypal_status").is(":checked")&&(paypal_api_email=jQuery("#arm_paypal_merchant_email").val(),paypal_api_username=jQuery("#arm_paypal_merchant_api_username").val(),paypal_api_password=jQuery("#arm_paypal_merchant_api_password").val(),paypal_api_signature=jQuery("#arm_paypal_merchant_api_signature").val(),paypal_api_email_msg=jQuery("#arm_paypal_merchant_email").attr("required_msg"),paypal_api_username_msg=jQuery("#arm_paypal_merchant_api_username").attr("required_msg"),paypal_api_password_msg=jQuery("#arm_paypal_merchant_api_password").attr("required_msg"),paypal_api_signature_msg=jQuery("#arm_paypal_merchant_api_signature").attr("required_msg"),""==paypal_api_email?(require_count=1,jQuery("#arm_paypal_merchant_email").addClass("error"),jQuery("#arm_paypal_merchant_email-error").html(paypal_api_email_msg)):(jQuery("#arm_paypal_merchant_email").removeClass("error"),jQuery("#arm_paypal_merchant_email-error").html("")),""==paypal_api_username?(require_count=1,jQuery("#arm_paypal_merchant_api_username").addClass("error"),jQuery("#arm_paypal_merchant_api_username-error").html(paypal_api_username_msg)):(jQuery("#arm_paypal_merchant_api_username").removeClass("error"),jQuery("#arm_paypal_merchant_api_username-error").html("")),""==paypal_api_password?(require_count=1,jQuery("#arm_paypal_merchant_api_password").addClass("error"),jQuery("#arm_paypal_merchant_api_password-error").html(paypal_api_password_msg)):(jQuery("#arm_paypal_merchant_api_password").removeClass("error"),jQuery("#arm_paypal_merchant_api_password-error").html("")),""==paypal_api_signature?(require_count=1,jQuery("#arm_paypal_merchant_api_signature").addClass("error"),jQuery("#arm_paypal_merchant_api_signature-error").html(paypal_api_signature_msg)):(jQuery("#arm_paypal_merchant_api_signature").removeClass("error"),jQuery("#arm_paypal_merchant_api_signature-error").html("")))),0==require_count&&validate_all_fields(this,r)):"0"==jQuery('input[name="arm_package_actvted"]').val()?(jQuery("#arm_lic_activation-error").html(arm_activatelicense_msg),jQuery("#arm_lic_activation-error").show()):(jQuery("#arm_lic_activation-error").hide(),jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+r+")").hide(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_"+r).show(),jQuery("#total_completed_page").val(r))}),jQuery(document).on("change",'input[name="arm_selected_payment_gateway[stripe][payment_method]"]',function(){var e=jQuery('input[name="arm_selected_payment_gateway[stripe][payment_method]"]:checked').val();console.log(e),"sandbox"==e?(jQuery(".arm-form-table-label.arm_stripe_sandbox_api").show(),jQuery(".arm-form-table-label.arm_stripe_live_api").hide()):(jQuery(".arm-form-table-label.arm_stripe_sandbox_api").hide(),jQuery(".arm-form-table-label.arm_stripe_live_api").show())}),jQuery(document).on("change",'input[name="arm_selected_payment_gateway[paypal][payment_method]"]',function(){"sandbox"==jQuery('input[name="arm_selected_payment_gateway[paypal][payment_method]"]:checked').val()?(jQuery(".arm-form-table-label.arm_paypal_sandbox_api").show(),jQuery(".arm-form-table-label.arm_paypal_live_api").hide()):(jQuery(".arm-form-table-label.arm_paypal_sandbox_api").hide(),jQuery(".arm-form-table-label.arm_paypal_live_api").show())}),jQuery(document).on("click",".arm-ws-step-complate:not(.arm-ws-step-box-disabled)",function(){var e=jQuery(this).attr("data-page_id");jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+e+")").hide(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_"+e).show(),jQuery("#total_completed_page").val(e)}),jQuery(document).on("click",".arm-ws-back-btn:not(.arm_stripe_verify_webhook,.arm_skip_license)",function(){var e=jQuery("#total_completed_page").val(),e=parseInt(e)-1,r=jQuery('input[name="arm_package_actvted"]').val();1<e?(jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+e+")").hide(),jQuery(".arm-wizard-setup-container.arm_setup_wizard_page_"+e).show(),jQuery("#total_completed_page").val(e)):1==e&&(1==r?(jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+e+" )").hide(),jQuery(".arm_package_actvted").show()):(jQuery(".arm-wizard-setup-container:not(.arm_setup_wizard_page_"+e+" )").hide(),jQuery(".arm_license_activation").show()),jQuery("#total_completed_page").val(e))}),jQuery(document).on("click",".arm-membership-plan",function(){jQuery(".arm-membership-plan:not(.arm-payment-opt)").removeClass("click"),jQuery('input[name="arm_membership_plan_amount"]').removeAttr("required"),jQuery(this).hasClass("arm_free_plan")?(jQuery('input[name="arm_subscription_plan_type"]').val("free"),jQuery(this).addClass("click"),jQuery(".arm_plan_amount_section").addClass("hidden_section")):jQuery(this).hasClass("arm_paid_infinite_plan")?(jQuery('input[name="arm_subscription_plan_type"]').val("paid_infinite"),jQuery(this).addClass("click"),jQuery(".arm_plan_amount_section").removeClass("hidden_section")):jQuery(this).hasClass("arm_paid_finite_plan")?(jQuery('input[name="arm_subscription_plan_type"]').val("paid_finite"),jQuery(this).addClass("click"),jQuery(".arm_plan_amount_section").removeClass("hidden_section")):jQuery(this).hasClass("arm_paid_subscription_plan")&&(jQuery('input[name="arm_subscription_plan_type"]').val("recurring"),jQuery(this).addClass("click"),jQuery(".arm_plan_amount_section").removeClass("hidden_section"))}),jQuery(document).on("change","#arm_setup_stripe_status",function(){jQuery(this).is(":checked")?jQuery(".arm_stripe_payment_section").slideDown():jQuery(".arm_stripe_payment_section").slideUp()}),jQuery(document).on("change","#arm_setup_paypal_status",function(){jQuery(this).is(":checked")?jQuery(".arm_paypal_payment_setup").slideDown():jQuery(".arm_paypal_payment_setup").slideUp()}),jQuery(document).on("change","#arm_setup_bank_status",function(){jQuery(this).is(":checked")?jQuery(".arm_bank_transfer_payment_setup").slideDown():jQuery(".arm_bank_transfer_payment_setup").slideUp()}),jQuery(document).on("click",".arm_stripe_verify_webhook",function(){var e=jQuery('input[name="arm_wp_nonce"]').val(),r=(jQuery("#arm_stripe_webhook_verify_setup").css("display","none"),jQuery("#arm_stripe_webhook_error").css("display","none"),jQuery("#arm_stripe_webhook_verified-error").css("display","none"),jQuery("#arm_stripe_secret_key").val()),a=jQuery("#arm_stripe_publishable_key").val(),t=jQuery("#arm_stripe_secret_key").attr("required_msg"),_=jQuery("#arm_stripe_publishable_key").attr("required_msg");return""!=r&&""!=a?(jQuery(".arm_loading").fadeIn("slow"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_verify_stripe_webhook_setup&secrate_key="+r+"&_wpnonce="+e,success:function(e){"success"==e.type?(jQuery(".arm_loading").fadeOut(),jQuery("#arm_stripe_webhook_verify_setup").removeClass("hidden_section"),jQuery("#arm_stripe_webhook_error_setup").addClass("hidden_section"),jQuery("#arm_stripe_webhook_verify_setup").html(e.message),jQuery("#arm_stripe_webhook_verify_setup").css("display","inline-block"),jQuery("#arm_stripe_webhook_verified").val(1)):(jQuery(".arm_loading").fadeOut(),jQuery("#arm_stripe_webhook_verify_setup").addClass("hidden_section"),jQuery("#arm_stripe_webhook_error_setup").removeClass("hidden_section"),jQuery("#arm_stripe_webhook_error_setup").html(e.message),jQuery("#arm_stripe_webhook_error_setup").css("display","inline-block"),jQuery("#arm_stripe_webhook_verified").val(""))}})):(""==r&&(jQuery("#arm_stripe_secret_key").addClass("error"),jQuery("#arm_stripe_secret_key-error").html(t)),""==a&&(jQuery("#arm_stripe_publishable_key").addClass("error"),jQuery("#arm_stripe_publishable_key-error").html(_))),!1}),jQuery(document).on("keyup","input.error",function(){focus_id=jQuery(this).attr("id"),jQuery(this).removeClass("error"),jQuery(document).find("span#"+focus_id+"-error").html("")}),jQuery(document).on("keyup","input#arm_lic_activation_email",function(){var e=jQuery(this).parents(".arm-wizard-setup-container").attr("id"),r=jQuery(this).val(),a=jQuery("#"+e+" input#arm_lic_activation_email").attr("data-validation-regex-regex");""!=r&&(new RegExp(a).test(r)?(jQuery(document).find("span#arm_lic_activation_email-error").html(""),jQuery(this).removeClass("error")):(a=jQuery("#"+e+" input#arm_lic_activation_email").attr("data-validation-regex-message"),jQuery(document).find("span#arm_lic_activation_email-error").html(a),jQuery(this).addClass("error")))}),jQuery(document).on("click",".arm-addon-btn:not(.arm_features_activate_btn,.activate-addon)",function(){var r=jQuery(this).attr("id"),e=jQuery('input[name="arm_package_actvted"]').val(),a=jQuery(this).attr("data-feature"),t=jQuery(this).attr("data-feature_val"),_=jQuery(this).parents(".arm-addon-box").find('input[name="arm_wp_nonce"]').val();1==e?(1==t&&jQuery("#"+r+"_loader").show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_update_feature_settings&arm_features_options="+a+"&arm_features_status="+t+"&_wpnonce="+_,success:function(e){"success"==e.type?1==t?(jQuery("#"+r+"_loader").hide(),jQuery("#"+r+".arm-addon-btn").addClass("hidden_section"),jQuery("#"+r+".activate-addon").removeClass("hidden_section")):(jQuery("#"+r+"uninstall_loader").hide(),jQuery("#"+r+".arm-addon-btn").removeClass("hidden_section"),jQuery("#"+r+".activate-addon").addClass("hidden_section")):((1==t?jQuery("#"+r+"_loader"):jQuery("#"+r+"uninstall_loader")).hide(),armToast(e.msg,"error"))}})):(jQuery(this).parents(".arm-addon-box").find("#arm_no_license-error").html(arm_nolicense_msg),setTimeout(()=>{jQuery(this).parents(".arm-addon-box").find("#arm_no_license-error").html("")},3e3))}),jQuery(document).on("click",".arm_features_activate_btn",function(){var r=jQuery(this).attr("id"),e=jQuery(this).parents(".arm-addon-box").find('input[name="arm_wp_nonce"]').val(),a=jQuery('input[name="arm_package_actvted"]').val(),t=jQuery(this).attr("data-version"),_=jQuery(this).attr("data-arm_version"),i=jQuery(this).attr("data-type");1==a?(_<t&&alert(ADDON_NOT_COMPATIBLE_MESSAGE),"free_addon"==i?(jQuery(this),a=jQuery(this).attr("data-plugin").split("/")[0],jQuery("#"+r+"_loader").show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_install_plugin&slug="+a+"&_wpnonce="+e,success:function(e){e.success?(jQuery("#"+r+"_loader").hide(),jQuery("#"+r).attr("data-type","activate_addon"),armToast(installAddonSuccess,"success")):(armToast(installAddonError,"error"),console.warn(e.data.errorMessage))}})):"activate_addon"==i&&(jQuery("#"+r+"_loader").show(),_=jQuery(this).attr("data-file"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_active_plugin&slug="+_+"&_wpnonce="+e,success:function(e){"success"==e.type?(jQuery("#"+r+"_loader").hide(),jQuery("#"+r+".arm-addon-btn").addClass("hidden_section"),jQuery("#"+r+".activate-addon").removeClass("hidden_section")):"error"==e.type?(jQuery("#"+r+"_loader").hide(),armToast(e.msg,"error",5e3)):(jQuery("#"+r+"_loader").hide(),armToast(activeAddonError,"error"))}}))):(jQuery(this).parents(".arm-addon-box").find("#arm_no_license-error").html(arm_nolicense_msg),setTimeout(()=>{jQuery(this).parents(".arm-addon-box").find("#arm_no_license-error").html("")},3e3))}),jQuery(document).on("click",".arm_setup_click_to_copy_text",function(){var e,r,a,t,_=jQuery(this).parents(".arm-short-code-wapper").attr("id");(jQuery(this).parents("form").hasClass("arm_admin_member_form")?(e=jQuery(this).attr("data-code"),jQuery(this).val(e),armCopyToClipboardForm(e)):(e=jQuery(this).attr("data-code"),armCopyToClipboard(e)))?jQuery("#"+_).find(".arm_copied_text").css("display","block").delay(3e3).fadeOut():(r=jQuery("#"+_).find(".armCopyText"),a=jQuery("#"+_).find(".arm_click_to_copy_text"),(t=jQuery("<input type=text>")).prop("value",e),t.prop("readonly",!0),t.insertAfter(r),t.focus(),t.select(),r.hide(),a.hide(),t.focusout(function(){r.show(),a.removeAttr("style"),t.remove()}))}),jQuery(document).on("click",".arm_skip_setup_process",function(){var e=jQuery(this).parents(".arm_setup_skip_div").find('input[name="arm_wp_nonce"]').val();jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=skip_setup_action&_wpnonce="+e,success:function(e){"success"==e.type?window.location.replace(e.redirect_url):(jQuery("#"+get_loader_id+"_loader").hide(),armToast(activeAddonError,"error"))}})});