function arm_hide_show_subscription_plan_notice(){var e="arm_pp_lock_key",t=(arm_hide_gutenberg_notice("blank_plan_amount"),arm_hide_gutenberg_notice("invalid_plan_amount"),jQuery(".paid_subscription_options_recurring_payment_cycle_label")),r=jQuery(".paid_subscription_options_recurring_payment_cycle_amount"),a=!1,n=!1,o=!1;t.each(function(){""==jQuery(this).val()&&(a=!0)}),r.each(function(){""==jQuery(this).val()?n=!0:ArmPlanNumberValidation(this)||(o=!0)}),a?(arm_lock_postSaving(e),arm_display_gutenber_notice("arm_label_blank")):arm_hide_gutenberg_notice("arm_label_blank"),n?(arm_lock_postSaving(e),arm_display_gutenber_notice("blank_plan_amount")):arm_hide_gutenberg_notice("blank_plan_amount"),o&&(arm_lock_postSaving(e),arm_display_gutenber_notice("invalid_plan_amount")),a||n||o||arm_unlock_postSaving(e)}function ArmPlanNumberValidation(e){e=jQuery(e).val();return!!/^[0-9.]*$/.test(e)}function arm_unlock_postSaving(e){wp.data.select("core/editor").isPostSavingLocked()&&(wp.data.dispatch("core/editor").unlockPostSaving(e),void 0!==wp.data.dispatch("core/editor").unlockPostAutosaving&&wp.data.dispatch("core/editor").unlockPostAutosaving(e))}function arm_lock_postSaving(e){wp.data.dispatch("core/editor").lockPostSaving(e),void 0!==wp.data.dispatch("core/editor").lockPostAutosaving&&wp.data.dispatch("core/editor").lockPostAutosaving(e)}function arm_display_gutenber_notice(e){"blank_plan_amount"==e?wp.data.dispatch("core/notices").createNotice("error",ARMEDITORNOTICELABEL+AMOUNTERROR,{id:"arm_blank_amount_notice"}):"invalid_plan_amount"==e?wp.data.dispatch("core/notices").createNotice("error",ARMEDITORNOTICELABEL+INVALIDAMOUNTERROR,{id:"arm_invalid_amount_notice"}):"arm_label_blank"==e?wp.data.dispatch("core/notices").createNotice("error",ARMEDITORNOTICELABEL+LABELERROR,{id:"arm_blank_label_notice"}):"arm_blank_plans"==e&&wp.data.dispatch("core/notices").createNotice("error",ARMEDITORNOTICELABEL+NO_PLANS,{id:"arm_empty_plan_notice"})}function arm_hide_gutenberg_notice(e){"blank_plan_amount"==e?wp.data.dispatch("core/notices").removeNotice("arm_blank_amount_notice"):"invalid_plan_amount"==e?wp.data.dispatch("core/notices").removeNotice("arm_invalid_amount_notice"):"arm_label_blank"==e?wp.data.dispatch("core/notices").removeNotice("arm_blank_label_notice"):"arm_blank_plans"==e&&wp.data.dispatch("core/notices").removeNotice("arm_empty_plan_notice")}function isNumber(e){e=e.which||e.keyCode;return!(46!=e&&31<e&&(e<48||57<e))}function ArmNumberValidation(e,t){e=e.which||e.keyCode,t=jQuery(t).val();return!(46!=e&&31<e&&(e<48||57<e)&&37!=e&&39!=e)&&!(""!=t&&1<t.split(".").length&&46==e)}!function(){arm_membership_shortcode=armember_block_admin.arm_block_esc_html.membership_shortcodes,arm_restrict_content_shortcode=armember_block_admin.arm_block_esc_html.restrict_content_shortcode,arm_armember_block_restriction=armember_block_admin.arm_block_esc_html.armember_block_restriction,arm_all_membership_plan=armember_block_admin.all_membership_plans,arm_gutenberg_block_restriction_feature=armember_block_admin.arm_gutenberg_block_restriction_feature;wp.i18n.__;var o=wp.element.createElement,a={};const e=wp.blocks["registerBlockType"],i=wp.element["Fragment"],{InspectorControls:_,InnerBlocks:c,useBlockProps:l}=wp.blockEditor,{PanelBody:m,RadioControl:s,ToggleControl:p}=wp.components;var t=o("svg",{width:32,height:32,viewBox:"0 0 32 32"},o("path",{d:"M15.5915 8.99385C17.5402 9.1187 19.7137 7.24504 19.5889 4.92196C19.4887 2.7488 17.79 1 15.6165 1C13.3679 1.02503 11.6191 2.77352 11.6191 5.04683C11.6191 7.24536 13.4427 8.99385 15.5915 8.99385Z",style:{fill:"#F54EAC"}}),o("path",{d:"M5.58224 19.9779C5.57754 19.9754 5.65515 20.026 5.65734 20.0279L5.65703 20.0273C11.4689 13.4846 15.9883 16.4946 18.8494 20.4247C19.4486 20.4491 19.9866 20.4942 20.4395 20.544C17.6473 15.031 12.0695 10.7222 5.58224 19.9779Z",style:{fill:"#005AEE"}}),o("path",{d:"M22.3459 27.2021L22.3465 27.2018C22.3164 26.1606 22.0814 24.8912 21.6674 23.5637L20.7051 23.4551C21.75 25.4668 22.3146 27.1295 22.3459 27.2021Z",style:{fill:"#005AEE"}}),o("path",{d:"M21.9117 20.768C21.984 20.7871 23.0117 21.0631 24.1318 21.5062C23.8986 19.9399 23.4636 17.9341 22.6631 16.4113C22.1461 15.428 21.6138 14.3877 20.8642 13.5384C20.0148 12.5892 19.0654 11.7398 17.9162 11.1652C16.9917 10.7155 16.0425 10.4407 15.0429 10.3907C13.4441 10.3406 11.97 10.7653 10.6209 11.5649C8.94688 12.5642 7.64781 13.9881 6.5484 15.6117C5.57416 17.0607 4.84936 18.6346 4.27477 20.3082C4.17494 20.608 4.09983 20.9078 4 21.2077C4.03317 21.2242 4.05821 21.2327 4.07511 21.2327C4.12518 21.2077 4.7996 19.9587 5.12445 19.4091C5.99885 18.0102 7.02315 16.7612 8.32223 15.7369C9.69642 14.6628 11.1955 13.9881 12.9693 13.9133C14.5431 13.8385 15.9849 14.2372 17.2665 15.1373C19.188 16.4871 20.5003 18.6196 21.3819 20.6666C21.6598 20.7116 21.8428 20.7495 21.9092 20.767L21.9117 20.768Z",style:{fill:"#0059ED"}}),o("path",{d:"M23.038 26.3537C23.1714 27.1364 23.2712 27.919 23.3378 28.7017C23.3879 29.4763 23.3879 30.2505 23.4129 31.0001C23.4129 31.4748 23.4129 31.1932 23.4129 31.6676C24.2523 28.9274 24.5571 26.9639 24.3956 24.0975C23.7594 23.9257 23.1071 23.7849 22.4355 23.6738C22.8327 25.0962 23.0001 26.1521 23.038 26.3537Z",style:{fill:"#0059ED"}}),o("path",{d:"M25.9639 23.1693C24.6545 22.1469 21.7478 21.3745 21.7478 21.3745C21.5193 21.3147 19.9646 21.0293 17.91 21.0293C15.709 21.0293 12.934 21.3567 10.5991 22.6404C10.2363 22.8401 9.8733 23.0398 9.51122 23.2666C8.8681 23.7 8.21903 24.161 7.56401 24.649C7.20255 24.9031 6.86362 25.2379 6.52344 25.5187C6.52375 25.5368 6.52438 25.555 6.52469 25.5731C6.6458 25.5156 6.76692 25.4579 6.88803 25.4007C7.43257 25.1146 7.97711 24.8289 8.5226 24.5701C9.18982 24.2809 9.83636 23.9924 10.5064 23.8121C11.5207 23.5142 12.5366 23.2704 13.5333 23.0817C14.4562 22.9311 15.3735 22.835 16.2855 22.7934C16.7968 22.7669 17.3088 22.76 17.8183 22.76C18.3463 22.76 18.8717 22.7672 19.3919 22.7678L19.6779 22.7606C19.681 22.7606 19.6844 22.7606 19.6885 22.7606C19.8147 22.7606 20.435 22.7956 20.435 22.7956L21.8085 22.9506C24.0443 23.2466 26.2474 23.8697 28.4438 25.0095C28.4009 24.9284 26.6709 23.6406 25.9639 23.1693Z",style:{fill:"#F54EAC"}}));e("armember/armember-shortcode",{title:arm_membership_shortcode.block_title,icon:t,category:"armember",keywords:arm_membership_shortcode.keywords,attributes:{ArmShortcode:{type:"string",default:""},content:{source:"html",selector:"h2"}},html:!0,insert:function(e){arm_open_form_shortcode_popup()},edit:function(t){window.arm_props_selected="1",window.arm_props=t;var e=jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").val(),r=jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").length;if("armember/armember-shortcode"==t.name){if(""==e||null==e||"undefined"==e||0==r){if(!t.isSelected)return o("textarea",{className:"wp-block-armember-armember-shortcode",value:t.attributes.ArmShortcode,style:a,onChange:function(e){t.setAttributes({ArmShortcode:jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").val()})}},t.attributes.ArmShortcode);arm_open_form_shortcode_popup()}return o("textarea",{className:"wp-block-armember-armember-shortcode",value:t.attributes.ArmShortcode,style:a,onChange:function(e){t.setAttributes({ArmShortcode:jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").val()})}},t.attributes.ArmShortcode)}},save:function(e){return void 0!==window.arm_props&&null!==window.arm_props&&1==jQuery("#block-"+window.arm_props.clientId).find(".editor-block-list__block-html-textarea").is(":visible")&&(e.attributes.ArmShortcode=jQuery("#block-"+window.arm_props.clientId).find(".editor-block-list__block-html-textarea").val()),e.attributes.ArmShortcode}}),e("armember/armember-restrict-content",{title:arm_restrict_content_shortcode.block_title,icon:t,category:"armember",keywords:arm_restrict_content_shortcode.keywords,attributes:{ArmRestrictContent:{type:"string",default:""},content:{source:"html",selector:"h2"}},html:!0,insert:function(e){arm_open_restriction_shortcode_popup()},edit:function(t){window.arm_props_selected="2",window.arm_restrict_content_props=t;var e=jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").val(),r=jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").length;if("armember/armember-restrict-content"==t.name){if(""==e||null==e||"undefined"==e||0==r){if(!t.isSelected)return o("textarea",{className:"wp-block-armember-armember-restrict-content-textarea",value:t.attributes.ArmRestrictContent,style:a,onChange:function(e){t.setAttributes({ArmRestrictContent:jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").val()})}},t.attributes.ArmRestrictContent);arm_open_restriction_shortcode_popup()}return o("textarea",{className:"wp-block-armember-armember-restrict-content-textarea",value:t.attributes.ArmRestrictContent,style:a,onChange:function(e){t.setAttributes({ArmRestrictContent:jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").val()})}},t.attributes.ArmRestrictContent)}},save:function(e){return void 0!==window.arm_restrict_content_props&&null!==window.arm_restrict_content_props&&1==jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".editor-block-list__block-html-textarea").is(":visible")&&(e.attributes.ArmRestrictContent=jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".editor-block-list__block-html-textarea").val()),e.attributes.ArmRestrictContent}}),1==arm_gutenberg_block_restriction_feature&&e("armember/armember-block-restriction",{title:arm_armember_block_restriction.block_title,description:arm_armember_block_restriction.description,icon:t,category:"armember",keywords:arm_armember_block_restriction.keywords,attributes:{plans:{type:"array",default:[]},uid:{type:"string",default:""},allowed_access:{type:"string",default:"show"}},edit:function(e){const{attributes:{plans:r,allowed_access:t},setAttributes:a}=e;var e=l(),n=arm_all_membership_plan.map(function(t){return[o(p,{key:t.value,label:t.label,checked:r.some(e=>e==t.value),onChange:function(e){if(e&&!r.some(e=>e==t.value)){const e=r.slice();e.push(t.value+""),a({plans:e})}else if(!e&&r.some(e=>e==t.value)){const e=r.filter(e=>e!=t.value);a({plans:e})}}})]});return[o(i,{key:"fragment"},o("div",{className:"armember-block-restrict-membership-element"},o(_,{key:"inspector"},o(m,{title:arm_armember_block_restriction.block_title,className:"armember-block-restrict-membership-element-panel"},o("p",null,o("strong",null,arm_armember_block_restriction.restriction_type.type)),o("div",{className:"armswitch-radio"},o(s,{selected:t,options:[{label:arm_armember_block_restriction.restriction_type.show,value:"show"},{label:arm_armember_block_restriction.restriction_type.hide,value:"hide"}],onChange:e=>a({allowed_access:e})})),o("p",null,o("strong",null,arm_armember_block_restriction.membership_plan)),o("div",{className:"armember-block-inspector-scrollable"},o("div",{className:"armswitch-checkbox"},o("div",{className:"armember-block-plan-help"},arm_armember_block_restriction.arm_armember_block_restriction),n)))),o("span",{className:"armember-block-title"},arm_armember_block_restriction.block_title),o("div",e,o(c,{templateLock:!1}))))]},save:function(e){var t=l.save();return o("div",t,o(c.Content,e.attributes.plans,e.attributes.allowed_access),o(c.Content,null))}})}((window.wp.blocks,window.wp.components,window.wp.i18n,window.wp.element,window.wp.editor,window.wp.blockEditor)),jQuery(document).on("change","#arm_enable_paid_post",function(){var e,t,r,a,n="arm_pp_lock_key";jQuery(this).is(":checked")?"buy_now"==jQuery('input[name="paid_post_type"]:checked').val()?(""==jQuery('input[name="arm_paid_post_plan"]').val()?arm_lock_postSaving:arm_unlock_postSaving)(n):(e=jQuery(".paid_subscription_options_recurring_payment_cycle_label"),t=jQuery(".paid_subscription_options_recurring_payment_cycle_amount"),a=r=!1,e.each(function(){""==jQuery(this).val()&&(r=!0)}),t.each(function(){""==jQuery(this).val()&&(a=!0)}),(r||a?arm_lock_postSaving:arm_unlock_postSaving)(n)):arm_unlock_postSaving(n)}),jQuery(document).on("change",'input[name="paid_post_type"]',function(){var e,t="arm_pp_lock_key";jQuery(this).is(":checked")&&("buy_now"==(e=jQuery(this).val())?(arm_hide_gutenberg_notice("arm_label_blank"),""==jQuery('input[name="arm_paid_post_plan"]').val()?(arm_lock_postSaving(t),arm_display_gutenber_notice("blank_plan_amount")):ArmPlanNumberValidation(jQuery('input[name="arm_paid_post_plan"]'))?(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("blank_plan_amount"),arm_hide_gutenberg_notice("invalid_plan_amount")):(arm_lock_postSaving(t),arm_hide_gutenberg_notice("blank_plan_amount"),arm_display_gutenber_notice("invalid_plan_amount"))):"free"==e?(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("blank_plan_amount"),arm_hide_gutenberg_notice("invalid_plan_amount")):arm_hide_show_subscription_plan_notice())}),jQuery(document).on("change",".arm_enable_drip_rule",function(){var e=jQuery(this).attr("data-id"),t="arm_pp_lock_key";jQuery(this).is(":checked")&&(""==jQuery("#arm_drip_rule_plans_"+e).val()?(arm_lock_postSaving(t),arm_display_gutenber_notice("arm_blank_plans")):(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("arm_blank_plans")))}),jQuery(document).on("change","#arm_drip_rule_plans",function(){var e="arm_pp_lock_key";""==jQuery(this).val()?(arm_lock_postSaving(e),arm_display_gutenber_notice("arm_blank_plans")):(arm_unlock_postSaving(e),arm_hide_gutenberg_notice("arm_blank_plans"))}),jQuery(document).on("keyup",".paid_subscription_options_recurring_payment_cycle_label",function(){var e,t="arm_pp_lock_key";""==jQuery(this).val()?(arm_lock_postSaving(t),arm_display_gutenber_notice("arm_label_blank")):1<jQuery(".paid_subscription_options_recurring_payment_cycle_label").length?(e=!1,jQuery(".paid_subscription_options_recurring_payment_cycle_label").each(function(){""==jQuery(this).val()&&(e=!0)}),e?(arm_lock_postSaving(t),arm_display_gutenber_notice("arm_label_blank")):(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("arm_label_blank"))):(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("arm_label_blank"))}),jQuery(document).on("click","#arm_add_payment_cycle_recurring",function(){arm_hide_show_subscription_plan_notice()}),jQuery(document).on("click","#arm_remove_recurring_payment_cycle",function(){arm_hide_show_subscription_plan_notice()}),jQuery(document).on("keyup",".paid_subscription_options_recurring_payment_cycle_amount",function(){var e,t,r="arm_pp_lock_key";""==jQuery(this).val()?(arm_lock_postSaving(r),arm_display_gutenber_notice("blank_plan_amount")):ArmPlanNumberValidation(this)?0<jQuery(".paid_subscription_options_recurring_payment_cycle_amount").length&&(t=e=!1,jQuery(".paid_subscription_options_recurring_payment_cycle_amount").each(function(){""==jQuery(this).val()?e=!0:ArmPlanNumberValidation(this)||(t=!0)}),e?(arm_lock_postSaving(r),arm_display_gutenber_notice("blank_plan_amount")):arm_hide_gutenberg_notice("blank_plan_amount"),t&&(arm_lock_postSaving(r),arm_display_gutenber_notice("invalid_plan_amount")),e||t||(arm_unlock_postSaving(r),arm_hide_gutenberg_notice("blank_plan_amount"),arm_hide_gutenberg_notice("invalid_plan_amount"))):(arm_lock_postSaving(r),arm_hide_gutenberg_notice("blank_plan_amount"),arm_display_gutenber_notice("invalid_plan_amount"))}),jQuery(document).on("keyup",'input[name="arm_paid_post_plan"]',function(e){var t="arm_pp_lock_key";""==jQuery(this).val()?(arm_lock_postSaving(t),arm_display_gutenber_notice("blank_plan_amount")):ArmPlanNumberValidation(this)?(arm_unlock_postSaving(t),arm_hide_gutenberg_notice("blank_plan_amount"),arm_hide_gutenberg_notice("invalid_plan_amount")):(arm_lock_postSaving(t),arm_hide_gutenberg_notice("blank_plan_amount"),arm_display_gutenber_notice("invalid_plan_amount"))});