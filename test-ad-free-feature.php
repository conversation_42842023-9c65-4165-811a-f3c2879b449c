<?php
// Test file to demonstrate ad-free premium feature
// This simulates how the ad-free feature works

// <PERSON><PERSON> function to simulate ARMember check
function mock_is_premium_user_no_ads($simulate_premium = false) {
    return $simulate_premium;
}

// Mock function to simulate ad display
function mock_doo_compose_ad($ad_id, $is_premium = false) {
    if ($is_premium) {
        return ''; // No ads for premium users
    }
    
    // Sample ads for different positions
    $ads = [
        '_dooplay_adhome' => '<div style="background: #ff6b6b; color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px 0;"><h3>🎯 Homepage Banner Ad</h3><p>This is where your homepage ads would appear</p></div>',
        '_dooplay_adsingle' => '<div style="background: #4ecdc4; color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px 0;"><h3>📄 Single Post Ad</h3><p>This is where your single post ads would appear</p></div>',
        '_dooplay_adplayer' => '<div style="background: #45b7d1; color: white; padding: 20px; text-align: center; border-radius: 10px; margin: 10px 0;"><h3>🎬 Video Player Ad</h3><p>This is where your video player ads would appear</p></div>',
        '_dooplay_adlinktop' => '<div style="background: #f9ca24; color: #333; padding: 15px; text-align: center; border-radius: 10px; margin: 10px 0;"><h3>🔗 Link Top Ad</h3><p>This ad appears before download links</p></div>',
        '_dooplay_adlinkbottom' => '<div style="background: #6c5ce7; color: white; padding: 15px; text-align: center; border-radius: 10px; margin: 10px 0;"><h3>🔗 Link Bottom Ad</h3><p>This ad appears after download links</p></div>',
    ];
    
    return isset($ads[$ad_id]) ? $ads[$ad_id] : '<div style="background: #ddd; padding: 10px; text-align: center; border-radius: 5px;">Generic Ad</div>';
}

// Simulate premium status (change this to test different scenarios)
$simulate_premium_user = isset($_GET['premium']) ? (bool)$_GET['premium'] : false;
$is_premium = mock_is_premium_user_no_ads($simulate_premium_user);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ad-Free Premium Feature Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .status-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        
        .premium-notification {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }
        
        .ad-free-notice {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .toggle-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .toggle-links a {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .toggle-links .regular {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }
        
        .toggle-links .premium {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
        }
        
        .toggle-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .content-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .content-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .ad-placeholder {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            color: #666;
            border-radius: 10px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .feature-yes {
            color: #28a745;
            font-weight: bold;
        }
        
        .feature-no {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ad-Free Premium Feature Demo</h1>
        
        <div class="toggle-links">
            <a href="?premium=0" class="regular">
                <i class="fas fa-user"></i> View as Regular User (With Ads)
            </a>
            <a href="?premium=1" class="premium">
                <i class="fas fa-crown"></i> View as Premium User (Ad-Free)
            </a>
        </div>
        
        <div class="status-info">
            <strong>Current Status:</strong> 
            <?php if ($is_premium): ?>
                <span style="color: #FFD700;"><i class="fas fa-crown"></i> Premium User (Ad-Free Experience)</span>
            <?php else: ?>
                <span style="color: #6c757d;"><i class="fas fa-user"></i> Regular User (Ads Enabled)</span>
            <?php endif; ?>
        </div>
        
        <?php if ($is_premium): ?>
        <div class="ad-free-notice">
            <i class="fas fa-shield-alt" style="margin-right: 8px;"></i>
            Ad-Free Experience Active: You won't see any advertisements on this site!
            <i class="fas fa-check-circle" style="margin-left: 8px;"></i>
        </div>
        <?php endif; ?>
        
        <!-- Homepage Section -->
        <div class="content-section">
            <h3><i class="fas fa-home"></i> Homepage Content</h3>
            <p>This is the main homepage content where users browse movies and TV shows.</p>
            
            <?php 
            $homepage_ad = mock_doo_compose_ad('_dooplay_adhome', $is_premium);
            if ($homepage_ad): 
                echo $homepage_ad;
            else: 
            ?>
                <div class="ad-placeholder">
                    <i class="fas fa-eye-slash"></i> Homepage Ad Hidden (Premium User)
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Single Post Section -->
        <div class="content-section">
            <h3><i class="fas fa-film"></i> Movie/TV Show Page</h3>
            <p>This is where users view individual movie or TV show details.</p>
            
            <?php 
            $single_ad = mock_doo_compose_ad('_dooplay_adsingle', $is_premium);
            if ($single_ad): 
                echo $single_ad;
            else: 
            ?>
                <div class="ad-placeholder">
                    <i class="fas fa-eye-slash"></i> Single Post Ad Hidden (Premium User)
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Video Player Section -->
        <div class="content-section">
            <h3><i class="fas fa-play-circle"></i> Video Player</h3>
            <p>This is where users watch movies and TV shows.</p>
            
            <?php 
            $player_ad = mock_doo_compose_ad('_dooplay_adplayer', $is_premium);
            if ($player_ad): 
                echo $player_ad;
            else: 
            ?>
                <div class="ad-placeholder">
                    <i class="fas fa-eye-slash"></i> Video Player Ad Hidden (Premium User)
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Download Links Section -->
        <div class="content-section">
            <h3><i class="fas fa-download"></i> Download Links</h3>
            
            <?php 
            $link_top_ad = mock_doo_compose_ad('_dooplay_adlinktop', $is_premium);
            if ($link_top_ad): 
                echo $link_top_ad;
            else: 
            ?>
                <div class="ad-placeholder">
                    <i class="fas fa-eye-slash"></i> Link Top Ad Hidden (Premium User)
                </div>
            <?php endif; ?>
            
            <p>Download links would appear here...</p>
            
            <?php 
            $link_bottom_ad = mock_doo_compose_ad('_dooplay_adlinkbottom', $is_premium);
            if ($link_bottom_ad): 
                echo $link_bottom_ad;
            else: 
            ?>
                <div class="ad-placeholder">
                    <i class="fas fa-eye-slash"></i> Link Bottom Ad Hidden (Premium User)
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Comparison Table -->
        <h2>Feature Comparison</h2>
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>Regular User</th>
                    <th>Premium User</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Homepage Ads</strong></td>
                    <td class="feature-no"><i class="fas fa-times"></i> Visible</td>
                    <td class="feature-yes"><i class="fas fa-check"></i> Hidden</td>
                </tr>
                <tr>
                    <td><strong>Single Post Ads</strong></td>
                    <td class="feature-no"><i class="fas fa-times"></i> Visible</td>
                    <td class="feature-yes"><i class="fas fa-check"></i> Hidden</td>
                </tr>
                <tr>
                    <td><strong>Video Player Ads</strong></td>
                    <td class="feature-no"><i class="fas fa-times"></i> Visible</td>
                    <td class="feature-yes"><i class="fas fa-check"></i> Hidden</td>
                </tr>
                <tr>
                    <td><strong>Download Link Ads</strong></td>
                    <td class="feature-no"><i class="fas fa-times"></i> Visible</td>
                    <td class="feature-yes"><i class="fas fa-check"></i> Hidden</td>
                </tr>
                <tr>
                    <td><strong>Direct Downloads</strong></td>
                    <td class="feature-no"><i class="fas fa-times"></i> No (10s delay)</td>
                    <td class="feature-yes"><i class="fas fa-check"></i> Yes (Instant)</td>
                </tr>
                <tr>
                    <td><strong>Premium Badge</strong></td>
                    <td class="feature-no"><i class="fas fa-lock"></i> Locked</td>
                    <td class="feature-yes"><i class="fas fa-crown"></i> Unlocked</td>
                </tr>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 10px;">
            <h3>How it works:</h3>
            <ul>
                <li><strong>Ad Detection:</strong> The <code>doo_compose_ad()</code> function checks if user is premium before displaying ads</li>
                <li><strong>ARMember Integration:</strong> Uses ARMember plugin functions to verify active membership status</li>
                <li><strong>Complete Ad-Free:</strong> All ad positions (homepage, single post, video player, download links) are hidden for premium users</li>
                <li><strong>Seamless Experience:</strong> Premium users get clean, distraction-free browsing and downloading</li>
            </ul>
            
            <h4>Benefits for Site Owners:</h4>
            <ul>
                <li>💰 <strong>Revenue Stream:</strong> Premium memberships provide recurring income</li>
                <li>🎯 <strong>User Retention:</strong> Premium users are more likely to stay loyal</li>
                <li>📈 <strong>Conversion Incentive:</strong> Clear value proposition for upgrading</li>
                <li>⚖️ <strong>Balanced Model:</strong> Free users support site through ads, premium users through subscriptions</li>
            </ul>
        </div>
    </div>
</body>
</html>
