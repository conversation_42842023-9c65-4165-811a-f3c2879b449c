<?php
/*
* ----------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* ----------------------------------------------------
* @since 2.5.5
*/

# Theme options
define('DOO_THEME_DOWNLOAD_MOD', true);
define('DOO_THEME_PLAYER_MOD',   true);

// Register custom taxonomies
function dooplay_register_taxonomies() {
    // Register Genres taxonomy
    register_taxonomy('genres', array('movies', 'tvshows', 'post'), array(
        'hierarchical' => true,
        'labels' => array(
            'name' => __('Genres', 'dooplay'),
            'singular_name' => __('Genre', 'dooplay'),
            'search_items' => __('Search Genres', 'dooplay'),
            'all_items' => __('All Genres', 'dooplay'),
            'parent_item' => __('Parent Genre', 'dooplay'),
            'parent_item_colon' => __('Parent Genre:', 'dooplay'),
            'edit_item' => __('Edit Genre', 'dooplay'),
            'update_item' => __('Update Genre', 'dooplay'),
            'add_new_item' => __('Add New Genre', 'dooplay'),
            'new_item_name' => __('New Genre Name', 'dooplay'),
            'menu_name' => __('Genres', 'dooplay'),
        ),
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'genre'),
        'public' => true,
        'show_in_rest' => true,
    ));
}
add_action('init', 'dooplay_register_taxonomies');

// Add default custom genres (runs only once)
function dooplay_add_default_genres() {
    // Check if already added
    if (get_option('dooplay_default_genres_added')) {
        return;
    }

    $default_genres = array(
        'বাংলা সিনেমা' => 'Bangla movies and films',
        'বলিউড' => 'Bollywood movies and shows',
        'হলিউড' => 'Hollywood movies and shows',
        'ওয়েব সিরিজ' => 'Web series from various platforms',
        'বাংলা নাটক' => 'Bangla TV dramas and series',
        'ডকুমেন্টারি' => 'Documentary films and series',
        'পারিবারিক' => 'Family-friendly content',
        'ট্রেন্ডিং' => 'Currently trending content'
    );

    foreach($default_genres as $name => $description) {
        if (!term_exists($name, 'genres')) {
            wp_insert_term($name, 'genres', array(
                'description' => $description,
                'slug' => sanitize_title($name)
            ));
        }
    }

    // Mark as added
    update_option('dooplay_default_genres_added', true);
}
add_action('init', 'dooplay_add_default_genres');

// Sanitize genre IDs input
function dooplay_sanitize_genre_ids($value) {
    if (is_array($value)) {
        $value = implode(',', $value);
    }

    if (!is_string($value)) {
        return '';
    }

    // Remove any non-numeric characters except commas and spaces
    $value = preg_replace('/[^0-9,\s]/', '', $value);

    // Split by comma, trim spaces, filter out empty values
    $ids = array_map('trim', explode(',', $value));
    $ids = array_filter($ids, function($id) {
        return is_numeric($id) && $id > 0;
    });

    // Return as comma-separated string
    return implode(',', $ids);
}
define('DOO_THEME_DBMOVIES',     true);
define('DOO_THEME_USER_MOD',     true);
define('DOO_THEME_VIEWS_COUNT',  true);
define('DOO_THEME_RELATED',      true);

// Live TV Channel Custom Post Type
function dooplay_register_live_tv_post_type() {
    register_post_type('live_tv_channels', array(
        'labels' => array(
            'name' => __('Live TV Channels', 'dooplay'),
            'singular_name' => __('Live TV Channel', 'dooplay'),
            'add_new' => __('Add New Channel', 'dooplay'),
            'add_new_item' => __('Add New Channel', 'dooplay'),
            'edit_item' => __('Edit Channel', 'dooplay'),
            'new_item' => __('New Channel', 'dooplay'),
            'view_item' => __('View Channel', 'dooplay'),
            'search_items' => __('Search Channels', 'dooplay'),
            'not_found' => __('No channels found', 'dooplay'),
            'not_found_in_trash' => __('No channels found in trash', 'dooplay'),
            'menu_name' => __('Live TV', 'dooplay')
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-video-alt3',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'show_in_rest' => false, // Disable Gutenberg, use Classic Editor
        'rewrite' => array('slug' => 'live-tv'),
        'show_in_rest' => true,
        'menu_position' => 5
    ));
}
add_action('init', 'dooplay_register_live_tv_post_type');

// Add Live TV meta boxes
function dooplay_add_live_tv_meta_boxes() {
    add_meta_box(
        'live_tv_details',
        __('Channel Details', 'dooplay'),
        'dooplay_live_tv_meta_box_callback',
        'live_tv_channels',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'dooplay_add_live_tv_meta_boxes');

// Meta box callback
function dooplay_live_tv_meta_box_callback($post) {
    wp_nonce_field('dooplay_live_tv_meta_box', 'dooplay_live_tv_meta_box_nonce');

    $stream_url = get_post_meta($post->ID, '_live_tv_stream_url', true);
    $channel_logo = get_post_meta($post->ID, '_live_tv_channel_logo', true);
    $channel_category = get_post_meta($post->ID, '_live_tv_category', true);
    $is_active = get_post_meta($post->ID, '_live_tv_is_active', true);

    ?>
    <style>
    .live-tv-meta-box {
        background: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
    }
    .live-tv-meta-box .form-table th {
        width: 150px;
        font-weight: 600;
        color: #333;
    }
    .live-tv-meta-box .form-table td {
        padding: 10px 0;
    }
    .live-tv-meta-box input[type="url"],
    .live-tv-meta-box select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .live-tv-meta-box .description {
        font-style: italic;
        color: #666;
        font-size: 0.9em;
        margin-top: 5px;
    }
    .channel-logo-preview {
        max-width: 100px;
        max-height: 60px;
        margin-top: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        display: none;
    }
    .test-stream-btn {
        background: #0073aa;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
        margin-left: 10px;
    }
    .test-stream-btn:hover {
        background: #005a87;
    }
    .stream-status {
        margin-left: 10px;
        font-weight: bold;
    }
    .stream-status.working { color: #46b450; }
    .stream-status.broken { color: #dc3232; }
    </style>

    <div class="live-tv-meta-box">
        <table class="form-table">
            <tr>
                <th><label for="live_tv_stream_url"><?php _e('Stream URL', 'dooplay'); ?> <span style="color: red;">*</span></label></th>
                <td>
                    <input type="url" id="live_tv_stream_url" name="live_tv_stream_url"
                           value="<?php echo esc_attr($stream_url); ?>" required />
                    <button type="button" class="test-stream-btn" onclick="testStreamUrl()"><?php _e('Test Stream', 'dooplay'); ?></button>
                    <span id="stream-status" class="stream-status"></span>
                    <p class="description"><?php _e('Enter the .m3u8 stream URL for this channel. This is required.', 'dooplay'); ?></p>
                </td>
            </tr>
            <tr>
                <th><label for="live_tv_channel_logo"><?php _e('Channel Logo URL', 'dooplay'); ?></label></th>
                <td>
                    <input type="url" id="live_tv_channel_logo" name="live_tv_channel_logo"
                           value="<?php echo esc_attr($channel_logo); ?>" onchange="previewLogo(this.value)" />
                    <p class="description"><?php _e('Enter the URL of the channel logo image (optional).', 'dooplay'); ?></p>
                    <img id="logo-preview" class="channel-logo-preview" src="<?php echo esc_url($channel_logo); ?>"
                         style="<?php echo $channel_logo ? 'display: block;' : 'display: none;'; ?>" />
                </td>
            </tr>
            <tr>
                <th><label for="live_tv_category"><?php _e('Category', 'dooplay'); ?></label></th>
                <td>
                    <select id="live_tv_category" name="live_tv_category">
                        <option value="news" <?php selected($channel_category, 'news'); ?>><?php _e('📰 News', 'dooplay'); ?></option>
                        <option value="entertainment" <?php selected($channel_category, 'entertainment'); ?>><?php _e('🎬 Entertainment', 'dooplay'); ?></option>
                        <option value="sports" <?php selected($channel_category, 'sports'); ?>><?php _e('⚽ Sports', 'dooplay'); ?></option>
                        <option value="music" <?php selected($channel_category, 'music'); ?>><?php _e('🎵 Music', 'dooplay'); ?></option>
                        <option value="kids" <?php selected($channel_category, 'kids'); ?>><?php _e('👶 Kids', 'dooplay'); ?></option>
                        <option value="religious" <?php selected($channel_category, 'religious'); ?>><?php _e('🙏 Religious', 'dooplay'); ?></option>
                        <option value="other" <?php selected($channel_category, 'other'); ?>><?php _e('📺 Other', 'dooplay'); ?></option>
                    </select>
                    <p class="description"><?php _e('Select the category that best describes this channel.', 'dooplay'); ?></p>
                </td>
            </tr>
            <tr>
                <th><label for="live_tv_is_active"><?php _e('Channel Status', 'dooplay'); ?></label></th>
                <td>
                    <label>
                        <input type="checkbox" id="live_tv_is_active" name="live_tv_is_active" value="1"
                               <?php checked($is_active, '1'); ?> />
                        <?php _e('Channel is active and working', 'dooplay'); ?>
                    </label>
                    <p class="description"><?php _e('Uncheck this if the channel is not working or should be hidden from the frontend.', 'dooplay'); ?></p>
                </td>
            </tr>
        </table>
    </div>

    <script>
    function previewLogo(url) {
        const preview = document.getElementById('logo-preview');
        if (url) {
            preview.src = url;
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }

    function testStreamUrl() {
        const streamUrl = document.getElementById('live_tv_stream_url').value;
        const statusElement = document.getElementById('stream-status');
        const testBtn = document.querySelector('.test-stream-btn');

        if (!streamUrl) {
            alert('<?php _e("Please enter a stream URL first.", "dooplay"); ?>');
            return;
        }

        testBtn.disabled = true;
        testBtn.textContent = '<?php _e("Testing...", "dooplay"); ?>';
        statusElement.textContent = '';
        statusElement.className = 'stream-status';

        // Simple test - try to fetch the URL
        fetch(streamUrl, { method: 'HEAD', mode: 'no-cors' })
            .then(() => {
                statusElement.textContent = '<?php _e("✓ Stream appears to be working", "dooplay"); ?>';
                statusElement.className = 'stream-status working';
            })
            .catch(() => {
                statusElement.textContent = '<?php _e("⚠ Could not verify stream (may still work)", "dooplay"); ?>';
                statusElement.className = 'stream-status broken';
            })
            .finally(() => {
                testBtn.disabled = false;
                testBtn.textContent = '<?php _e("Test Stream", "dooplay"); ?>';
            });
    }
    </script>
    <?php
}

// Save meta box data
function dooplay_save_live_tv_meta_box($post_id) {
    if (!isset($_POST['dooplay_live_tv_meta_box_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['dooplay_live_tv_meta_box_nonce'], 'dooplay_live_tv_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    if (isset($_POST['live_tv_stream_url'])) {
        update_post_meta($post_id, '_live_tv_stream_url', sanitize_url($_POST['live_tv_stream_url']));
    }

    if (isset($_POST['live_tv_channel_logo'])) {
        update_post_meta($post_id, '_live_tv_channel_logo', sanitize_url($_POST['live_tv_channel_logo']));
    }

    if (isset($_POST['live_tv_category'])) {
        update_post_meta($post_id, '_live_tv_category', sanitize_text_field($_POST['live_tv_category']));
    }

    $is_active = isset($_POST['live_tv_is_active']) ? '1' : '0';
    update_post_meta($post_id, '_live_tv_is_active', $is_active);
}
add_action('save_post', 'dooplay_save_live_tv_meta_box');

// Force Classic Editor for Live TV Channels
function dooplay_force_classic_editor_for_live_tv($use_block_editor, $post) {
    if ($post && $post->post_type === 'live_tv_channels') {
        return false; // Use Classic Editor
    }
    return $use_block_editor;
}
add_filter('use_block_editor_for_post', 'dooplay_force_classic_editor_for_live_tv', 10, 2);

// Also disable for post type
function dooplay_disable_gutenberg_for_live_tv($can_edit, $post_type) {
    if ($post_type === 'live_tv_channels') {
        return false;
    }
    return $can_edit;
}
add_filter('use_block_editor_for_post_type', 'dooplay_disable_gutenberg_for_live_tv', 10, 2);

// M3U Playlist Scraper
function dooplay_scrape_m3u_playlist($url) {
    $response = wp_remote_get($url, array(
        'timeout' => 30,
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ));

    if (is_wp_error($response)) {
        return false;
    }

    $body = wp_remote_retrieve_body($response);
    $lines = explode("\n", $body);
    $channels = array();
    $current_channel = array();

    foreach ($lines as $line) {
        $line = trim($line);

        if (strpos($line, '#EXTINF:') === 0) {
            // Parse channel info
            preg_match('/#EXTINF:.*?,(.*?)$/', $line, $matches);
            $current_channel['name'] = isset($matches[1]) ? trim($matches[1]) : 'Unknown Channel';

            // Extract logo if available
            preg_match('/tvg-logo="([^"]*)"/', $line, $logo_matches);
            $current_channel['logo'] = isset($logo_matches[1]) ? $logo_matches[1] : '';

            // Extract group/category
            preg_match('/group-title="([^"]*)"/', $line, $group_matches);
            $current_channel['category'] = isset($group_matches[1]) ? strtolower($group_matches[1]) : 'other';

        } elseif (!empty($line) && strpos($line, '#') !== 0 && !empty($current_channel)) {
            // This is the stream URL
            $current_channel['url'] = $line;
            $channels[] = $current_channel;
            $current_channel = array();
        }
    }

    return $channels;
}

// Import channels from M3U (Fast version without testing)
function dooplay_import_m3u_channels($url, $test_streams = false) {
    // Increase execution time for import
    set_time_limit(300); // 5 minutes
    ini_set('memory_limit', '256M');

    $channels = dooplay_scrape_m3u_playlist($url);
    $imported = 0;
    $skipped = 0;

    if (!$channels) {
        return array('error' => 'Failed to fetch playlist');
    }

    foreach ($channels as $channel) {
        // Check if channel already exists
        $existing = get_posts(array(
            'post_type' => 'live_tv_channels',
            'meta_query' => array(
                array(
                    'key' => '_live_tv_stream_url',
                    'value' => $channel['url'],
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        ));

        if (!empty($existing)) {
            $skipped++;
            continue;
        }

        // Import without testing (faster) or test if requested
        $should_import = true;
        if ($test_streams) {
            $should_import = dooplay_test_stream_url($channel['url']);
        }

        if ($should_import) {
            $post_id = wp_insert_post(array(
                'post_title' => sanitize_text_field($channel['name']),
                'post_type' => 'live_tv_channels',
                'post_status' => 'publish',
                'post_content' => 'Live TV Channel imported from M3U playlist'
            ));

            if ($post_id) {
                update_post_meta($post_id, '_live_tv_stream_url', esc_url_raw($channel['url']));
                update_post_meta($post_id, '_live_tv_channel_logo', esc_url_raw($channel['logo']));
                update_post_meta($post_id, '_live_tv_category', sanitize_text_field($channel['category']));
                // Set as active by default, test later
                update_post_meta($post_id, '_live_tv_is_active', $test_streams ? '1' : '1');
                $imported++;
            }
        } else {
            $skipped++;
        }

        // Prevent timeout by processing in chunks
        if ($imported % 10 == 0) {
            // Clear any output buffers
            if (ob_get_level()) {
                ob_flush();
                flush();
            }
        }
    }

    return array(
        'imported' => $imported,
        'skipped' => $skipped,
        'total' => count($channels)
    );
}

// Test if stream URL is working (Fast version)
function dooplay_test_stream_url($url) {
    $response = wp_remote_head($url, array(
        'timeout' => 5, // Reduced from 10 to 5 seconds
        'redirection' => 2, // Reduced redirections
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'sslverify' => false // Skip SSL verification for speed
    ));

    if (is_wp_error($response)) {
        return false;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    return ($response_code >= 200 && $response_code < 400);
}

// Admin menu for Live TV management
function dooplay_live_tv_admin_menu() {
    add_submenu_page(
        'edit.php?post_type=live_tv_channels',
        __('Channel Management', 'dooplay'),
        __('Channel Management', 'dooplay'),
        'manage_options',
        'live-tv-management',
        'dooplay_live_tv_management_page'
    );
}
add_action('admin_menu', 'dooplay_live_tv_admin_menu');

// Channel management page
function dooplay_live_tv_management_page() {
    // Handle form submissions
    if (isset($_POST['import_m3u']) && wp_verify_nonce($_POST['_wpnonce'], 'import_m3u_action')) {
        $playlist_url = sanitize_url($_POST['playlist_url']);
        $test_streams = isset($_POST['test_streams']) ? true : false;
        if (!empty($playlist_url)) {
            $result = dooplay_import_m3u_channels($playlist_url, $test_streams);
            if (isset($result['error'])) {
                echo '<div class="notice notice-error"><p>' . esc_html($result['error']) . '</p></div>';
            } else {
                echo '<div class="notice notice-success"><p>';
                echo sprintf(__('Import completed! %d channels imported, %d skipped out of %d total.', 'dooplay'),
                    $result['imported'], $result['skipped'], $result['total']);
                if (!$test_streams) {
                    echo '<br>' . __('Note: Channels imported without testing. Use "Test All Channels" to verify them.', 'dooplay');
                }
                echo '</p></div>';
            }
        }
    }

    if (isset($_POST['test_channels']) && wp_verify_nonce($_POST['_wpnonce'], 'test_channels_action')) {
        dooplay_test_all_channels();
        echo '<div class="notice notice-success"><p>' . __('Channel testing completed!', 'dooplay') . '</p></div>';
    }

    if (isset($_POST['import_default']) && wp_verify_nonce($_POST['_wpnonce'], 'import_default_action')) {
        $default_url = 'https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u';

        // Start background import
        $import_id = dooplay_start_background_import($default_url);

        if ($import_id) {
            echo '<div class="notice notice-info"><p>';
            echo __('Import started in background! Refresh this page in 1-2 minutes to see results.', 'dooplay');
            echo ' <a href="javascript:location.reload()" class="button button-small">' . __('Refresh Now', 'dooplay') . '</a>';
            echo '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('Failed to start import. Please try again.', 'dooplay') . '</p></div>';
        }
    }

    if (isset($_POST['remove_inactive']) && wp_verify_nonce($_POST['_wpnonce'], 'remove_inactive_action')) {
        $removed = dooplay_remove_inactive_channels();
        echo '<div class="notice notice-success"><p>';
        echo sprintf(__('%d inactive channels removed.', 'dooplay'), $removed);
        echo '</p></div>';
    }

    if (isset($_POST['add_playlist']) && wp_verify_nonce($_POST['_wpnonce'], 'add_playlist_action')) {
        $playlist_name = sanitize_text_field($_POST['playlist_name']);
        $playlist_url = sanitize_url($_POST['playlist_url']);
        $auto_refresh = isset($_POST['auto_refresh']) ? true : false;

        if (!empty($playlist_name) && !empty($playlist_url)) {
            dooplay_add_active_playlist($playlist_name, $playlist_url, $auto_refresh);
            echo '<div class="notice notice-success"><p>' . __('Playlist added successfully!', 'dooplay') . '</p></div>';
        }
    }

    if (isset($_POST['remove_playlist']) && wp_verify_nonce($_POST['_wpnonce'], 'remove_playlist_action')) {
        $playlist_id = sanitize_text_field($_POST['playlist_id']);
        dooplay_remove_active_playlist($playlist_id);
        echo '<div class="notice notice-success"><p>' . __('Playlist removed successfully!', 'dooplay') . '</p></div>';
    }

    if (isset($_POST['refresh_playlist']) && wp_verify_nonce($_POST['_wpnonce'], 'refresh_playlist_action')) {
        $playlist_id = sanitize_text_field($_POST['playlist_id']);
        $result = dooplay_refresh_single_playlist($playlist_id);
        if ($result) {
            echo '<div class="notice notice-success"><p>';
            echo sprintf(__('Playlist refreshed! %d channels imported.', 'dooplay'), $result['imported']);
            echo '</p></div>';
        }
    }

    ?>
    <div class="wrap">
        <h1><?php _e('Live TV Channel Management', 'dooplay'); ?></h1>

        <div class="live-tv-admin-grid">
            <div class="admin-main-content">
                <!-- Quick Import Default Playlist -->
                <div class="card">
                    <h2><i class="dashicons dashicons-download"></i> <?php _e('Quick Import (Background Processing)', 'dooplay'); ?></h2>
                    <p><?php _e('Import channels from the default PiratesTV playlist. Processing happens in background to prevent timeouts.', 'dooplay'); ?></p>

                    <div class="import-controls">
                        <form method="post" style="display: inline;" id="default-import-form">
                            <?php wp_nonce_field('import_default_action'); ?>
                            <input type="submit" name="import_default" class="button-primary button-large"
                                   value="<?php _e('Start Background Import', 'dooplay'); ?>" />
                        </form>

                        <div id="import-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <p class="progress-text"><?php _e('Import in progress...', 'dooplay'); ?></p>
                        </div>
                    </div>

                    <p class="description">
                        <?php _e('Source:', 'dooplay'); ?>
                        <code>https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u</code><br>
                        <?php _e('⚡ No timeout issues - processes in background', 'dooplay'); ?>
                    </p>
                </div>

                <!-- Custom M3U Import -->
                <div class="card">
                    <h2><i class="dashicons dashicons-media-playlist"></i> <?php _e('Import from Custom M3U Playlist', 'dooplay'); ?></h2>
                    <form method="post">
                        <?php wp_nonce_field('import_m3u_action'); ?>
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('M3U Playlist URL', 'dooplay'); ?></th>
                                <td>
                                    <input type="url" name="playlist_url" class="regular-text"
                                           placeholder="https://example.com/playlist.m3u" required />
                                    <p class="description"><?php _e('Enter the URL of any M3U playlist to import channels from.', 'dooplay'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Import Options', 'dooplay'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="test_streams" value="1" />
                                        <?php _e('Test streams during import (slower but more accurate)', 'dooplay'); ?>
                                    </label>
                                    <p class="description"><?php _e('Unchecked = Fast import (recommended). Checked = Test each stream (may timeout on large playlists).', 'dooplay'); ?></p>
                                </td>
                            </tr>
                        </table>
                        <p class="submit">
                            <input type="submit" name="import_m3u" class="button-primary"
                                   value="<?php _e('Import Custom Playlist', 'dooplay'); ?>" />
                        </p>
                    </form>
                </div>

                <!-- Channel Management Tools -->
                <div class="card">
                    <h2><i class="dashicons dashicons-admin-tools"></i> <?php _e('Channel Management Tools', 'dooplay'); ?></h2>
                    <div class="management-tools">
                        <div class="tool-item">
                            <h3><?php _e('Test All Channels', 'dooplay'); ?></h3>
                            <p><?php _e('Check if all channels are working and automatically disable broken ones.', 'dooplay'); ?></p>
                            <form method="post" style="display: inline;">
                                <?php wp_nonce_field('test_channels_action'); ?>
                                <input type="submit" name="test_channels" class="button-secondary"
                                       value="<?php _e('Test All Channels', 'dooplay'); ?>" />
                            </form>
                        </div>

                        <div class="tool-item">
                            <h3><?php _e('Remove Inactive Channels', 'dooplay'); ?></h3>
                            <p><?php _e('Permanently delete all channels that are marked as inactive.', 'dooplay'); ?></p>
                            <form method="post" style="display: inline;">
                                <?php wp_nonce_field('remove_inactive_action'); ?>
                                <input type="submit" name="remove_inactive" class="button-secondary button-delete"
                                       value="<?php _e('Remove Inactive', 'dooplay'); ?>"
                                       onclick="return confirm('<?php _e('Are you sure? This will permanently delete all inactive channels.', 'dooplay'); ?>')" />
                            </form>
                        </div>

                        <div class="tool-item">
                            <h3><?php _e('Bulk Category Update', 'dooplay'); ?></h3>
                            <p><?php _e('Update category for multiple channels based on keywords in their names.', 'dooplay'); ?></p>
                            <button type="button" class="button-secondary" onclick="openBulkCategoryModal()"><?php _e('Bulk Update Categories', 'dooplay'); ?></button>
                        </div>

                        <div class="tool-item">
                            <h3><?php _e('Export/Import Channels', 'dooplay'); ?></h3>
                            <p><?php _e('Export your channels to JSON or import from backup.', 'dooplay'); ?></p>
                            <a href="<?php echo admin_url('admin-ajax.php?action=export_channels&nonce=' . wp_create_nonce('export_channels')); ?>"
                               class="button-secondary"><?php _e('Export Channels', 'dooplay'); ?></a>
                            <button type="button" class="button-secondary" onclick="openImportModal()"><?php _e('Import Backup', 'dooplay'); ?></button>
                        </div>
                    </div>
                </div>

                <!-- Active Playlists Management -->
                <div class="card">
                    <h2><i class="dashicons dashicons-playlist-video"></i> <?php _e('Active Playlists Management', 'dooplay'); ?></h2>

                    <!-- Add New Playlist -->
                    <div class="add-playlist-section">
                        <h3><?php _e('Add New Playlist', 'dooplay'); ?></h3>
                        <form method="post" class="add-playlist-form">
                            <?php wp_nonce_field('add_playlist_action'); ?>
                            <table class="form-table">
                                <tr>
                                    <th><label for="playlist_name"><?php _e('Playlist Name', 'dooplay'); ?></label></th>
                                    <td><input type="text" id="playlist_name" name="playlist_name" class="regular-text" required /></td>
                                </tr>
                                <tr>
                                    <th><label for="playlist_url"><?php _e('M3U URL', 'dooplay'); ?></label></th>
                                    <td><input type="url" id="playlist_url" name="playlist_url" class="regular-text" required /></td>
                                </tr>
                                <tr>
                                    <th><label for="auto_refresh"><?php _e('Auto Refresh', 'dooplay'); ?></label></th>
                                    <td>
                                        <label>
                                            <input type="checkbox" id="auto_refresh" name="auto_refresh" value="1" checked />
                                            <?php _e('Automatically refresh this playlist daily', 'dooplay'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                            <p class="submit">
                                <input type="submit" name="add_playlist" class="button-primary" value="<?php _e('Add Playlist', 'dooplay'); ?>" />
                            </p>
                        </form>
                    </div>

                    <!-- Active Playlists List -->
                    <div class="active-playlists-section">
                        <h3><?php _e('Active Playlists', 'dooplay'); ?></h3>
                        <?php dooplay_display_active_playlists(); ?>
                    </div>
                </div>
            </div>

            <div class="admin-sidebar">
                <?php dooplay_display_channel_stats(); ?>

                <!-- Auto Refresh Status -->
                <div class="card">
                    <h2><i class="dashicons dashicons-update"></i> <?php _e('Auto Refresh Status', 'dooplay'); ?></h2>
                    <?php
                    $last_refresh = get_option('dooplay_last_refresh', 0);
                    $next_refresh = wp_next_scheduled('dooplay_daily_channel_refresh');
                    ?>
                    <div class="refresh-status">
                        <div class="status-item">
                            <strong><?php _e('Last Refresh:', 'dooplay'); ?></strong><br>
                            <?php echo $last_refresh ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $last_refresh) : __('Never', 'dooplay'); ?>
                        </div>
                        <div class="status-item">
                            <strong><?php _e('Next Refresh:', 'dooplay'); ?></strong><br>
                            <?php echo $next_refresh ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $next_refresh) : __('Not scheduled', 'dooplay'); ?>
                        </div>
                        <div class="status-item">
                            <strong><?php _e('Refresh Frequency:', 'dooplay'); ?></strong><br>
                            <?php _e('Every 24 hours', 'dooplay'); ?>
                        </div>
                    </div>

                    <div class="manual-refresh">
                        <form method="post" style="margin-top: 15px;">
                            <?php wp_nonce_field('manual_refresh_action'); ?>
                            <input type="submit" name="manual_refresh" class="button-secondary"
                                   value="<?php _e('Force Refresh Now', 'dooplay'); ?>"
                                   onclick="return confirm('<?php _e('This will test all channels and refresh active playlists. Continue?', 'dooplay'); ?>')" />
                        </form>
                    </div>
                </div>

                <div class="card">
                    <h2><i class="dashicons dashicons-admin-links"></i> <?php _e('Quick Links', 'dooplay'); ?></h2>
                    <div class="quick-links">
                        <a href="<?php echo admin_url('post-new.php?post_type=live_tv_channels'); ?>" class="button button-secondary">
                            <i class="dashicons dashicons-plus-alt"></i>
                            <?php _e('Add New Channel', 'dooplay'); ?>
                        </a>
                        <a href="<?php echo admin_url('edit.php?post_type=live_tv_channels'); ?>" class="button button-secondary">
                            <i class="dashicons dashicons-list-view"></i>
                            <?php _e('View All Channels', 'dooplay'); ?>
                        </a>
                        <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="button button-secondary" target="_blank">
                            <i class="dashicons dashicons-visibility"></i>
                            <?php _e('View Live TV Page', 'dooplay'); ?>
                        </a>
                        <a href="<?php echo admin_url('options-general.php?page=live-tv-settings'); ?>" class="button button-secondary">
                            <i class="dashicons dashicons-admin-settings"></i>
                            <?php _e('Settings', 'dooplay'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    .live-tv-admin-grid {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 20px;
        margin-top: 20px;
    }

    .admin-main-content .card,
    .admin-sidebar .card {
        background: white;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    }

    .card h2 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1.3em;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .management-tools {
        display: grid;
        gap: 20px;
    }

    .tool-item {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #e74c3c;
    }

    .tool-item h3 {
        margin: 0 0 8px 0;
        color: #2c3e50;
    }

    .tool-item p {
        margin: 0 0 10px 0;
        color: #666;
        font-size: 0.95em;
    }

    .button-delete {
        background: #dc3545 !important;
        border-color: #dc3545 !important;
        color: white !important;
    }

    .button-delete:hover {
        background: #c82333 !important;
        border-color: #bd2130 !important;
    }

    .quick-links {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .quick-links .button {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: flex-start;
        text-decoration: none;
    }

    @media (max-width: 1200px) {
        .live-tv-admin-grid {
            grid-template-columns: 1fr;
        }

        .admin-sidebar {
            order: -1;
        }
    }

    /* Progress Bar Styles */
    .progress-bar {
        width: 100%;
        height: 20px;
        background: #f0f0f0;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #e74c3c, #c0392b);
        width: 0%;
        transition: width 0.3s ease;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    /* Modal Styles */
    .live-tv-admin-modal {
        display: none;
        position: fixed;
        z-index: 100000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.7);
    }

    .modal-content-admin {
        background: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        position: relative;
    }

    .modal-close-admin {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .modal-close-admin:hover {
        color: #333;
    }

    /* Playlist Management Styles */
    .add-playlist-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
        border-left: 4px solid #e74c3c;
    }

    .add-playlist-form .form-table th {
        width: 150px;
        font-weight: 600;
    }

    .playlists-table-container {
        overflow-x: auto;
        margin-top: 15px;
    }

    .playlists-table {
        border-collapse: collapse;
        width: 100%;
    }

    .playlists-table th,
    .playlists-table td {
        padding: 12px 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    .playlists-table th {
        background: #f1f1f1;
        font-weight: 600;
    }

    .playlists-table code {
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.85em;
        color: #666;
    }

    .status-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .channel-count {
        background: #e74c3c;
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.85em;
        font-weight: bold;
    }

    .actions-cell {
        white-space: nowrap;
    }

    .actions-cell .button {
        margin-right: 5px;
        padding: 4px 8px;
        font-size: 0.85em;
    }

    .refresh-status {
        display: grid;
        gap: 15px;
        margin-bottom: 15px;
    }

    .status-item {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #e74c3c;
    }

    .status-item strong {
        color: #2c3e50;
    }

    .manual-refresh {
        text-align: center;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }

    @media (max-width: 768px) {
        .playlists-table {
            font-size: 0.9em;
        }

        .playlists-table th,
        .playlists-table td {
            padding: 8px 4px;
        }

        .actions-cell .button {
            display: block;
            margin: 2px 0;
            width: 100%;
        }
    }
    </style>

    <!-- Bulk Category Modal -->
    <div id="bulk-category-modal" class="live-tv-admin-modal">
        <div class="modal-content-admin">
            <span class="modal-close-admin" onclick="closeBulkCategoryModal()">&times;</span>
            <h2><?php _e('Bulk Category Update', 'dooplay'); ?></h2>
            <p><?php _e('This will automatically categorize channels based on keywords in their names.', 'dooplay'); ?></p>
            <div id="bulk-category-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p class="progress-text"><?php _e('Updating categories...', 'dooplay'); ?></p>
            </div>
            <div class="modal-actions">
                <button type="button" class="button-primary" onclick="startBulkCategoryUpdate()"><?php _e('Start Update', 'dooplay'); ?></button>
                <button type="button" class="button-secondary" onclick="closeBulkCategoryModal()"><?php _e('Cancel', 'dooplay'); ?></button>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div id="import-modal" class="live-tv-admin-modal">
        <div class="modal-content-admin">
            <span class="modal-close-admin" onclick="closeImportModal()">&times;</span>
            <h2><?php _e('Import Channel Backup', 'dooplay'); ?></h2>
            <form id="import-form" enctype="multipart/form-data">
                <p>
                    <label for="import-file"><?php _e('Select JSON backup file:', 'dooplay'); ?></label><br>
                    <input type="file" id="import-file" name="import_file" accept=".json" required />
                </p>
                <div class="modal-actions">
                    <button type="submit" class="button-primary"><?php _e('Import Channels', 'dooplay'); ?></button>
                    <button type="button" class="button-secondary" onclick="closeImportModal()"><?php _e('Cancel', 'dooplay'); ?></button>
                </div>
            </form>
        </div>
    </div>

    <script>
    // Bulk Category Modal Functions
    function openBulkCategoryModal() {
        document.getElementById('bulk-category-modal').style.display = 'block';
    }

    function closeBulkCategoryModal() {
        document.getElementById('bulk-category-modal').style.display = 'none';
    }

    function startBulkCategoryUpdate() {
        const progressDiv = document.getElementById('bulk-category-progress');
        progressDiv.style.display = 'block';

        jQuery.post(ajaxurl, {
            action: 'bulk_category_update',
            nonce: '<?php echo wp_create_nonce('bulk_category_update'); ?>'
        }, function(response) {
            if (response.success) {
                alert('<?php _e("Categories updated for", "dooplay"); ?> ' + response.data.updated + ' <?php _e("channels", "dooplay"); ?>');
                location.reload();
            } else {
                alert('<?php _e("Error updating categories", "dooplay"); ?>');
            }
            progressDiv.style.display = 'none';
        });
    }

    // Import Modal Functions
    function openImportModal() {
        document.getElementById('import-modal').style.display = 'block';
    }

    function closeImportModal() {
        document.getElementById('import-modal').style.display = 'none';
    }

    // Close modals when clicking outside
    window.onclick = function(event) {
        const bulkModal = document.getElementById('bulk-category-modal');
        const importModal = document.getElementById('import-modal');

        if (event.target === bulkModal) {
            closeBulkCategoryModal();
        }
        if (event.target === importModal) {
            closeImportModal();
        }
    }
    </script>
    <?php
}

// Test all channels and update status
function dooplay_test_all_channels() {
    $channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));

    foreach ($channels as $channel) {
        $stream_url = get_post_meta($channel->ID, '_live_tv_stream_url', true);
        if (!empty($stream_url)) {
            $is_working = dooplay_test_stream_url($stream_url);
            update_post_meta($channel->ID, '_live_tv_is_active', $is_working ? '1' : '0');
        }
    }
}

// Remove inactive channels
function dooplay_remove_inactive_channels() {
    $inactive_channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'meta_query' => array(
            array(
                'key' => '_live_tv_is_active',
                'value' => '0',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));

    $removed = 0;
    foreach ($inactive_channels as $channel_id) {
        if (wp_delete_post($channel_id, true)) {
            $removed++;
        }
    }

    return $removed;
}

// Display channel statistics
function dooplay_display_channel_stats() {
    $total_channels = wp_count_posts('live_tv_channels')->publish;
    $active_channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'meta_query' => array(
            array(
                'key' => '_live_tv_is_active',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));

    $categories = array();
    $all_channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => -1
    ));

    foreach ($all_channels as $channel) {
        $category = get_post_meta($channel->ID, '_live_tv_category', true);
        if (!isset($categories[$category])) {
            $categories[$category] = 0;
        }
        $categories[$category]++;
    }

    ?>
    <div class="card">
        <h2><i class="dashicons dashicons-chart-bar"></i> <?php _e('Channel Statistics', 'dooplay'); ?></h2>

        <div class="stats-overview">
            <div class="stat-box total">
                <div class="stat-number"><?php echo $total_channels; ?></div>
                <div class="stat-label"><?php _e('Total Channels', 'dooplay'); ?></div>
            </div>
            <div class="stat-box active">
                <div class="stat-number"><?php echo count($active_channels); ?></div>
                <div class="stat-label"><?php _e('Active Channels', 'dooplay'); ?></div>
            </div>
            <div class="stat-box inactive">
                <div class="stat-number"><?php echo $total_channels - count($active_channels); ?></div>
                <div class="stat-label"><?php _e('Inactive Channels', 'dooplay'); ?></div>
            </div>
        </div>

        <h3><?php _e('Channels by Category', 'dooplay'); ?></h3>
        <div class="category-stats">
            <?php foreach ($categories as $category => $count): ?>
            <div class="category-stat">
                <span class="category-name"><?php echo ucfirst($category); ?></span>
                <span class="category-count"><?php echo $count; ?></span>
                <div class="category-bar">
                    <div class="category-fill" style="width: <?php echo $total_channels > 0 ? ($count / $total_channels * 100) : 0; ?>%"></div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php if ($total_channels > 0): ?>
        <div class="stats-summary">
            <p><strong><?php _e('Success Rate:', 'dooplay'); ?></strong>
               <?php echo $total_channels > 0 ? round((count($active_channels) / $total_channels) * 100, 1) : 0; ?>%</p>
            <p><strong><?php _e('Last Updated:', 'dooplay'); ?></strong>
               <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format')); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <style>
    .stats-overview {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        margin-bottom: 20px;
    }

    .stat-box {
        text-align: center;
        padding: 15px 10px;
        border-radius: 4px;
        border-left: 4px solid;
    }

    .stat-box.total {
        background: #f8f9fa;
        border-left-color: #6c757d;
    }

    .stat-box.active {
        background: #d4edda;
        border-left-color: #28a745;
    }

    .stat-box.inactive {
        background: #f8d7da;
        border-left-color: #dc3545;
    }

    .stat-number {
        font-size: 1.8em;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 5px;
    }

    .stat-box.total .stat-number { color: #6c757d; }
    .stat-box.active .stat-number { color: #28a745; }
    .stat-box.inactive .stat-number { color: #dc3545; }

    .stat-label {
        font-size: 0.85em;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .category-stats {
        margin-bottom: 15px;
    }

    .category-stat {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9em;
    }

    .category-name {
        flex: 1;
        font-weight: 500;
        text-transform: capitalize;
    }

    .category-count {
        margin: 0 10px;
        font-weight: bold;
        color: #e74c3c;
        min-width: 30px;
        text-align: right;
    }

    .category-bar {
        flex: 1;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }

    .category-fill {
        height: 100%;
        background: linear-gradient(90deg, #e74c3c, #c0392b);
        transition: width 0.3s ease;
    }

    .stats-summary {
        padding-top: 15px;
        border-top: 1px solid #eee;
        font-size: 0.9em;
    }

    .stats-summary p {
        margin: 5px 0;
        color: #666;
    }

    @media (max-width: 600px) {
        .stats-overview {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .category-stat {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }

        .category-bar {
            width: 100%;
        }
    }
    </style>
    <?php
}

// Enqueue Live TV scripts and styles
function dooplay_enqueue_live_tv_assets() {
    if (is_post_type_archive('live_tv_channels') || is_singular('live_tv_channels')) {
        // Enqueue HLS.js library
        wp_enqueue_script('hls-js', 'https://cdn.jsdelivr.net/npm/hls.js@latest', array(), '1.4.10', true);

        // Enqueue custom Live TV script
        wp_enqueue_script('dooplay-live-tv', get_template_directory_uri() . '/assets/js/live-tv.js', array('jquery', 'hls-js'), '1.0.0', true);

        // Add custom CSS for Live TV menu
        wp_add_inline_style('dooplay-style', '
            /* Live TV Menu Styles for WordPress Menu System */
            .main-header .live-tv-menu-item {
                position: relative;
            }

            .main-header .live-tv-menu-item .live-tv-menu-link {
                display: flex !important;
                align-items: center;
                gap: 8px;
                padding: 8px 15px !important;
                background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
                color: white !important;
                text-decoration: none !important;
                border-radius: 20px;
                font-weight: 500;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
                margin: 0 10px;
            }

            .main-header .live-tv-menu-item .live-tv-menu-link:hover {
                background: linear-gradient(135deg, #c0392b, #a93226) !important;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
                color: white !important;
                text-decoration: none !important;
            }

            .main-header .live-tv-menu-item .live-tv-menu-link i {
                font-size: 1.1em;
                margin-right: 5px;
            }

            /* Responsive Menu Styles */
            .resp .live-tv-menu-item .live-tv-menu-link {
                display: flex !important;
                align-items: center;
                gap: 10px;
                padding: 12px 15px !important;
                background: #e74c3c !important;
                color: white !important;
                border-radius: 5px;
                margin: 5px 0 !important;
                text-decoration: none !important;
            }

            .resp .live-tv-menu-item .live-tv-menu-link:hover {
                background: #c0392b !important;
                color: white !important;
                text-decoration: none !important;
            }

            .resp .live-tv-menu-item .live-tv-menu-link i {
                font-size: 1.1em;
            }

            /* Ensure proper display */
            .main-header .live-tv-menu-item,
            .resp .live-tv-menu-item {
                list-style: none !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* Fix for theme compatibility */
            .main-header .menu-item.live-tv-menu-item a,
            .resp .menu-item.live-tv-menu-item a {
                background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
                color: white !important;
                border-radius: 20px !important;
                padding: 8px 15px !important;
                margin: 0 5px !important;
            }

            .main-header .menu-item.live-tv-menu-item a:hover,
            .resp .menu-item.live-tv-menu-item a:hover {
                background: linear-gradient(135deg, #c0392b, #a93226) !important;
                color: white !important;
                transform: translateY(-1px);
            }
        ');
    }
}
add_action('wp_enqueue_scripts', 'dooplay_enqueue_live_tv_assets');

// Add rewrite rules for Live TV
function dooplay_live_tv_rewrite_rules() {
    add_rewrite_rule('^live-tv/?$', 'index.php?post_type=live_tv_channels', 'top');
    add_rewrite_rule('^live-tv/page/([0-9]+)/?$', 'index.php?post_type=live_tv_channels&paged=$matches[1]', 'top');
}
add_action('init', 'dooplay_live_tv_rewrite_rules');

// Flush rewrite rules on theme activation
function dooplay_flush_rewrite_rules() {
    dooplay_live_tv_rewrite_rules();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'dooplay_flush_rewrite_rules');

// AJAX handler for M3U import
function dooplay_ajax_import_m3u() {
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'dooplay_import_m3u')) {
        wp_die('Security check failed');
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $playlist_url = sanitize_url($_POST['playlist_url']);

    if (empty($playlist_url)) {
        wp_send_json_error('Invalid playlist URL');
    }

    $result = dooplay_import_m3u_channels($playlist_url);

    if (isset($result['error'])) {
        wp_send_json_error($result['error']);
    } else {
        wp_send_json_success($result);
    }
}
add_action('wp_ajax_dooplay_import_m3u', 'dooplay_ajax_import_m3u');

// AJAX handler for channel testing
function dooplay_ajax_test_channels() {
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'dooplay_test_channels')) {
        wp_die('Security check failed');
    }

    // Check permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    dooplay_test_all_channels();

    wp_send_json_success('Channel testing completed');
}
add_action('wp_ajax_dooplay_test_channels', 'dooplay_ajax_test_channels');

// AJAX handler for checking import status
function dooplay_ajax_check_import_status() {
    if (!wp_verify_nonce($_POST['nonce'], 'dooplay_check_import_status')) {
        wp_die('Security check failed');
    }

    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $import_id = sanitize_text_field($_POST['import_id']);
    $import_data = get_transient('dooplay_import_' . $import_id);

    if (!$import_data) {
        wp_send_json_error('Import not found');
    }

    wp_send_json_success($import_data);
}
add_action('wp_ajax_dooplay_check_import_status', 'dooplay_ajax_check_import_status');

// Export channels to JSON
function dooplay_export_channels() {
    if (!wp_verify_nonce($_GET['nonce'], 'export_channels')) {
        wp_die('Security check failed');
    }

    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));

    $export_data = array();
    foreach ($channels as $channel) {
        $export_data[] = array(
            'title' => $channel->post_title,
            'content' => $channel->post_content,
            'stream_url' => get_post_meta($channel->ID, '_live_tv_stream_url', true),
            'logo' => get_post_meta($channel->ID, '_live_tv_channel_logo', true),
            'category' => get_post_meta($channel->ID, '_live_tv_category', true),
            'is_active' => get_post_meta($channel->ID, '_live_tv_is_active', true)
        );
    }

    $filename = 'live-tv-channels-' . date('Y-m-d-H-i-s') . '.json';

    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    echo json_encode($export_data, JSON_PRETTY_PRINT);
    exit;
}
add_action('wp_ajax_export_channels', 'dooplay_export_channels');

// Bulk category update
function dooplay_bulk_category_update() {
    if (!wp_verify_nonce($_POST['nonce'], 'bulk_category_update')) {
        wp_die('Security check failed');
    }

    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    $keywords = array(
        'news' => array('news', 'cnn', 'bbc', 'fox', 'msnbc', 'cnbc', 'sky news'),
        'sports' => array('sports', 'espn', 'fox sports', 'sky sports', 'star sports', 'football', 'cricket'),
        'entertainment' => array('entertainment', 'movie', 'drama', 'comedy', 'thriller'),
        'music' => array('music', 'mtv', 'vh1', 'music video'),
        'kids' => array('kids', 'cartoon', 'disney', 'nickelodeon', 'children'),
        'religious' => array('religious', 'islam', 'christian', 'hindu', 'peace tv', 'iqra')
    );

    $updated = 0;
    $channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => -1
    ));

    foreach ($channels as $channel) {
        $title = strtolower($channel->post_title);
        $current_category = get_post_meta($channel->ID, '_live_tv_category', true);

        if (empty($current_category) || $current_category === 'other') {
            foreach ($keywords as $category => $words) {
                foreach ($words as $word) {
                    if (strpos($title, $word) !== false) {
                        update_post_meta($channel->ID, '_live_tv_category', $category);
                        $updated++;
                        break 2;
                    }
                }
            }
        }
    }

    wp_send_json_success(array('updated' => $updated));
}
add_action('wp_ajax_bulk_category_update', 'dooplay_bulk_category_update');

// Playlist Management Functions
function dooplay_add_active_playlist($name, $url, $auto_refresh = true) {
    $playlists = get_option('dooplay_active_playlists', array());

    $playlist_id = 'playlist_' . time() . '_' . wp_rand(1000, 9999);

    $playlists[$playlist_id] = array(
        'name' => $name,
        'url' => $url,
        'auto_refresh' => $auto_refresh,
        'added' => time(),
        'last_refresh' => 0,
        'last_imported' => 0,
        'total_channels' => 0,
        'status' => 'active'
    );

    update_option('dooplay_active_playlists', $playlists);
    return $playlist_id;
}

function dooplay_remove_active_playlist($playlist_id) {
    $playlists = get_option('dooplay_active_playlists', array());

    if (isset($playlists[$playlist_id])) {
        unset($playlists[$playlist_id]);
        update_option('dooplay_active_playlists', $playlists);
        return true;
    }

    return false;
}

function dooplay_refresh_single_playlist($playlist_id) {
    $playlists = get_option('dooplay_active_playlists', array());

    if (!isset($playlists[$playlist_id])) {
        return false;
    }

    $playlist = $playlists[$playlist_id];
    $result = dooplay_import_m3u_channels_chunked($playlist['url']);

    // Update playlist stats
    $playlists[$playlist_id]['last_refresh'] = time();
    $playlists[$playlist_id]['last_imported'] = $result['imported'];
    $playlists[$playlist_id]['total_channels'] = $result['total'];

    update_option('dooplay_active_playlists', $playlists);

    return $result;
}

// Initialize default playlists
function dooplay_init_default_playlists() {
    $playlists = get_option('dooplay_active_playlists', array());

    if (empty($playlists)) {
        $default_playlists = array(
            array(
                'name' => 'PiratesTV Main',
                'url' => 'https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u',
                'auto_refresh' => true
            ),
            array(
                'name' => 'Free IPTV',
                'url' => 'https://raw.githubusercontent.com/iptv-org/iptv/master/streams/bd.m3u',
                'auto_refresh' => false
            ),
            array(
                'name' => 'Global IPTV',
                'url' => 'https://raw.githubusercontent.com/iptv-org/iptv/master/streams/in.m3u',
                'auto_refresh' => false
            )
        );

        foreach ($default_playlists as $playlist) {
            dooplay_add_active_playlist($playlist['name'], $playlist['url'], $playlist['auto_refresh']);
        }
    }

    if (isset($_POST['manual_refresh']) && wp_verify_nonce($_POST['_wpnonce'], 'manual_refresh_action')) {
        // Start background refresh
        wp_schedule_single_event(time() + 5, 'dooplay_daily_channel_refresh');
        echo '<div class="notice notice-info"><p>' . __('Manual refresh started in background! Check back in a few minutes.', 'dooplay') . '</p></div>';
    }
}
add_action('init', 'dooplay_init_default_playlists');

// AJAX Live Search for Channels
function dooplay_live_search_channels() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'live_search_nonce')) {
        wp_die('Security check failed');
    }

    $search_term = sanitize_text_field($_POST['search_term']);

    if (empty($search_term) || strlen($search_term) < 2) {
        wp_send_json_error('Search term too short');
    }

    // Search query
    $args = array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => 10,
        'post_status' => 'publish',
        's' => $search_term,
        'meta_query' => array(
            array(
                'key' => '_live_tv_is_active',
                'value' => '1',
                'compare' => '='
            )
        ),
        'orderby' => 'relevance'
    );

    $search_query = new WP_Query($args);
    $results = array();

    if ($search_query->have_posts()) {
        while ($search_query->have_posts()) {
            $search_query->the_post();

            $stream_url = get_post_meta(get_the_ID(), '_live_tv_stream_url', true);
            $channel_logo = get_post_meta(get_the_ID(), '_live_tv_channel_logo', true);
            $channel_category = get_post_meta(get_the_ID(), '_live_tv_category', true);

            // Get category display name
            $categories = array(
                'bangla' => __('বাংলা', 'dooplay'),
                'news' => __('News', 'dooplay'),
                'entertainment' => __('Entertainment', 'dooplay'),
                'sports' => __('Sports', 'dooplay'),
                'music' => __('Music', 'dooplay'),
                'kids' => __('Kids', 'dooplay'),
                'religious' => __('Religious', 'dooplay'),
                'other' => __('Other', 'dooplay')
            );

            $category_name = isset($categories[$channel_category]) ? $categories[$channel_category] : ucfirst($channel_category);

            $results[] = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'url' => get_permalink(),
                'stream_url' => $stream_url,
                'logo' => $channel_logo,
                'category' => $category_name,
                'category_key' => $channel_category
            );
        }
        wp_reset_postdata();
    }

    wp_send_json_success($results);
}

add_action('wp_ajax_live_search_channels', 'dooplay_live_search_channels');
add_action('wp_ajax_nopriv_live_search_channels', 'dooplay_live_search_channels');

// Enhanced pagination function for Live TV
function dooplay_live_tv_pagination($query = null, $show_info = true, $show_jump = true) {
    global $wp_query;

    if (!$query) {
        $query = $wp_query;
    }

    if ($query->max_num_pages <= 1) {
        return;
    }

    $current_page = max(1, get_query_var('paged'));
    $total_pages = $query->max_num_pages;
    $total_posts = $query->found_posts;
    $posts_per_page = get_query_var('posts_per_page') ?: 12;

    ?>
    <div class="live-tv-pagination enhanced-pagination">
        <?php if ($show_info): ?>
        <div class="pagination-info">
            <?php
            $start_post = (($current_page - 1) * $posts_per_page) + 1;
            $end_post = min($current_page * $posts_per_page, $total_posts);

            printf(
                __('Showing %d-%d of %d channels (Page %d of %d)', 'dooplay'),
                $start_post,
                $end_post,
                $total_posts,
                $current_page,
                $total_pages
            );
            ?>
        </div>
        <?php endif; ?>

        <div class="pagination-numbers">
            <?php
            echo paginate_links(array(
                'total' => $total_pages,
                'current' => $current_page,
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 2,
                'mid_size' => 3,
                'prev_next' => true,
                'prev_text' => '<i class="fas fa-chevron-left"></i> <span class="nav-text">' . __('Previous', 'dooplay') . '</span>',
                'next_text' => '<span class="nav-text">' . __('Next', 'dooplay') . '</span> <i class="fas fa-chevron-right"></i>',
                'type' => 'list',
                'before_page_number' => '<span class="screen-reader-text">' . __('Page', 'dooplay') . ' </span>',
                'add_args' => array_filter(array(
                    'category' => get_query_var('category'),
                    's' => get_query_var('s')
                ))
            ));
            ?>
        </div>

        <?php if ($show_jump && $total_pages > 5): ?>
        <div class="pagination-jump">
            <form method="get" class="jump-form">
                <?php
                // Preserve current query vars
                $preserve_vars = array('category', 's');
                foreach ($preserve_vars as $var) {
                    $value = get_query_var($var);
                    if ($value) {
                        echo '<input type="hidden" name="' . esc_attr($var) . '" value="' . esc_attr($value) . '" />';
                    }
                }
                ?>
                <label for="jump-page"><?php _e('Go to page:', 'dooplay'); ?></label>
                <input type="number" id="jump-page" name="paged" min="1" max="<?php echo $total_pages; ?>"
                       value="<?php echo $current_page; ?>" class="jump-input" />
                <button type="submit" class="jump-btn">
                    <i class="fas fa-arrow-right"></i>
                    <span><?php _e('Go', 'dooplay'); ?></span>
                </button>
            </form>
        </div>
        <?php endif; ?>

        <!-- Pagination Summary for Screen Readers -->
        <div class="pagination-summary screen-reader-text">
            <?php
            printf(
                __('Page %d of %d, showing %d channels per page', 'dooplay'),
                $current_page,
                $total_pages,
                $posts_per_page
            );
            ?>
        </div>
    </div>
    <?php
}

// Display active playlists in admin
function dooplay_display_active_playlists() {
    $playlists = get_option('dooplay_active_playlists', array());

    if (empty($playlists)) {
        echo '<p>' . __('No active playlists found.', 'dooplay') . '</p>';
        return;
    }

    echo '<div class="playlists-table-container">';
    echo '<table class="widefat playlists-table">';
    echo '<thead>';
    echo '<tr>';
    echo '<th>' . __('Name', 'dooplay') . '</th>';
    echo '<th>' . __('URL', 'dooplay') . '</th>';
    echo '<th>' . __('Auto Refresh', 'dooplay') . '</th>';
    echo '<th>' . __('Last Refresh', 'dooplay') . '</th>';
    echo '<th>' . __('Channels', 'dooplay') . '</th>';
    echo '<th>' . __('Actions', 'dooplay') . '</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($playlists as $playlist_id => $playlist) {
        echo '<tr>';
        echo '<td><strong>' . esc_html($playlist['name']) . '</strong></td>';
        echo '<td><code>' . esc_html(substr($playlist['url'], 0, 50)) . '...</code></td>';
        echo '<td>';
        if ($playlist['auto_refresh']) {
            echo '<span class="status-badge active">✓ ' . __('Enabled', 'dooplay') . '</span>';
        } else {
            echo '<span class="status-badge inactive">✗ ' . __('Disabled', 'dooplay') . '</span>';
        }
        echo '</td>';
        echo '<td>';
        if ($playlist['last_refresh']) {
            echo date_i18n(get_option('date_format'), $playlist['last_refresh']);
        } else {
            echo __('Never', 'dooplay');
        }
        echo '</td>';
        echo '<td>';
        if ($playlist['last_imported']) {
            echo '<span class="channel-count">' . $playlist['last_imported'] . '</span>';
        } else {
            echo '-';
        }
        echo '</td>';
        echo '<td class="actions-cell">';

        // Refresh button
        echo '<form method="post" style="display: inline;">';
        wp_nonce_field('refresh_playlist_action');
        echo '<input type="hidden" name="playlist_id" value="' . esc_attr($playlist_id) . '" />';
        echo '<input type="submit" name="refresh_playlist" class="button button-small" value="' . __('Refresh', 'dooplay') . '" />';
        echo '</form> ';

        // Remove button
        echo '<form method="post" style="display: inline;">';
        wp_nonce_field('remove_playlist_action');
        echo '<input type="hidden" name="playlist_id" value="' . esc_attr($playlist_id) . '" />';
        echo '<input type="submit" name="remove_playlist" class="button button-small button-link-delete" value="' . __('Remove', 'dooplay') . '" ';
        echo 'onclick="return confirm(\'' . __('Are you sure you want to remove this playlist?', 'dooplay') . '\')" />';
        echo '</form>';

        echo '</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';
    echo '</div>';
}

// Add default M3U playlist URL option
function dooplay_add_default_m3u_option() {
    $default_url = 'https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u';
    add_option('dooplay_default_m3u_url', $default_url);
}
add_action('init', 'dooplay_add_default_m3u_option');

// Scheduled channel testing (daily)
function dooplay_schedule_channel_testing() {
    if (!wp_next_scheduled('dooplay_daily_channel_test')) {
        wp_schedule_event(time(), 'daily', 'dooplay_daily_channel_test');
    }
}
add_action('wp', 'dooplay_schedule_channel_testing');

// Daily channel test hook
function dooplay_daily_channel_test() {
    dooplay_test_all_channels();
}
add_action('dooplay_daily_channel_test', 'dooplay_daily_channel_test');

// Enhanced daily refresh system
function dooplay_daily_channel_refresh() {
    // Test all channels and update status
    dooplay_test_all_channels();

    // Auto refresh from active playlists
    dooplay_auto_refresh_playlists();

    // Log the refresh activity
    update_option('dooplay_last_refresh', time());
}
add_action('dooplay_daily_channel_refresh', 'dooplay_daily_channel_refresh');

// Schedule enhanced daily refresh
function dooplay_schedule_enhanced_refresh() {
    if (!wp_next_scheduled('dooplay_daily_channel_refresh')) {
        wp_schedule_event(time(), 'daily', 'dooplay_daily_channel_refresh');
    }
}
add_action('wp', 'dooplay_schedule_enhanced_refresh');

// Auto refresh from active playlists
function dooplay_auto_refresh_playlists() {
    $active_playlists = get_option('dooplay_active_playlists', array());

    if (empty($active_playlists)) {
        return;
    }

    $refreshed = 0;
    foreach ($active_playlists as $playlist) {
        if ($playlist['auto_refresh']) {
            $result = dooplay_import_m3u_channels_chunked($playlist['url']);
            $refreshed++;

            // Update playlist stats
            $playlist['last_refresh'] = time();
            $playlist['last_imported'] = $result['imported'];

            // Small delay between playlists
            sleep(2);
        }
    }

    // Update active playlists with new stats
    update_option('dooplay_active_playlists', $active_playlists);

    return $refreshed;
}

// Auto import from default M3U (weekly)
function dooplay_schedule_auto_import() {
    if (!wp_next_scheduled('dooplay_weekly_auto_import')) {
        wp_schedule_event(time(), 'weekly', 'dooplay_weekly_auto_import');
    }
}
add_action('wp', 'dooplay_schedule_auto_import');

// Weekly auto import hook
function dooplay_weekly_auto_import() {
    $default_url = get_option('dooplay_default_m3u_url');
    if (!empty($default_url)) {
        dooplay_import_m3u_channels($default_url);
    }
}
add_action('dooplay_weekly_auto_import', 'dooplay_weekly_auto_import');

// Add Live TV widget
class Dooplay_Live_TV_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'dooplay_live_tv_widget',
            __('Live TV Channels', 'dooplay'),
            array('description' => __('Display live TV channels', 'dooplay'))
        );
    }

    public function widget($args, $instance) {
        $title = apply_filters('widget_title', $instance['title']);
        $limit = !empty($instance['limit']) ? $instance['limit'] : 5;
        $category = !empty($instance['category']) ? $instance['category'] : '';

        echo $args['before_widget'];

        if (!empty($title)) {
            echo $args['before_title'] . $title . $args['after_title'];
        }

        $query_args = array(
            'post_type' => 'live_tv_channels',
            'posts_per_page' => $limit,
            'meta_query' => array(
                array(
                    'key' => '_live_tv_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );

        if (!empty($category)) {
            $query_args['meta_query'][] = array(
                'key' => '_live_tv_category',
                'value' => $category,
                'compare' => '='
            );
        }

        $channels = new WP_Query($query_args);

        if ($channels->have_posts()) {
            echo '<div class="live-tv-widget">';
            while ($channels->have_posts()) {
                $channels->the_post();
                $logo = get_post_meta(get_the_ID(), '_live_tv_channel_logo', true);
                ?>
                <div class="widget-channel-item">
                    <a href="<?php the_permalink(); ?>" class="channel-link">
                        <?php if ($logo): ?>
                            <img src="<?php echo esc_url($logo); ?>" alt="<?php the_title(); ?>" class="channel-logo-small" />
                        <?php else: ?>
                            <div class="channel-icon"><i class="fas fa-tv"></i></div>
                        <?php endif; ?>
                        <span class="channel-name"><?php the_title(); ?></span>
                        <span class="live-badge"><?php _e('LIVE', 'dooplay'); ?></span>
                    </a>
                </div>
                <?php
            }
            echo '</div>';
            wp_reset_postdata();
        } else {
            echo '<p>' . __('No live channels available.', 'dooplay') . '</p>';
        }

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Live TV', 'dooplay');
        $limit = !empty($instance['limit']) ? $instance['limit'] : 5;
        $category = !empty($instance['category']) ? $instance['category'] : '';

        $categories = array(
            '' => __('All Categories', 'dooplay'),
            'news' => __('News', 'dooplay'),
            'entertainment' => __('Entertainment', 'dooplay'),
            'sports' => __('Sports', 'dooplay'),
            'music' => __('Music', 'dooplay'),
            'kids' => __('Kids', 'dooplay'),
            'religious' => __('Religious', 'dooplay'),
            'other' => __('Other', 'dooplay')
        );
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'dooplay'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>" />
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('limit'); ?>"><?php _e('Number of channels:', 'dooplay'); ?></label>
            <input class="tiny-text" id="<?php echo $this->get_field_id('limit'); ?>" name="<?php echo $this->get_field_name('limit'); ?>" type="number" step="1" min="1" value="<?php echo esc_attr($limit); ?>" size="3" />
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('category'); ?>"><?php _e('Category:', 'dooplay'); ?></label>
            <select class="widefat" id="<?php echo $this->get_field_id('category'); ?>" name="<?php echo $this->get_field_name('category'); ?>">
                <?php foreach ($categories as $value => $label): ?>
                    <option value="<?php echo esc_attr($value); ?>" <?php selected($category, $value); ?>><?php echo esc_html($label); ?></option>
                <?php endforeach; ?>
            </select>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? strip_tags($new_instance['title']) : '';
        $instance['limit'] = (!empty($new_instance['limit'])) ? absint($new_instance['limit']) : 5;
        $instance['category'] = (!empty($new_instance['category'])) ? sanitize_text_field($new_instance['category']) : '';

        return $instance;
    }
}

// Register Live TV widget
function dooplay_register_live_tv_widget() {
    register_widget('Dooplay_Live_TV_Widget');
}
add_action('widgets_init', 'dooplay_register_live_tv_widget');

// Add Live TV widget styles
function dooplay_live_tv_widget_styles() {
    wp_add_inline_style('dooplay-style', '
        .live-tv-widget {
            margin: 0;
            padding: 0;
        }

        .widget-channel-item {
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .widget-channel-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .widget-channel-item .channel-link {
            display: flex;
            align-items: center;
            gap: 10px;
            text-decoration: none;
            color: #333;
            transition: color 0.3s ease;
        }

        .widget-channel-item .channel-link:hover {
            color: #e74c3c;
            text-decoration: none;
        }

        .channel-logo-small {
            width: 30px;
            height: 30px;
            object-fit: contain;
            border-radius: 4px;
            background: #f8f9fa;
            padding: 2px;
        }

        .channel-icon {
            width: 30px;
            height: 30px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 0.9em;
        }

        .channel-name {
            flex: 1;
            font-size: 0.9em;
            font-weight: 500;
        }

        .live-badge {
            background: #e74c3c;
            color: white;
            font-size: 0.7em;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
    ');
}
add_action('wp_enqueue_scripts', 'dooplay_live_tv_widget_styles');

// Live TV Shortcode
function dooplay_live_tv_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => 8,
        'category' => '',
        'columns' => 4,
        'show_category' => 'true'
    ), $atts, 'live_tv');

    $query_args = array(
        'post_type' => 'live_tv_channels',
        'posts_per_page' => intval($atts['limit']),
        'meta_query' => array(
            array(
                'key' => '_live_tv_is_active',
                'value' => '1',
                'compare' => '='
            )
        )
    );

    if (!empty($atts['category'])) {
        $query_args['meta_query'][] = array(
            'key' => '_live_tv_category',
            'value' => sanitize_text_field($atts['category']),
            'compare' => '='
        );
    }

    $channels = new WP_Query($query_args);

    if (!$channels->have_posts()) {
        return '<p>' . __('No live channels available.', 'dooplay') . '</p>';
    }

    $categories = array(
        'bangla' => __('বাংলা', 'dooplay'),
        'news' => __('News', 'dooplay'),
        'entertainment' => __('Entertainment', 'dooplay'),
        'sports' => __('Sports', 'dooplay'),
        'music' => __('Music', 'dooplay'),
        'kids' => __('Kids', 'dooplay'),
        'religious' => __('Religious', 'dooplay'),
        'other' => __('Other', 'dooplay')
    );

    $columns = max(1, min(6, intval($atts['columns'])));
    $column_class = 'shortcode-col-' . $columns;

    ob_start();
    ?>
    <div class="live-tv-shortcode">
        <div class="live-tv-shortcode-grid <?php echo esc_attr($column_class); ?>">
            <?php while ($channels->have_posts()): $channels->the_post(); ?>
                <?php
                $stream_url = get_post_meta(get_the_ID(), '_live_tv_stream_url', true);
                $channel_logo = get_post_meta(get_the_ID(), '_live_tv_channel_logo', true);
                $channel_category = get_post_meta(get_the_ID(), '_live_tv_category', true);
                ?>
                <div class="shortcode-channel-card" data-stream="<?php echo esc_attr($stream_url); ?>">
                    <div class="shortcode-channel-thumbnail">
                        <?php if ($channel_logo): ?>
                            <img src="<?php echo esc_url($channel_logo); ?>" alt="<?php the_title(); ?>" />
                        <?php else: ?>
                            <div class="shortcode-channel-placeholder">
                                <i class="fas fa-tv"></i>
                            </div>
                        <?php endif; ?>
                        <div class="shortcode-channel-overlay">
                            <div class="shortcode-play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="shortcode-channel-status">
                                <span class="shortcode-live-indicator">
                                    <i class="fas fa-circle"></i>
                                    <?php _e('LIVE', 'dooplay'); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="shortcode-channel-info">
                        <h4 class="shortcode-channel-title"><?php the_title(); ?></h4>
                        <?php if ($atts['show_category'] === 'true'): ?>
                        <span class="shortcode-channel-category">
                            <?php echo isset($categories[$channel_category]) ? $categories[$channel_category] : ucfirst($channel_category); ?>
                        </span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>

        <div class="shortcode-view-all">
            <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="shortcode-view-all-btn">
                <i class="fas fa-tv"></i>
                <?php _e('View All Channels', 'dooplay'); ?>
            </a>
        </div>
    </div>

    <style>
    .live-tv-shortcode {
        margin: 20px 0;
    }

    .live-tv-shortcode-grid {
        display: grid;
        gap: 20px;
        margin-bottom: 20px;
    }

    .shortcode-col-1 { grid-template-columns: 1fr; }
    .shortcode-col-2 { grid-template-columns: repeat(2, 1fr); }
    .shortcode-col-3 { grid-template-columns: repeat(3, 1fr); }
    .shortcode-col-4 { grid-template-columns: repeat(4, 1fr); }
    .shortcode-col-5 { grid-template-columns: repeat(5, 1fr); }
    .shortcode-col-6 { grid-template-columns: repeat(6, 1fr); }

    .shortcode-channel-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
    }

    .shortcode-channel-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .shortcode-channel-thumbnail {
        position: relative;
        height: 120px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .shortcode-channel-thumbnail img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .shortcode-channel-placeholder {
        font-size: 2em;
        color: #dee2e6;
    }

    .shortcode-channel-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .shortcode-channel-card:hover .shortcode-channel-overlay {
        opacity: 1;
    }

    .shortcode-play-button {
        background: #e74c3c;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2em;
    }

    .shortcode-channel-status {
        position: absolute;
        top: 8px;
        right: 8px;
    }

    .shortcode-live-indicator {
        background: #e74c3c;
        color: white;
        padding: 3px 6px;
        border-radius: 3px;
        font-size: 0.7em;
        font-weight: bold;
    }

    .shortcode-live-indicator i {
        animation: pulse 1.5s infinite;
        margin-right: 3px;
    }

    .shortcode-channel-info {
        padding: 12px;
    }

    .shortcode-channel-title {
        margin: 0 0 5px 0;
        font-size: 0.95em;
        font-weight: 600;
        color: #333;
        line-height: 1.3;
    }

    .shortcode-channel-category {
        color: #666;
        font-size: 0.8em;
        text-transform: capitalize;
    }

    .shortcode-view-all {
        text-align: center;
    }

    .shortcode-view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #e74c3c;
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .shortcode-view-all-btn:hover {
        background: #c0392b;
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .shortcode-col-3,
        .shortcode-col-4,
        .shortcode-col-5,
        .shortcode-col-6 {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .live-tv-shortcode-grid {
            grid-template-columns: 1fr !important;
        }
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const shortcodeCards = document.querySelectorAll('.shortcode-channel-card');
        shortcodeCards.forEach(function(card) {
            card.addEventListener('click', function() {
                const streamUrl = this.dataset.stream;
                const channelTitle = this.querySelector('.shortcode-channel-title').textContent;

                if (typeof playChannel === 'function' && streamUrl) {
                    playChannel(streamUrl, channelTitle);
                }
            });
        });
    });
    </script>
    <?php

    wp_reset_postdata();
    return ob_get_clean();
}
add_shortcode('live_tv', 'dooplay_live_tv_shortcode');

// Include Live TV admin settings
if (is_admin()) {
    require_once get_template_directory() . '/inc/live-tv-admin.php';
}

// Add Live TV to WordPress menu system
function dooplay_add_live_tv_to_menu($items, $args) {
    // Only add to header menu
    if ($args->theme_location == 'header') {
        $live_tv_url = get_post_type_archive_link('live_tv_channels');
        $live_tv_item = '<li class="menu-item live-tv-menu-item">
            <a href="' . esc_url($live_tv_url) . '" class="live-tv-menu-link">
                <i class="fas fa-tv"></i>
                <span>' . __('Live TV', 'dooplay') . '</span>
            </a>
        </li>';
        $items .= $live_tv_item;
    }
    return $items;
}
add_filter('wp_nav_menu_items', 'dooplay_add_live_tv_to_menu', 10, 2);

// Fix rewrite rules and flush on activation
function dooplay_live_tv_activation() {
    // Register post type first
    dooplay_register_live_tv_post_type();

    // Add rewrite rules
    add_rewrite_rule('^live-tv/?$', 'index.php?post_type=live_tv_channels', 'top');
    add_rewrite_rule('^live-tv/page/([0-9]+)/?$', 'index.php?post_type=live_tv_channels&paged=$matches[1]', 'top');

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Hook activation
function dooplay_live_tv_init_activation() {
    if (!get_option('dooplay_live_tv_activated')) {
        dooplay_live_tv_activation();
        update_option('dooplay_live_tv_activated', true);
    }
}
add_action('init', 'dooplay_live_tv_init_activation');

// Ensure proper URL structure
function dooplay_live_tv_template_redirect() {
    global $wp_query;

    // Handle /live-tv/ URL
    if (isset($_SERVER['REQUEST_URI']) && $_SERVER['REQUEST_URI'] === '/live-tv/' || $_SERVER['REQUEST_URI'] === '/live-tv') {
        $wp_query->set('post_type', 'live_tv_channels');
        $wp_query->is_home = false;
        $wp_query->is_archive = true;
        $wp_query->is_post_type_archive = true;
    }
}
add_action('template_redirect', 'dooplay_live_tv_template_redirect');

// Add custom query vars
function dooplay_live_tv_query_vars($vars) {
    $vars[] = 'live_tv_category';
    return $vars;
}
add_filter('query_vars', 'dooplay_live_tv_query_vars');

// Create demo channels on activation
function dooplay_create_demo_channels() {
    // Check if demo channels already created
    if (get_option('dooplay_demo_channels_created')) {
        return;
    }

    $demo_channels = array(
        array(
            'title' => 'BBC News',
            'url' => 'https://vs-hls-push-ww-live.akamaized.net/x=4/i=urn:bbc:pips:service:bbc_news_channel_hd/t=3840/v=pv14/b=5070016/main.m3u8',
            'logo' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/62/BBC_News_2019.svg/320px-BBC_News_2019.svg.png',
            'category' => 'news',
            'description' => 'BBC News provides trusted World and UK news as well as local and regional perspectives.'
        ),
        array(
            'title' => 'Al Jazeera English',
            'url' => 'https://live-hls-web-aje.getaj.net/AJE/01.m3u8',
            'logo' => 'https://upload.wikimedia.org/wikipedia/en/thumb/f/f2/Aljazeera_eng.svg/320px-Aljazeera_eng.svg.png',
            'category' => 'news',
            'description' => 'Al Jazeera English is an international 24-hour news channel.'
        ),
        array(
            'title' => 'NASA TV',
            'url' => 'https://ntv1.akamaized.net/hls/live/2014075/NASA-NTV1-HLS/master.m3u8',
            'logo' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e5/NASA_logo.svg/320px-NASA_logo.svg.png',
            'category' => 'entertainment',
            'description' => 'NASA TV provides live coverage of launches, spacewalks and other mission events.'
        ),
        array(
            'title' => 'Red Bull TV',
            'url' => 'https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8',
            'logo' => 'https://upload.wikimedia.org/wikipedia/en/thumb/d/d4/Red_Bull_TV_logo.svg/320px-Red_Bull_TV_logo.svg.png',
            'category' => 'sports',
            'description' => 'Red Bull TV features live events, documentaries and original programming.'
        )
    );

    foreach ($demo_channels as $channel) {
        // Check if channel already exists
        $existing = get_posts(array(
            'post_type' => 'live_tv_channels',
            'title' => $channel['title'],
            'posts_per_page' => 1
        ));

        if (empty($existing)) {
            $post_id = wp_insert_post(array(
                'post_title' => $channel['title'],
                'post_content' => $channel['description'],
                'post_type' => 'live_tv_channels',
                'post_status' => 'publish'
            ));

            if ($post_id) {
                update_post_meta($post_id, '_live_tv_stream_url', $channel['url']);
                update_post_meta($post_id, '_live_tv_channel_logo', $channel['logo']);
                update_post_meta($post_id, '_live_tv_category', $channel['category']);
                update_post_meta($post_id, '_live_tv_is_active', '1');
            }
        }
    }

    // Mark as created
    update_option('dooplay_demo_channels_created', true);
}

// Create demo channels on theme activation
function dooplay_live_tv_theme_activation() {
    dooplay_live_tv_activation();
    dooplay_create_demo_channels();
}
add_action('after_switch_theme', 'dooplay_live_tv_theme_activation');

// Also create on init if not created yet
add_action('init', 'dooplay_create_demo_channels');

// Admin notice for Live TV setup
function dooplay_live_tv_admin_notice() {
    $screen = get_current_screen();

    // Only show on dashboard and Live TV pages
    if ($screen->id !== 'dashboard' && $screen->post_type !== 'live_tv_channels') {
        return;
    }

    // Check if notice was dismissed
    if (get_option('dooplay_live_tv_notice_dismissed')) {
        return;
    }

    $total_channels = wp_count_posts('live_tv_channels')->publish;

    ?>
    <div class="notice notice-info is-dismissible" id="live-tv-setup-notice">
        <h3><i class="dashicons dashicons-video-alt3"></i> <?php _e('Live TV Feature Activated!', 'dooplay'); ?></h3>
        <p><strong><?php _e('Your Live TV system is ready to use.', 'dooplay'); ?></strong></p>

        <?php if ($total_channels > 0): ?>
            <p><?php printf(__('You have %d channels available. ', 'dooplay'), $total_channels); ?>
               <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" target="_blank"><?php _e('View Live TV Page', 'dooplay'); ?></a></p>
        <?php else: ?>
            <p><?php _e('Get started by importing channels from M3U playlist:', 'dooplay'); ?></p>
            <ol>
                <li><?php _e('Go to', 'dooplay'); ?> <a href="<?php echo admin_url('edit.php?post_type=live_tv_channels&page=live-tv-management'); ?>"><?php _e('Live TV > Channel Management', 'dooplay'); ?></a></li>
                <li><?php _e('Enter M3U URL:', 'dooplay'); ?> <code>https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u</code></li>
                <li><?php _e('Click "Import Channels"', 'dooplay'); ?></li>
            </ol>
        <?php endif; ?>

        <p><strong><?php _e('Quick Tips:', 'dooplay'); ?></strong></p>
        <ul>
            <li><?php _e('Use shortcode', 'dooplay'); ?> <code>[live_tv]</code> <?php _e('to display channels anywhere', 'dooplay'); ?></li>
            <li><?php _e('Add "Live TV Channels" widget to sidebars', 'dooplay'); ?></li>
            <li><?php _e('Configure settings in', 'dooplay'); ?> <a href="<?php echo admin_url('options-general.php?page=live-tv-settings'); ?>"><?php _e('Settings > Live TV Settings', 'dooplay'); ?></a></li>
        </ul>

        <p>
            <a href="<?php echo admin_url('edit.php?post_type=live_tv_channels&page=live-tv-management'); ?>" class="button button-primary"><?php _e('Manage Channels', 'dooplay'); ?></a>
            <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="button button-secondary" target="_blank"><?php _e('View Live TV', 'dooplay'); ?></a>
            <button type="button" class="button button-link" onclick="dismissLiveTVNotice()"><?php _e('Dismiss', 'dooplay'); ?></button>
        </p>
    </div>

    <script>
    function dismissLiveTVNotice() {
        jQuery.post(ajaxurl, {
            action: 'dismiss_live_tv_notice',
            nonce: '<?php echo wp_create_nonce('dismiss_live_tv_notice'); ?>'
        }, function() {
            jQuery('#live-tv-setup-notice').fadeOut();
        });
    }

    // Auto dismiss when clicking the X
    jQuery(document).on('click', '#live-tv-setup-notice .notice-dismiss', function() {
        dismissLiveTVNotice();
    });
    </script>

    <style>
    #live-tv-setup-notice h3 {
        margin-top: 0;
        color: #e74c3c;
    }

    #live-tv-setup-notice .dashicons {
        margin-right: 5px;
    }

    #live-tv-setup-notice code {
        background: #f1f1f1;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
    }

    #live-tv-setup-notice ol,
    #live-tv-setup-notice ul {
        margin-left: 20px;
    }
    </style>
    <?php
}
add_action('admin_notices', 'dooplay_live_tv_admin_notice');

// AJAX handler for dismissing notice
function dooplay_dismiss_live_tv_notice() {
    if (!wp_verify_nonce($_POST['nonce'], 'dismiss_live_tv_notice')) {
        wp_die('Security check failed');
    }

    update_option('dooplay_live_tv_notice_dismissed', true);
    wp_die();
}
add_action('wp_ajax_dismiss_live_tv_notice', 'dooplay_dismiss_live_tv_notice');

// Background Import System
function dooplay_start_background_import($url) {
    // Create a unique import ID
    $import_id = 'import_' . time() . '_' . wp_rand(1000, 9999);

    // Store import data
    set_transient('dooplay_import_' . $import_id, array(
        'url' => $url,
        'status' => 'pending',
        'started' => time(),
        'imported' => 0,
        'skipped' => 0,
        'total' => 0
    ), 3600); // 1 hour expiry

    // Schedule the import
    wp_schedule_single_event(time() + 5, 'dooplay_process_background_import', array($import_id));

    return $import_id;
}

// Process background import
function dooplay_process_background_import($import_id) {
    $import_data = get_transient('dooplay_import_' . $import_id);

    if (!$import_data) {
        return;
    }

    // Update status to processing
    $import_data['status'] = 'processing';
    set_transient('dooplay_import_' . $import_id, $import_data, 3600);

    // Increase limits for background processing
    ignore_user_abort(true);
    set_time_limit(0); // No time limit
    ini_set('memory_limit', '512M');

    // Process import in chunks
    $result = dooplay_import_m3u_channels_chunked($import_data['url']);

    // Update final status
    $import_data['status'] = 'completed';
    $import_data['imported'] = $result['imported'];
    $import_data['skipped'] = $result['skipped'];
    $import_data['total'] = $result['total'];
    $import_data['completed'] = time();

    set_transient('dooplay_import_' . $import_id, $import_data, 3600);
}
add_action('dooplay_process_background_import', 'dooplay_process_background_import');

// Chunked import function
function dooplay_import_m3u_channels_chunked($url) {
    $channels = dooplay_scrape_m3u_playlist($url);
    $imported = 0;
    $skipped = 0;
    $chunk_size = 20; // Process 20 channels at a time

    if (!$channels) {
        return array('error' => 'Failed to fetch playlist');
    }

    $chunks = array_chunk($channels, $chunk_size);

    foreach ($chunks as $chunk) {
        foreach ($chunk as $channel) {
            // Check if channel already exists
            $existing = get_posts(array(
                'post_type' => 'live_tv_channels',
                'meta_query' => array(
                    array(
                        'key' => '_live_tv_stream_url',
                        'value' => $channel['url'],
                        'compare' => '='
                    )
                ),
                'posts_per_page' => 1
            ));

            if (!empty($existing)) {
                $skipped++;
                continue;
            }

            // Import channel without testing
            $post_id = wp_insert_post(array(
                'post_title' => sanitize_text_field($channel['name']),
                'post_type' => 'live_tv_channels',
                'post_status' => 'publish',
                'post_content' => 'Live TV Channel imported from M3U playlist'
            ));

            if ($post_id) {
                update_post_meta($post_id, '_live_tv_stream_url', esc_url_raw($channel['url']));
                update_post_meta($post_id, '_live_tv_channel_logo', esc_url_raw($channel['logo']));
                update_post_meta($post_id, '_live_tv_category', sanitize_text_field($channel['category']));
                update_post_meta($post_id, '_live_tv_is_active', '1');
                $imported++;
            }
        }

        // Small delay between chunks to prevent server overload
        usleep(100000); // 0.1 second
    }

    return array(
        'imported' => $imported,
        'skipped' => $skipped,
        'total' => count($channels)
    );
}
define('DOO_THEME_SOCIAL_SHARE', true);
define('DOO_THEME_CACHE',        true);
define('DOO_THEME_PLAYERSERNAM', true);
define('DOO_THEME_JSCOMPRESS',   true);
define('DOO_THEME_TOTAL_POSTC',  true);
define('DOO_THEME_LAZYLOAD',     false);
# Repository data
define('DOO_COM','Doothemes');
define('DOO_VERSION','2.5.5');
define('Bes_VERSION','*******'); // Bescraper version for wp_update only 23/06/2021
define('DOO_VERSION_DB','2.8');
define('DOO_ITEM_ID','154');
define('DOO_PHP_REQUIRE','7.1');
define('DOO_THEME','Dooplay');
define('DOO_THEME_SLUG','dooplay');
define('DOO_SERVER','https://cdn.bescraper.cf/api');
define('DOO_GICO','https://s2.googleusercontent.com/s2/favicons?domain=');

# Configure Here date format #
define('DOO_TIME','M. d, Y');  // More Info >>> https://www.php.net/manual/function.date.php
##############################

# Define Rating data
define('DOO_MAIN_RATING','_starstruck_avg');
define('DOO_MAIN_VOTOS','_starstruck_total');
# Define Options key
define('DOO_OPTIONS','_dooplay_options');
define('DOO_CUSTOMIZE', '_dooplay_customize');
# Define template directory
define('DOO_URI',get_template_directory_uri());
define('DOO_DIR',get_template_directory());

# Translations
load_theme_textdomain('dooplay', DOO_DIR.'/lang/');

# Load Application
require get_parent_theme_file_path('/inc/doo_init.php');

/* Custom functions
========================================================
*/

// Advanced SEO Functions for Auto Content Generation
function dooplay_auto_generate_content($post_id) {
    $post = get_post($post_id);
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'dt_poster', true);
        $director = get_post_meta($post_id, 'director', true);
        $cast = get_post_meta($post_id, 'cast', true);
        $rating = get_post_meta($post_id, '_starstruck_avg', true);
        $votes = get_post_meta($post_id, '_starstruck_total', true);
        $genre = get_post_meta($post_id, 'genre', true);
        
        // Generate rich content
        $content = "<div class='movie-info-seo'>\n";
        $content .= "<h2>$title";
        if ($year) $content .= " ($year)";
        $content .= "</h2>\n";
        
        if ($director) {
            $content .= "<p><strong>Director:</strong> $director</p>\n";
        }
        
        if ($cast) {
            $content .= "<p><strong>Cast:</strong> $cast</p>\n";
        }
        
        if ($genre) {
            $content .= "<p><strong>Genre:</strong> $genre</p>\n";
        }
        
        if ($rating) {
            $content .= "<p><strong>Rating:</strong> $rating/5";
            if ($votes) $content .= " ($votes votes)";
            $content .= "</p>\n";
        }
        
        $content .= "<p><strong>Synopsis:</strong> ";
        $content .= "Watch $title online in HD quality. ";
        if ($year) $content .= "Released in $year, ";
        if ($director) $content .= "directed by $director, ";
        if ($cast) $content .= "starring $cast. ";
        $content .= "Enjoy streaming this amazing $genre movie with high quality video and audio.</p>\n";
        
        $content .= "</div>\n";
        
        // Add to post content if empty
        if (empty($post->post_content)) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $content
            ));
        }
    }
}

// Auto Internal Linking Function
function dooplay_auto_internal_links($content) {
    global $post;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        $title = get_the_title();
        $genre = get_post_meta($post_id, 'genre', true);
        $cast = get_post_meta($post_id, 'cast', true);
        
        // Find related posts for internal linking
        $related_posts = array();
        
        if ($genre) {
            $genre_posts = get_posts(array(
                'post_type' => array('movies', 'tvshows'),
                'meta_query' => array(
                    array('key' => 'genre', 'value' => $genre, 'compare' => 'LIKE')
                ),
                'posts_per_page' => 5,
                'post__not_in' => array($post_id)
            ));
            $related_posts = array_merge($related_posts, $genre_posts);
        }
        
        if ($cast) {
            $cast_array = explode(',', $cast);
            foreach ($cast_array as $actor) {
                $actor = trim($actor);
                $actor_posts = get_posts(array(
                    'post_type' => array('movies', 'tvshows'),
                    'meta_query' => array(
                        array('key' => 'cast', 'value' => $actor, 'compare' => 'LIKE')
                    ),
                    'posts_per_page' => 3,
                    'post__not_in' => array($post_id)
                ));
                $related_posts = array_merge($related_posts, $actor_posts);
            }
        }
        
        // Add internal links to content
        if (!empty($related_posts)) {
            $content .= "<div class='internal-links-seo'>\n";
            $content .= "<h3>Related Movies & TV Shows</h3>\n";
            $content .= "<ul>\n";
            
            foreach (array_slice($related_posts, 0, 10) as $related_post) {
                $related_title = get_the_title($related_post->ID);
                $related_url = get_permalink($related_post->ID);
                $content .= "<li><a href='$related_url' title='$related_title'>$related_title</a></li>\n";
            }
            
            $content .= "</ul>\n";
            $content .= "</div>\n";
        }
    }
    
    return $content;
}

// Hook for auto content generation
add_action('save_post', 'dooplay_auto_generate_content');

// Hook for auto internal linking
add_action('save_post', 'dooplay_auto_internal_links', 15, 1);

// Remove content filters that add visible content
remove_filter('the_content', 'dooplay_auto_internal_links');
remove_filter('the_content', 'dooplay_copyright_safe_content');

// Copyright Safe Content Function - DISABLED TO PREVENT DUPLICATES
function dooplay_copyright_safe_content($content) {
    // This function is disabled to prevent duplicate disclaimers
    return $content;
}

// Auto Generate Meta Description
function dooplay_auto_meta_description($post_id) {
    $post = get_post($post_id);
    $title = get_the_title($post_id);
    $excerpt = get_the_excerpt($post_id);
    
    if (empty($excerpt)) {
        $content = wp_strip_all_tags($post->post_content);
        $excerpt = wp_trim_words($content, 25, '...');
    }
    
    $meta_description = "Watch $title online in HD quality. $excerpt";
    
    update_post_meta($post_id, '_yoast_wpseo_metadesc', $meta_description);
}

// Hook for auto meta description
add_action('save_post', 'dooplay_auto_meta_description');

// Auto Generate Focus Keyphrase
function dooplay_auto_focus_keyphrase($post_id) {
    $title = get_the_title($post_id);
    $post_type = get_post_type($post_id);
    
    $keyphrase = $title;
    if ($post_type == 'movies') {
        $keyphrase .= " movie";
    } elseif ($post_type == 'tvshows') {
        $keyphrase .= " tv show";
    }
    
    update_post_meta($post_id, '_yoast_wpseo_focuskw', $keyphrase);
}

// Hook for auto focus keyphrase
add_action('save_post', 'dooplay_auto_focus_keyphrase');

// Auto Sitemap Generator
function dooplay_generate_sitemap() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // Homepage
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($movie->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($movie->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // TV Shows
    $tvshows = get_posts(array(
        'post_type' => 'tvshows',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($tvshows as $show) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($show->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($show->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    $sitemap .= "</urlset>";
    
    // Save sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
}

// Generate sitemap on post save
add_action('save_post', 'dooplay_generate_sitemap');

// Auto Submit to Search Engines
function dooplay_submit_to_search_engines($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $url = get_permalink($post_id);
        
        // Submit to Google (you need to add your Google Search Console URL)
        // wp_remote_post('https://www.google.com/ping?sitemap=' . home_url() . '/sitemap.xml');
        
        // Submit to Bing
        wp_remote_post('https://www.bing.com/ping?sitemap=' . home_url() . '/sitemap.xml');
    }
}

add_action('save_post', 'dooplay_submit_to_search_engines');

// Load SEO CSS
function dooplay_enqueue_seo_styles() {
    wp_enqueue_style('dooplay-seo-enhanced', get_template_directory_uri() . '/assets/css/seo-enhanced.css', array(), '1.0.0');
}
add_action('wp_enqueue_scripts', 'dooplay_enqueue_seo_styles');

// Advanced Search Optimization Functions
function dooplay_advanced_search_optimization($post_id) {
    // Check if post is being saved
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (defined('DOING_AJAX') && DOING_AJAX) return;
    if (wp_is_post_revision($post_id)) return;
    if (wp_is_post_autosave($post_id)) return;
    
    $post = get_post($post_id);
    if (!$post) return;
    
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        $director = get_post_meta($post_id, 'dt_dir', true);
        $cast = get_post_meta($post_id, 'dt_cast', true);
        $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
        $rating = get_post_meta($post_id, 'imdbRating', true);
        $runtime = get_post_meta($post_id, 'runtime', true);
        $country = get_post_meta($post_id, 'Country', true);
        
        // Generate multiple search variations
        $search_variations = array();
        
        // Original title
        $search_variations[] = $title;
        
        // Title with year
        if ($year) {
            $search_variations[] = "$title $year";
            $search_variations[] = "$title ($year)";
        }
        
        // Title with "movie" or "film"
        $search_variations[] = "$title movie";
        $search_variations[] = "$title film";
        $search_variations[] = "watch $title";
        $search_variations[] = "$title watch online";
        $search_variations[] = "$title streaming";
        
        // Title with "full movie"
        $search_variations[] = "$title full movie";
        $search_variations[] = "$title full film";
        
        // Director variations
        if ($director) {
            // Clean and format director data
            $director_entries = explode(']', $director);
            $formatted_director = '';
            
            foreach ($director_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $formatted_director = trim($parts[1]);
                        break; // Take first director only
                    }
                }
            }
            
            if ($formatted_director) {
                $search_variations[] = "$title $formatted_director";
                $search_variations[] = "$formatted_director $title";
                $search_variations[] = "movies by $formatted_director";
            }
        }
        
        // Cast variations
        if ($cast) {
            // Clean and format cast data
            $cast_entries = explode(']', $cast);
            $cast_names = array();
            
            foreach ($cast_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $actor_parts = explode(',', $parts[1]);
                        if (!empty($actor_parts[0])) {
                            $actor_name = trim($actor_parts[0]);
                            if (!in_array($actor_name, $cast_names)) {
                                $cast_names[] = $actor_name;
                            }
                        }
                    }
                }
            }
            
            // Add cast variations
            foreach ($cast_names as $actor) {
                $search_variations[] = "$actor movies";
                $search_variations[] = "$title $actor";
                $search_variations[] = "$actor $title";
            }
        }
        
        // Genre variations
        if ($genre) {
            $genre_array = explode(',', $genre);
            foreach ($genre_array as $g) {
                $g = trim($g);
                $search_variations[] = "$g movies";
                $search_variations[] = "$title $g";
                $search_variations[] = "$g $title";
            }
        }
        
        // Year variations
        if ($year) {
            $search_variations[] = "$year movies";
            $search_variations[] = "movies $year";
            $search_variations[] = "$title $year movie";
        }
        
        // Rating variations
        if ($rating) {
            $search_variations[] = "best movies $rating";
            $search_variations[] = "$title $rating rating";
        }
        
        // Country variations
        if ($country) {
            $search_variations[] = "$country movies";
            $search_variations[] = "$title $country";
        }
        
        // HD/Quality variations
        $search_variations[] = "$title HD";
        $search_variations[] = "$title 1080p";
        $search_variations[] = "$title 720p";
        $search_variations[] = "HD $title";
        
        // Online streaming variations
        $search_variations[] = "$title online";
        $search_variations[] = "$title stream";
        $search_variations[] = "stream $title";
        $search_variations[] = "$title free";
        $search_variations[] = "free $title";
        
        // Save search variations as meta
        update_post_meta($post_id, '_search_variations', $search_variations);
        
        // Generate LSI (Latent Semantic Indexing) keywords
        $lsi_keywords = array();
        $lsi_keywords[] = "cinema";
        $lsi_keywords[] = "entertainment";
        $lsi_keywords[] = "streaming";
        $lsi_keywords[] = "online";
        $lsi_keywords[] = "watch";
        $lsi_keywords[] = "view";
        $lsi_keywords[] = "download";
        $lsi_keywords[] = "HD";
        $lsi_keywords[] = "quality";
        $lsi_keywords[] = "full";
        $lsi_keywords[] = "complete";
        $lsi_keywords[] = "latest";
        $lsi_keywords[] = "new";
        $lsi_keywords[] = "popular";
        $lsi_keywords[] = "best";
        $lsi_keywords[] = "top";
        $lsi_keywords[] = "trending";
        $lsi_keywords[] = "blockbuster";
        $lsi_keywords[] = "hit";
        $lsi_keywords[] = "successful";
        $lsi_keywords[] = "award-winning";
        $lsi_keywords[] = "critically acclaimed";
        
        update_post_meta($post_id, '_lsi_keywords', $lsi_keywords);
    }
}

// Hook for advanced search optimization
add_action('save_post', 'dooplay_advanced_search_optimization', 10, 1);

// Enhanced Meta Tags for Better Search
function dooplay_enhanced_meta_tags() {
    global $post;
    
    if (!$post) return;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        if (!$post_id) return;
        
        $title = get_the_title();
        $search_variations = get_post_meta($post_id, '_search_variations', true);
        $lsi_keywords = get_post_meta($post_id, '_lsi_keywords', true);
        
        if ($search_variations && is_array($search_variations)) {
            echo "<meta name=\"keywords\" content=\"" . esc_attr(implode(', ', $search_variations)) . "\">\n";
        }
        
        if ($lsi_keywords && is_array($lsi_keywords)) {
            echo "<meta name=\"lsi-keywords\" content=\"" . esc_attr(implode(', ', $lsi_keywords)) . "\">\n";
        }
        
        // Add structured data for search
        echo "<script type=\"application/ld+json\">\n";
        echo "{\n";
        echo "  \"@context\": \"https://schema.org\",\n";
        echo "  \"@type\": \"WebPage\",\n";
        echo "  \"name\": \"" . esc_js($title) . "\",\n";
        echo "  \"description\": \"Watch " . esc_js($title) . " online in HD quality\",\n";
        echo "  \"url\": \"" . esc_url(get_permalink()) . "\",\n";
        if ($search_variations && is_array($search_variations)) {
            echo "  \"keywords\": \"" . esc_js(implode(', ', $search_variations)) . "\"\n";
        }
        echo "}\n";
        echo "</script>\n";
    }
}

// Remove wp_head actions that output visible content
remove_action('wp_head', 'dooplay_enhanced_meta_tags');

// Auto Generate Search-Friendly URLs
function dooplay_search_friendly_urls($post_id) {
    // Check if post is being saved
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (defined('DOING_AJAX') && DOING_AJAX) return;
    if (wp_is_post_revision($post_id)) return;
    if (wp_is_post_autosave($post_id)) return;
    
    $post = get_post($post_id);
    if (!$post) return;
    
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        
        // Create search-friendly slug
        $slug = sanitize_title($title);
        if ($year) {
            $slug .= "-$year";
        }
        
        // Add search terms to slug
        $slug .= "-movie-online";
        
        // Update post slug only if it's different
        $current_slug = $post->post_name;
        if ($current_slug !== $slug) {
            wp_update_post(array(
                'ID' => $post_id,
                'post_name' => $slug
            ));
        }
    }
}

add_action('save_post', 'dooplay_search_friendly_urls', 20, 1);

// Enhanced Content for Search - Modified to not output visible content
function dooplay_enhanced_search_content($content) {
    // This function now only works in background for SEO, no visible output
    return $content;
}

// Remove SEO/Download content from the_content filter
remove_filter('the_content', 'dooplay_enhanced_search_content', 10);

// Auto Generate Related Search Pages
function dooplay_generate_search_pages() {
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $title = get_the_title($movie->ID);
        $search_variations = get_post_meta($movie->ID, '_search_variations', true);
        
        if ($search_variations) {
            foreach (array_slice($search_variations, 0, 5) as $variation) {
                // Create search-friendly page for each variation
                $page_title = "$variation - Watch Online";
                $page_content = "Watch $title online. $variation available in HD quality.";
                
                // Check if page already exists
                $existing_page = get_page_by_title($page_title);
                if (!$existing_page) {
                    wp_insert_post(array(
                        'post_title' => $page_title,
                        'post_content' => $page_content,
                        'post_status' => 'publish',
                        'post_type' => 'page',
                        'post_name' => sanitize_title($variation)
                    ));
                }
            }
        }
    }
}

// Run search page generation weekly
if (!wp_next_scheduled('dooplay_search_pages_cron')) {
    wp_schedule_event(time(), 'weekly', 'dooplay_search_pages_cron');
}
add_action('dooplay_search_pages_cron', 'dooplay_generate_search_pages');

// Advanced Indexing and Copyright-Safe Functions
function dooplay_advanced_indexing_optimization($post_id) {
    $post = get_post($post_id);
    $post_type = get_post_type($post_id);
    
    if ($post_type == 'movies' || $post_type == 'tvshows') {
        $title = get_the_title($post_id);
        $year = get_post_meta($post_id, 'release_date', true);
        $director = get_post_meta($post_id, 'dt_dir', true);
        $cast = get_post_meta($post_id, 'dt_cast', true);
        $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
        $rating = get_post_meta($post_id, 'imdbRating', true);
        $runtime = get_post_meta($post_id, 'runtime', true);
        $country = get_post_meta($post_id, 'Country', true);
        
        // Generate download-focused search variations
        $download_variations = array();
        
        // Basic download variations
        $download_variations[] = "$title download";
        $download_variations[] = "download $title";
        $download_variations[] = "$title movie download";
        $download_variations[] = "$title film download";
        $download_variations[] = "$title free download";
        $download_variations[] = "free download $title";
        
        // Quality variations
        $download_variations[] = "$title HD download";
        $download_variations[] = "$title 1080p download";
        $download_variations[] = "$title 720p download";
        $download_variations[] = "$title BluRay download";
        $download_variations[] = "$title WEB-DL download";
        
        // Year variations
        if ($year) {
            $download_variations[] = "$title $year download";
            $download_variations[] = "download $title $year";
            $download_variations[] = "$title movie $year download";
        }
        
        // Director variations
        if ($director) {
            // Clean and format director data
            $director_entries = explode(']', $director);
            $formatted_director = '';
            
            foreach ($director_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $formatted_director = trim($parts[1]);
                        break; // Take first director only
                    }
                }
            }
            
            if ($formatted_director) {
                $download_variations[] = "$title $formatted_director download";
                $download_variations[] = "$formatted_director $title download";
            }
        }
        
        // Cast variations
        if ($cast) {
            // Clean and format cast data
            $cast_entries = explode(']', $cast);
            $cast_names = array();
            
            foreach ($cast_entries as $entry) {
                if (!empty($entry)) {
                    $entry = substr($entry, 1); // Remove leading [
                    $parts = explode(';', $entry);
                    if (isset($parts[1])) {
                        $actor_parts = explode(',', $parts[1]);
                        if (!empty($actor_parts[0])) {
                            $actor_name = trim($actor_parts[0]);
                            if (!in_array($actor_name, $cast_names)) {
                                $cast_names[] = $actor_name;
                            }
                        }
                    }
                }
            }
            
            // Add cast variations
            foreach ($cast_names as $actor) {
                $download_variations[] = "$title $actor download";
                $download_variations[] = "$actor movies download";
            }
        }
        
        // Genre variations
        if ($genre) {
            $genre_array = explode(',', $genre);
            foreach ($genre_array as $g) {
                $g = trim($g);
                $download_variations[] = "$g movies download";
                $download_variations[] = "$title $g download";
            }
        }
        
        // File format variations
        $download_variations[] = "$title MP4 download";
        $download_variations[] = "$title MKV download";
        $download_variations[] = "$title AVI download";
        $download_variations[] = "$title torrent download";
        $download_variations[] = "$title magnet download";
        
        // Size variations
        $download_variations[] = "$title 1GB download";
        $download_variations[] = "$title 2GB download";
        $download_variations[] = "$title 500MB download";
        
        // Language variations
        $download_variations[] = "$title English download";
        $download_variations[] = "$title Hindi download";
        $download_variations[] = "$title Bengali download";
        $download_variations[] = "$title dual audio download";
        
        // Save download variations
        update_post_meta($post_id, '_download_variations', $download_variations);
        
        // Generate copyright-safe content
        $copyright_safe_content = dooplay_generate_copyright_safe_content($post_id);
        update_post_meta($post_id, '_copyright_safe_content', $copyright_safe_content);
    }
}

// Generate copyright-safe content
function dooplay_generate_copyright_safe_content($post_id) {
    $title = get_the_title($post_id);
    $year = get_post_meta($post_id, 'release_date', true);
    $director = get_post_meta($post_id, 'dt_dir', true);
    $cast = get_post_meta($post_id, 'dt_cast', true);
    $genre = get_the_term_list($post_id, 'genres', '', ', ', '');
    $rating = get_post_meta($post_id, 'imdbRating', true);
    $runtime = get_post_meta($post_id, 'runtime', true);
    $country = get_post_meta($post_id, 'Country', true);
    
    $content = "<div class='copyright-safe-content'>\n";
    $content .= "<h2>Movie Information: $title</h2>\n";
    $content .= "<p><strong>Disclaimer:</strong> This page provides information about the movie <strong>$title</strong>. ";
    $content .= "We do not host, store, or distribute any copyrighted content. ";
    $content .= "All content is for informational purposes only.</p>\n";
    
    $content .= "<div class='movie-info-section'>\n";
    $content .= "<h3>Movie Details</h3>\n";
    $content .= "<ul>\n";
    if ($year) $content .= "<li><strong>Release Year:</strong> $year</li>\n";
    if ($director) $content .= "<li><strong>Director:</strong> $director</li>\n";
    if ($cast) $content .= "<li><strong>Cast:</strong> $cast</li>\n";
    if ($genre) $content .= "<li><strong>Genre:</strong> $genre</li>\n";
    if ($rating) $content .= "<li><strong>IMDb Rating:</strong> $rating</li>\n";
    if ($runtime) $content .= "<li><strong>Runtime:</strong> $runtime minutes</li>\n";
    if ($country) $content .= "<li><strong>Country:</strong> $country</li>\n";
    $content .= "</ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='legal-notice'>\n";
    $content .= "<h3>Legal Notice</h3>\n";
    $content .= "<p>This website provides movie information and reviews only. ";
    $content .= "We do not provide any download links or copyrighted content. ";
    $content .= "All movie rights belong to their respective owners. ";
    $content .= "Please support the film industry by watching movies through legal channels.</p>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='where-to-watch'>\n";
    $content .= "<h3>Where to Watch Legally</h3>\n";
    $content .= "<p>To watch <strong>$title</strong> legally, consider these options:</p>\n";
    $content .= "<ul>\n";
    $content .= "<li>Netflix</li>\n";
    $content .= "<li>Amazon Prime Video</li>\n";
    $content .= "<li>Disney+</li>\n";
    $content .= "<li>HBO Max</li>\n";
    $content .= "<li>Hulu</li>\n";
    $content .= "<li>Apple TV+</li>\n";
    $content .= "<li>Google Play Movies</li>\n";
    $content .= "<li>iTunes</li>\n";
    $content .= "<li>Your local cinema</li>\n";
    $content .= "<li>DVD/Blu-ray purchase</li>\n";
    $content .= "</ul>\n";
    $content .= "</div>\n";
    
    $content .= "<div class='movie-review'>\n";
    $content .= "<h3>Movie Review</h3>\n";
    $content .= "<p><strong>$title</strong> is a $genre film";
    if ($year) $content .= " released in $year";
    if ($director) $content .= " directed by $director";
    $content .= ". ";
    if ($rating) $content .= "With an IMDb rating of $rating, ";
    $content .= "this movie offers an engaging cinematic experience. ";
    if ($runtime) $content .= "The $runtime-minute runtime ";
    $content .= "provides a comprehensive storytelling experience.</p>\n";
    $content .= "</div>\n";
    
    $content .= "</div>\n";
    
    return $content;
}

// Hook for advanced indexing
add_action('save_post', 'dooplay_advanced_indexing_optimization');

// Enhanced Meta Tags for Download Searches
function dooplay_download_search_meta_tags() {
    global $post;
    
    if (is_single() && (get_post_type() == 'movies' || get_post_type() == 'tvshows')) {
        $post_id = get_the_ID();
        $title = get_the_title();
        $download_variations = get_post_meta($post_id, '_download_variations', true);
        
        if ($download_variations) {
            echo "<meta name=\"download-keywords\" content=\"" . implode(', ', $download_variations) . "\">\n";
        }
        
        // Add structured data for movie information
        echo "<script type=\"application/ld+json\">\n";
        echo "{\n";
        echo "  \"@context\": \"https://schema.org\",\n";
        echo "  \"@type\": \"Movie\",\n";
        echo "  \"name\": \"$title\",\n";
        echo "  \"description\": \"Movie information and details for $title\",\n";
        echo "  \"url\": \"" . get_permalink() . "\",\n";
        echo "  \"publisher\": {\n";
        echo "    \"@type\": \"Organization\",\n";
        echo "    \"name\": \"" . get_bloginfo('name') . "\"\n";
        echo "  }\n";
        echo "}\n";
        echo "</script>\n";
    }
}

// Remove wp_head actions that output visible content
remove_action('wp_head', 'dooplay_download_search_meta_tags');

// Auto Generate Download Search Pages
function dooplay_generate_download_search_pages() {
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $title = get_the_title($movie->ID);
        $download_variations = get_post_meta($movie->ID, '_download_variations', true);
        
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 10) as $variation) {
                // Create informational page for each variation
                $page_title = "$variation - Movie Information";
                $page_content = "Information about $title. This page provides movie details and information only. ";
                $page_content .= "We do not provide any download links or copyrighted content. ";
                $page_content .= "Please support the film industry by watching through legal channels.";
                
                // Check if page already exists
                $existing_page = get_page_by_title($page_title);
                if (!$existing_page) {
                    wp_insert_post(array(
                        'post_title' => $page_title,
                        'post_content' => $page_content,
                        'post_status' => 'publish',
                        'post_type' => 'page',
                        'post_name' => sanitize_title($variation)
                    ));
                }
            }
        }
    }
}

// Run download search page generation weekly
if (!wp_next_scheduled('dooplay_download_pages_cron')) {
    wp_schedule_event(time(), 'weekly', 'dooplay_download_pages_cron');
}
add_action('dooplay_download_pages_cron', 'dooplay_generate_download_search_pages');

// Enhanced Content for Download Searches - Modified to not output visible content  
function dooplay_download_search_content($content) {
    // This function now only works in background for SEO, no visible output
    return $content;
}

// Remove SEO/Download content from the_content filter
remove_filter('the_content', 'dooplay_download_search_content', 10);

// Force Google Indexing
function dooplay_force_google_indexing($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $url = get_permalink($post_id);
        
        // Submit to Google for indexing
        $google_url = "https://www.google.com/ping?sitemap=" . home_url() . "/sitemap.xml";
        wp_remote_get($google_url);
        
        // Submit to Bing
        $bing_url = "https://www.bing.com/ping?sitemap=" . home_url() . "/sitemap.xml";
        wp_remote_get($bing_url);
        
        // Submit to Yandex
        $yandex_url = "https://blogs.yandex.com/pings/?status=success&url=" . urlencode($url);
        wp_remote_get($yandex_url);
    }
}

add_action('save_post', 'dooplay_force_google_indexing');

// Enhanced Sitemap for Better Indexing
function dooplay_enhanced_sitemap() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // Homepage
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies with download variations
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    foreach ($movies as $movie) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . get_permalink($movie->ID) . "</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d', strtotime($movie->post_modified)) . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.9</priority>\n";
        $sitemap .= "  </url>\n";
        
        // Add download variation pages
        $download_variations = get_post_meta($movie->ID, '_download_variations', true);
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 5) as $variation) {
                $variation_url = home_url() . "/" . sanitize_title($variation) . "/";
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.7</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
    }
    
    $sitemap .= "</urlset>";
    
    // Save enhanced sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
}

add_action('save_post', 'dooplay_enhanced_sitemap');

// Advanced Sitemap Generator with All Features
function dooplay_advanced_sitemap_generator() {
    $sitemap = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap .= "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n";
    $sitemap .= "         xmlns:image=\"http://www.google.com/schemas/sitemap-image/1.1\"\n";
    $sitemap .= "         xmlns:video=\"http://www.google.com/schemas/sitemap-video/1.1\"\n";
    $sitemap .= "         xmlns:news=\"http://www.google.com/schemas/sitemap-news/0.9\">\n";
    
    // Homepage with high priority
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>1.0</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Movies with all variations
    $movies = get_posts(array(
        'post_type' => 'movies',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($movies as $movie) {
        $movie_id = $movie->ID;
        $movie_title = get_the_title($movie_id);
        $movie_url = get_permalink($movie_id);
        $movie_modified = get_the_modified_date('Y-m-d\TH:i:s+00:00', $movie_id);
        $movie_image = dbmovies_get_poster($movie_id, 'medium');
        $movie_year = get_post_meta($movie_id, 'release_date', true);
        $movie_director = get_post_meta($movie_id, 'dt_dir', true);
        $movie_cast = get_post_meta($movie_id, 'dt_cast', true);
        $movie_rating = get_post_meta($movie_id, 'imdbRating', true);
        $movie_runtime = get_post_meta($movie_id, 'runtime', true);
        $movie_genre = get_the_term_list($movie_id, 'genres', '', ', ', '');
        
        // Main movie page
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$movie_url</loc>\n";
        $sitemap .= "    <lastmod>$movie_modified</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.9</priority>\n";
        
        // Add image data
        if ($movie_image) {
            $sitemap .= "    <image:image>\n";
            $sitemap .= "      <image:loc>$movie_image</image:loc>\n";
            $sitemap .= "      <image:title>$movie_title</image:title>\n";
            $sitemap .= "      <image:caption>$movie_title Movie Poster</image:caption>\n";
            $sitemap .= "    </image:image>\n";
        }
        
        // Add video data
        $sitemap .= "    <video:video>\n";
        $sitemap .= "      <video:thumbnail_loc>$movie_image</video:thumbnail_loc>\n";
        $sitemap .= "      <video:title>$movie_title</video:title>\n";
        $sitemap .= "      <video:description>Watch $movie_title online in HD quality</video:description>\n";
        if ($movie_year) $sitemap .= "      <video:publication_date>$movie_year-01-01T00:00:00+00:00</video:publication_date>\n";
        if ($movie_director) $sitemap .= "      <video:family_friendly>yes</video:family_friendly>\n";
        $sitemap .= "      <video:duration>120</video:duration>\n";
        $sitemap .= "    </video:video>\n";
        
        $sitemap .= "  </url>\n";
        
        // Download variations
        $download_variations = get_post_meta($movie_id, '_download_variations', true);
        if ($download_variations) {
            foreach (array_slice($download_variations, 0, 10) as $variation) {
                $variation_slug = sanitize_title($variation);
                $variation_url = home_url() . "/$variation_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.7</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Search variations
        $search_variations = get_post_meta($movie_id, '_search_variations', true);
        if ($search_variations) {
            foreach (array_slice($search_variations, 0, 8) as $variation) {
                $variation_slug = sanitize_title($variation);
                $variation_url = home_url() . "/$variation_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$variation_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.6</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Director pages
        if ($movie_director) {
            $director_slug = sanitize_title($movie_director);
            $director_url = home_url() . "/director/$director_slug/";
            
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>$director_url</loc>\n";
            $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.5</priority>\n";
            $sitemap .= "  </url>\n";
        }
        
        // Cast pages
        if ($movie_cast) {
            $cast_array = explode(',', $movie_cast);
            foreach (array_slice($cast_array, 0, 5) as $actor) {
                $actor = trim($actor);
                $actor_slug = sanitize_title($actor);
                $actor_url = home_url() . "/actor/$actor_slug/";
                
                $sitemap .= "  <url>\n";
                $sitemap .= "    <loc>$actor_url</loc>\n";
                $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
                $sitemap .= "    <changefreq>monthly</changefreq>\n";
                $sitemap .= "    <priority>0.5</priority>\n";
                $sitemap .= "  </url>\n";
            }
        }
        
        // Year pages
        if ($movie_year) {
            $year_url = home_url() . "/year/$movie_year/";
            
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>$year_url</loc>\n";
            $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
            $sitemap .= "    <changefreq>monthly</changefreq>\n";
            $sitemap .= "    <priority>0.4</priority>\n";
            $sitemap .= "  </url>\n";
        }
    }
    
    // TV Shows
    $tvshows = get_posts(array(
        'post_type' => 'tvshows',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    foreach ($tvshows as $show) {
        $show_id = $show->ID;
        $show_title = get_the_title($show_id);
        $show_url = get_permalink($show_id);
        $show_modified = get_the_modified_date('Y-m-d\TH:i:s+00:00', $show_id);
        $show_image = dbmovies_get_poster($show_id, 'medium');
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$show_url</loc>\n";
        $sitemap .= "    <lastmod>$show_modified</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.8</priority>\n";
        
        if ($show_image) {
            $sitemap .= "    <image:image>\n";
            $sitemap .= "      <image:loc>$show_image</image:loc>\n";
            $sitemap .= "      <image:title>$show_title</image:title>\n";
            $sitemap .= "      <image:caption>$show_title TV Show</image:caption>\n";
            $sitemap .= "    </image:image>\n";
        }
        
        $sitemap .= "  </url>\n";
    }
    
    // Categories
    $categories = get_categories(array(
        'taxonomy' => 'genres',
        'hide_empty' => true
    ));
    
    foreach ($categories as $category) {
        $category_url = get_term_link($category);
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$category_url</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>weekly</changefreq>\n";
        $sitemap .= "    <priority>0.6</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // Tags
    $tags = get_tags(array('hide_empty' => true));
    
    foreach ($tags as $tag) {
        $tag_url = get_tag_link($tag->term_id);
        
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>$tag_url</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>monthly</changefreq>\n";
        $sitemap .= "    <priority>0.4</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    // Archive pages
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.8</priority>\n";
    $sitemap .= "  </url>\n";
    
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/tvshows/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.8</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Search pages
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/search/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>weekly</changefreq>\n";
    $sitemap .= "    <priority>0.5</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Latest movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/latest-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Popular movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/popular-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Top rated movies page
    $sitemap .= "  <url>\n";
    $sitemap .= "    <loc>" . home_url() . "/top-rated-movies/</loc>\n";
    $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap .= "    <changefreq>daily</changefreq>\n";
    $sitemap .= "    <priority>0.7</priority>\n";
    $sitemap .= "  </url>\n";
    
    // Year archive pages (last 10 years)
    $current_year = date('Y');
    for ($year = $current_year; $year >= $current_year - 10; $year--) {
        $sitemap .= "  <url>\n";
        $sitemap .= "    <loc>" . home_url() . "/year/$year/</loc>\n";
        $sitemap .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $sitemap .= "    <changefreq>monthly</changefreq>\n";
        $sitemap .= "    <priority>0.5</priority>\n";
        $sitemap .= "  </url>\n";
    }
    
    $sitemap .= "</urlset>";
    
    // Save advanced sitemap
    $upload_dir = wp_upload_dir();
    $sitemap_path = $upload_dir['basedir'] . '/sitemap.xml';
    file_put_contents($sitemap_path, $sitemap);
    
    // Also save to root directory for easy access
    $root_sitemap_path = ABSPATH . 'sitemap.xml';
    file_put_contents($root_sitemap_path, $sitemap);
    
    return $sitemap;
}

// Generate sitemap on post save
add_action('save_post', 'dooplay_advanced_sitemap_generator');

// Auto submit sitemap to search engines
function dooplay_auto_submit_sitemap($post_id) {
    if (get_post_status($post_id) == 'publish') {
        $sitemap_url = home_url() . '/sitemap.xml';
        
        // Submit to Google
        $google_url = "https://www.google.com/ping?sitemap=" . urlencode($sitemap_url);
        wp_remote_get($google_url);
        
        // Submit to Bing
        $bing_url = "https://www.bing.com/ping?sitemap=" . urlencode($sitemap_url);
        wp_remote_get($bing_url);
        
        // Submit to Yandex
        $yandex_url = "https://blogs.yandex.com/pings/?status=success&url=" . urlencode($sitemap_url);
        wp_remote_get($yandex_url);
        
        // Submit to DuckDuckGo
        $duckduckgo_url = "https://duckduckgo.com/?q=" . urlencode($sitemap_url);
        wp_remote_get($duckduckgo_url);
    }
}

add_action('save_post', 'dooplay_auto_submit_sitemap');

// Create sitemap index file
function dooplay_create_sitemap_index() {
    $sitemap_index = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $sitemap_index .= "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    $sitemap_index .= "  <sitemap>\n";
    $sitemap_index .= "    <loc>" . home_url() . "/sitemap.xml</loc>\n";
    $sitemap_index .= "    <lastmod>" . date('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
    $sitemap_index .= "  </sitemap>\n";
    
    $sitemap_index .= "</sitemapindex>";
    
    // Save sitemap index
    $upload_dir = wp_upload_dir();
    $sitemap_index_path = $upload_dir['basedir'] . '/sitemap-index.xml';
    file_put_contents($sitemap_index_path, $sitemap_index);
    
    // Also save to root directory
    $root_sitemap_index_path = ABSPATH . 'sitemap-index.xml';
    file_put_contents($root_sitemap_index_path, $sitemap_index);
}

add_action('save_post', 'dooplay_create_sitemap_index');

// Add sitemap to robots.txt
function dooplay_add_sitemap_to_robots() {
    $robots_content = "User-agent: *\n";
    $robots_content .= "Allow: /\n";
    $robots_content .= "Disallow: /wp-admin/\n";
    $robots_content .= "Disallow: /wp-includes/\n";
    $robots_content .= "Disallow: /wp-content/plugins/\n";
    $robots_content .= "Disallow: /wp-content/themes/\n";
    $robots_content .= "Disallow: /wp-content/cache/\n";
    $robots_content .= "Disallow: /wp-content/uploads/2025/\n";
    $robots_content .= "Disallow: /wp-content/uploads/armember/\n";
    $robots_content .= "Allow: /movies/\n";
    $robots_content .= "Allow: /tvshows/\n";
    $robots_content .= "Allow: /episodes/\n";
    $robots_content .= "Allow: /seasons/\n";
    $robots_content .= "Sitemap: " . home_url() . "/sitemap.xml\n";
    $robots_content .= "Sitemap: " . home_url() . "/sitemap-index.xml\n";
    $robots_content .= "Crawl-delay: 1\n";
    
    // Save robots.txt
    $robots_path = ABSPATH . 'robots.txt';
    file_put_contents($robots_path, $robots_content);
}

add_action('save_post', 'dooplay_add_sitemap_to_robots');

// Force generate sitemap on theme activation
function dooplay_force_generate_sitemap() {
    dooplay_advanced_sitemap_generator();
    dooplay_create_sitemap_index();
    dooplay_add_sitemap_to_robots();
}

add_action('after_switch_theme', 'dooplay_force_generate_sitemap');

// Force Dark Theme Style
function dooplay_force_dark_theme() {
    // Set theme style to dark if not already set
    $current_style = get_option('dooplay_style');
    if ($current_style !== 'dark') {
        update_option('dooplay_style', 'dark');
    }
}

// Run on theme activation and ensure dark theme
add_action('after_switch_theme', 'dooplay_force_dark_theme');
add_action('init', 'dooplay_force_dark_theme');

// Add CSS Variables for Theme Compatibility - TEMPORARILY DISABLED
function dooplay_add_css_variables() {
    // TEMPORARILY DISABLED TO PREVENT WHITE SCREEN ISSUES
    return;
    
    // Check if theme options exist and we're not in admin
    if (!function_exists('dooplay_get_option') || is_admin()) {
        return;
    }
    
    try {
        $style = dooplay_get_option('style', 'dark'); // Default to dark
        $main_color = dooplay_get_option('maincolor', '#408bea');
        
        echo "<style>\n";
        echo ":root {\n";
        
        if ($style == 'dark') {
            // Dark theme variables
            echo "  --dooplay-bg-color: #1a1a1a;\n";
            echo "  --dooplay-card-bg: #2d2d2d;\n";
            echo "  --dooplay-text-color: #ffffff;\n";
            echo "  --dooplay-heading-color: #ffffff;\n";
            echo "  --dooplay-border-color: #404040;\n";
            echo "  --dooplay-link-color: #4fc3f7;\n";
            echo "  --dooplay-main-color: $main_color;\n";
            echo "  --dooplay-accent-color: #17a2b8;\n";
            echo "  --dooplay-light-bg: #2d2d2d;\n";
            echo "  --dooplay-warning-bg: rgba(255, 193, 7, 0.1);\n";
            echo "  --dooplay-warning-border: rgba(255, 193, 7, 0.3);\n";
            echo "  --dooplay-warning-text: #ffc107;\n";
            echo "  --dooplay-warning-color: #ffc107;\n";
            echo "  --dooplay-warning-light: rgba(255, 193, 7, 0.1);\n";
            echo "  --dooplay-success-bg: rgba(76, 175, 80, 0.1);\n";
            echo "  --dooplay-success-color: #4CAF50;\n";
            echo "  --dooplay-success-border: rgba(76, 175, 80, 0.3);\n";
            echo "  --dooplay-success-light: rgba(76, 175, 80, 0.2);\n";
            echo "  --dooplay-success-lighter: rgba(76, 175, 80, 0.3);\n";
            echo "  --dooplay-info-bg: rgba(33, 150, 243, 0.1);\n";
            echo "  --dooplay-info-color: #2196F3;\n";
            echo "  --dooplay-info-border: rgba(33, 150, 243, 0.3);\n";
            echo "  --dooplay-info-light: rgba(33, 150, 243, 0.1);\n";
            echo "  --dooplay-info-lighter: rgba(33, 150, 243, 0.2);\n";
            echo "  --dooplay-purple-bg: rgba(156, 39, 176, 0.1);\n";
            echo "  --dooplay-purple-color: #9C27B0;\n";
            echo "  --dooplay-purple-border: rgba(156, 39, 176, 0.3);\n";
            echo "  --dooplay-purple-light: rgba(156, 39, 176, 0.1);\n";
            echo "  --dooplay-purple-lighter: rgba(156, 39, 176, 0.2);\n";
            echo "  --dooplay-danger-bg: rgba(244, 67, 54, 0.1);\n";
            echo "  --dooplay-danger-color: #f44336;\n";
            echo "  --dooplay-danger-border: rgba(244, 67, 54, 0.3);\n";
            echo "  --dooplay-danger-light: rgba(244, 67, 54, 0.1);\n";
            echo "  --dooplay-danger-text: #f44336;\n";
            echo "  --dooplay-rating-bg: #ffc107;\n";
            echo "  --dooplay-rating-text: #333;\n";
        } else {
            // Light theme variables
            echo "  --dooplay-bg-color: #f8f9fa;\n";
            echo "  --dooplay-card-bg: #ffffff;\n";
            echo "  --dooplay-text-color: #333333;\n";
            echo "  --dooplay-heading-color: #333333;\n";
            echo "  --dooplay-border-color: #e9ecef;\n";
            echo "  --dooplay-link-color: #0c5460;\n";
            echo "  --dooplay-main-color: $main_color;\n";
            echo "  --dooplay-accent-color: #17a2b8;\n";
            echo "  --dooplay-light-bg: #e8f4fd;\n";
            echo "  --dooplay-warning-bg: #fff3cd;\n";
            echo "  --dooplay-warning-border: #ffeaa7;\n";
            echo "  --dooplay-warning-text: #856404;\n";
            echo "  --dooplay-warning-color: #FF9800;\n";
            echo "  --dooplay-warning-light: #FFF3E0;\n";
            echo "  --dooplay-success-bg: #f0f8ff;\n";
            echo "  --dooplay-success-color: #4CAF50;\n";
            echo "  --dooplay-success-border: #C8E6C9;\n";
            echo "  --dooplay-success-light: #E8F5E8;\n";
            echo "  --dooplay-success-lighter: #C8E6C9;\n";
            echo "  --dooplay-info-bg: #E3F2FD;\n";
            echo "  --dooplay-info-color: #2196F3;\n";
            echo "  --dooplay-info-border: #BBDEFB;\n";
            echo "  --dooplay-info-light: #E3F2FD;\n";
            echo "  --dooplay-info-lighter: #BBDEFB;\n";
            echo "  --dooplay-purple-bg: #F3E5F5;\n";
            echo "  --dooplay-purple-color: #9C27B0;\n";
            echo "  --dooplay-purple-border: #E1BEE7;\n";
            echo "  --dooplay-purple-light: #F3E5F5;\n";
            echo "  --dooplay-purple-lighter: #E1BEE7;\n";
            echo "  --dooplay-danger-bg: #FFEBEE;\n";
            echo "  --dooplay-danger-color: #f44336;\n";
            echo "  --dooplay-danger-border: #FFCDD2;\n";
            echo "  --dooplay-danger-light: #FFEBEE;\n";
            echo "  --dooplay-danger-text: #f44336;\n";
            echo "  --dooplay-rating-bg: #ffc107;\n";
            echo "  --dooplay-rating-text: #333;\n";
        }
        
        echo "}\n";
        echo "</style>\n";
    } catch (Exception $e) {
        // If there's an error, don't output anything
        return;
    }
}

// Only add CSS variables if not in admin
if (!is_admin()) {
    add_action('wp_head', 'dooplay_add_css_variables', 5);
}

// Enqueue premium links JavaScript in admin
function enqueue_premium_links_admin_scripts($hook) {
    // Only load on post edit pages and links pages
    if (in_array($hook, ['post.php', 'post-new.php', 'edit.php']) ||
        (isset($_GET['post_type']) && $_GET['post_type'] === 'dt_links')) {

        wp_enqueue_script(
            'premium-links-admin',
            get_template_directory_uri() . '/assets/js/premium-links.js',
            array('jquery'),
            '1.0.0',
            true
        );
    }
}
add_action('admin_enqueue_scripts', 'enqueue_premium_links_admin_scripts');

// Enqueue ARMember custom styles
function enqueue_armember_custom_styles() {
    // Only load on pages that might have ARMember forms
    if (is_page_template('page-login.php') ||
        is_page_template('page-register.php') ||
        is_page_template('page-account.php') ||
        is_page_template('page-membership.php') ||
        is_user_logged_in()) {

        wp_enqueue_style(
            'armember-custom',
            get_template_directory_uri() . '/assets/css/armember-custom.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'enqueue_armember_custom_styles');

// Enqueue Genre Sections Module Assets
function enqueue_genre_sections_assets() {
    // Load on home page and front page
    if (is_home() || is_front_page()) {
            // Enqueue CSS
            wp_enqueue_style(
                'genre-sections',
                get_template_directory_uri() . '/assets/css/genre-sections.css',
                array(),
                '1.0.0'
            );

            // Enqueue JavaScript
            wp_enqueue_script(
                'genre-sections',
                get_template_directory_uri() . '/assets/js/genre-sections.js',
                array('jquery'),
                '1.0.0',
                true
            );

        // Localize script for AJAX
        wp_localize_script('genre-sections', 'genreSectionsAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('genre_sections_nonce'),
            'loading_text' => __d('Loading...'),
            'load_more_text' => __d('Load More'),
            'no_more_text' => __d('No more items'),
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_genre_sections_assets');

// AJAX handler for loading more genre content
function ajax_load_more_genre_content() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'genre_sections_nonce')) {
        wp_die('Security check failed');
    }

    $genre_id = intval($_POST['genre_id']);
    $page = intval($_POST['page']);

    if (!$genre_id || !$page) {
        wp_send_json_error('Invalid parameters');
    }

    // Get module settings
    $items_per_section = dooplay_get_option('genre_sections_items_per_section', 8);
    $content_types = dooplay_get_option('genre_sections_content_type', array('movies', 'tvshows'));
    $sort_content = dooplay_get_option('genre_sections_sort_content', 'date');

    // Prepare content query args
    $post_types = array();
    if (in_array('movies', $content_types)) $post_types[] = 'movies';
    if (in_array('tvshows', $content_types)) $post_types[] = 'tvshows';
    if (in_array('episodes', $content_types)) $post_types[] = 'episodes';

    $orderby = 'date';
    $order = 'DESC';
    switch ($sort_content) {
        case 'title':
            $orderby = 'title';
            $order = 'ASC';
            break;
        case 'rating':
            $orderby = 'meta_value_num';
            break;
        case 'views':
            $orderby = 'meta_value_num';
            break;
        case 'random':
            $orderby = 'rand';
            break;
    }

    $query_args = array(
        'post_type' => $post_types,
        'posts_per_page' => $items_per_section,
        'paged' => $page,
        'orderby' => $orderby,
        'order' => $order,
        'tax_query' => array(
            array(
                'taxonomy' => 'genres',
                'field' => 'term_id',
                'terms' => $genre_id,
            ),
        ),
    );

    if ($sort_content === 'rating') {
        $query_args['meta_key'] = 'dt_rating_average';
    } elseif ($sort_content === 'views') {
        $query_args['meta_key'] = 'dt_views_count';
    }

    $query = new WP_Query($query_args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('inc/parts/item');
        }
        $html = ob_get_clean();
        wp_reset_postdata();

        wp_send_json_success(array(
            'html' => $html,
            'has_more' => $page < $query->max_num_pages
        ));
    } else {
        wp_send_json_error('No more content');
    }
}
add_action('wp_ajax_load_more_genre_content', 'ajax_load_more_genre_content');
add_action('wp_ajax_nopriv_load_more_genre_content', 'ajax_load_more_genre_content');


