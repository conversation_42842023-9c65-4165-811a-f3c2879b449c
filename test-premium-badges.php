<?php
// Test file to check premium badge functionality
// This file simulates the actual WordPress environment

// Mock WordPress functions for testing
if (!function_exists('get_post_meta')) {
    function get_post_meta($post_id, $key, $single = false) {
        // Mock some links as premium for testing
        $premium_links = [1, 3, 5]; // Link IDs 1, 3, 5 are premium
        
        if ($key === '_is_premium_link') {
            return in_array($post_id, $premium_links) ? '1' : '';
        }
        
        return '';
    }
}

if (!function_exists('is_user_logged_in')) {
    function is_user_logged_in() {
        return isset($_GET['logged_in']) ? (bool)$_GET['logged_in'] : false;
    }
}

if (!function_exists('get_current_user_id')) {
    function get_current_user_id() {
        return isset($_GET['logged_in']) && $_GET['logged_in'] ? 123 : 0;
    }
}

// Mock premium user check
function is_user_premium() {
    return isset($_GET['premium']) ? (bool)$_GET['premium'] : false;
}

// Simulate premium status
$is_premium = is_user_premium();

// Mock link data
$mock_links = [
    (object)[
        'ID' => 1,
        'post_title' => '4K Download Link',
        'quality' => '4K',
        'language' => 'English',
        'size' => '8.5GB',
        'url' => 'https://example.com/4k-download'
    ],
    (object)[
        'ID' => 2,
        'post_title' => '1080p Download Link',
        'quality' => '1080p',
        'language' => 'English',
        'size' => '2.5GB',
        'url' => 'https://example.com/1080p-download'
    ],
    (object)[
        'ID' => 3,
        'post_title' => 'HD Download Link',
        'quality' => 'HD',
        'language' => 'English',
        'size' => '1.8GB',
        'url' => 'https://example.com/hd-download'
    ],
    (object)[
        'ID' => 4,
        'post_title' => '720p Download Link',
        'quality' => '720p',
        'language' => 'English',
        'size' => '1.2GB',
        'url' => 'https://example.com/720p-download'
    ],
    (object)[
        'ID' => 5,
        'post_title' => 'BluRay Download Link',
        'quality' => 'BluRay',
        'language' => 'English',
        'size' => '4.2GB',
        'url' => 'https://example.com/bluray-download'
    ]
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Badge Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .status-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        
        .toggle-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .toggle-links a {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .toggle-links .regular {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }
        
        .toggle-links .premium {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
        }
        
        .links-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .links-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .links-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .links-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .quality-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .premium-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            color: #000;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
            letter-spacing: 0.5px;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        .premium-badge-locked {
            background: linear-gradient(135deg, #dc3545, #c82333, #bd2130);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
            letter-spacing: 0.5px;
            animation: pulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4); }
            to { box-shadow: 0 4px 16px rgba(255, 215, 0, 0.8); }
        }
        
        @keyframes pulse {
            from { box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4); }
            to { box-shadow: 0 4px 16px rgba(220, 53, 69, 0.8); }
        }
        
        .download-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 12px;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 2px;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .premium-download {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            color: #000;
        }
        
        .premium-locked {
            background: linear-gradient(135deg, #6c757d, #5a6268, #495057);
            color: white;
        }
        
        .info-box {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Premium Badge Test</h1>
        
        <div class="toggle-links">
            <a href="?premium=0" class="regular">
                <i class="fas fa-user"></i> View as Regular User
            </a>
            <a href="?premium=1" class="premium">
                <i class="fas fa-crown"></i> View as Premium User
            </a>
        </div>
        
        <div class="status-info">
            <strong>Current Status:</strong> 
            <?php if ($is_premium): ?>
                <span style="color: #FFD700;"><i class="fas fa-crown"></i> Premium User</span>
            <?php else: ?>
                <span style="color: #6c757d;"><i class="fas fa-user"></i> Regular User</span>
            <?php endif; ?>
        </div>
        
        <table class="links-table">
            <thead>
                <tr>
                    <th>Link ID</th>
                    <th>Quality</th>
                    <th>Language</th>
                    <th>Size</th>
                    <th>Actions</th>
                    <th>Premium Status</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($mock_links as $link): 
                    // Check if this specific link is marked as premium
                    $is_link_premium = get_post_meta($link->ID, '_is_premium_link', true);
                    
                    // Only show premium badge if this link is specifically marked as premium
                    $premium_badge = '';
                    if ($is_link_premium == '1') {
                        if ($is_premium) {
                            // User is premium - show unlocked badge
                            $premium_badge = '<span class="premium-badge">PREMIUM</span>';
                        } else {
                            // User is not premium - show locked badge
                            $premium_badge = '<span class="premium-badge-locked"><i class="fas fa-lock" style="margin-right: 4px; font-size: 8px;"></i>PREMIUM</span>';
                        }
                    }
                ?>
                <tr>
                    <td><strong><?php echo $link->ID; ?></strong></td>
                    <td>
                        <span class="quality-badge"><?php echo $link->quality; ?></span>
                        <?php echo $premium_badge; ?>
                    </td>
                    <td><?php echo $link->language; ?></td>
                    <td><?php echo $link->size; ?></td>
                    <td>
                        <?php if ($is_link_premium == '1'): ?>
                            <?php if ($is_premium): ?>
                                <!-- Premium user accessing premium link - direct download -->
                                <a href="<?php echo $link->url; ?>" class="download-btn premium-download" target="_blank">
                                    <i class="fas fa-download"></i> Direct Download
                                </a>
                            <?php else: ?>
                                <!-- Regular user accessing premium link - show locked message -->
                                <a href="#" class="download-btn premium-locked" onclick="alert('This is a Premium link! Upgrade your membership.'); return false;">
                                    <i class="fas fa-lock"></i> Premium Only
                                </a>
                            <?php endif; ?>
                        <?php else: ?>
                            <?php if ($is_premium): ?>
                                <!-- Premium user accessing regular link - still gets direct download benefit -->
                                <a href="<?php echo $link->url; ?>" class="download-btn" target="_blank">
                                    <i class="fas fa-download"></i> Direct Download
                                </a>
                            <?php else: ?>
                                <!-- Regular user accessing regular link - normal short link process -->
                                <a href="#" class="download-btn" onclick="alert('Regular download with 10s delay...'); return false;">
                                    <i class="fas fa-download"></i> Download
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($is_link_premium == '1'): ?>
                            <span style="color: #d4af37; font-weight: bold;">
                                <i class="fas fa-star"></i> Premium Link
                            </span>
                        <?php else: ?>
                            <span style="color: #6c757d;">
                                <i class="fas fa-link"></i> Regular Link
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="info-box">
            <h3>Test Results:</h3>
            <ul>
                <li><strong>Premium Links:</strong> Link IDs 1, 3, 5 (4K, HD, BluRay)</li>
                <li><strong>Regular Links:</strong> Link IDs 2, 4 (1080p, 720p)</li>
                <li><strong>Premium Badge:</strong> Only shows on premium links</li>
                <li><strong>Download Behavior:</strong> Premium users get direct access to all links</li>
            </ul>
            
            <h4>Expected Behavior:</h4>
            <ul>
                <li>✅ <strong>Premium links show badges</strong> (golden for premium users, red locked for regular users)</li>
                <li>✅ <strong>Regular links show no badges</strong></li>
                <li>✅ <strong>Premium users get direct downloads</strong> for all links</li>
                <li>✅ <strong>Regular users get locked message</strong> for premium links only</li>
            </ul>
        </div>
    </div>
</body>
</html>
