!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?t(require("jquery")):t(jQuery)}(function(u){var t,f,h,v,g,k,_,m,b,y,x,w,o,a,I,c,l,M,r,n,C,q,d,s,T,i,p,S,e=(t={showEvent:"click",onShow:function(){},onBeforeShow:function(){},onHide:function(){},onChange:function(){},onSubmit:function(){},colorScheme:"light",color:"auto",livePreview:!0,flat:!1,layout:"full",submit:1,submitText:"OK",height:156,polyfill:!1,styles:!1},f=function(t,e){var i=Y(t);u(e).data("colpick").fields.eq(1).val(i.r).end().eq(2).val(i.g).end().eq(3).val(i.b).end()},h=function(t,e){u(e).data("colpick").fields.eq(4).val(Math.round(t.h)).end().eq(5).val(Math.round(t.s)).end().eq(6).val(Math.round(t.b)).end()},v=function(t,e){u(e).data("colpick").fields.eq(0).val(N(t))},g=function(t,e){u(e).data("colpick").selector.css("backgroundColor","#"+N({h:t.h,s:100,b:100})),u(e).data("colpick").selectorIndic.css({left:parseInt(u(e).data("colpick").height*t.s/100,10),top:parseInt(u(e).data("colpick").height*(100-t.b)/100,10)})},k=function(t,e){u(e).data("colpick").hue.css("top",parseInt(u(e).data("colpick").height-u(e).data("colpick").height*t.h/360,10))},_=function(t,e){u(e).data("colpick").currentColor.css("backgroundColor","#"+N(t))},m=function(t,e){u(e).data("colpick").newColor.css("backgroundColor","#"+N(t))},b=function(){var t,e=u(this).parent().parent();0<this.parentNode.className.indexOf("_hex")?(e.data("colpick").color=t=D(p(this.value)),f(t,e.get(0)),h(t,e.get(0))):0<this.parentNode.className.indexOf("_hsb")?(e.data("colpick").color=t=T({h:parseInt(e.data("colpick").fields.eq(4).val(),10),s:parseInt(e.data("colpick").fields.eq(5).val(),10),b:parseInt(e.data("colpick").fields.eq(6).val(),10)}),f(t,e.get(0)),v(t,e.get(0))):(e.data("colpick").color=t=P(i({r:parseInt(e.data("colpick").fields.eq(1).val(),10),g:parseInt(e.data("colpick").fields.eq(2).val(),10),b:parseInt(e.data("colpick").fields.eq(3).val(),10)})),v(t,e.get(0)),h(t,e.get(0))),g(t,e.get(0)),k(t,e.get(0)),m(t,e.get(0)),e.data("colpick").onChange.apply(e.parent(),[t,N(t),Y(t),e.data("colpick").el,0])},y=function(){u(this).parent().removeClass("colpick_focus")},x=function(){u(this).parent().parent().data("colpick").fields.parent().removeClass("colpick_focus"),u(this).parent().addClass("colpick_focus")},w=function(t){t.preventDefault?t.preventDefault():t.returnValue=!1;var e=u(this).parent().find("input").focus(),i={el:u(this).parent().addClass("colpick_slider"),max:0<this.parentNode.className.indexOf("_hsb_h")?360:0<this.parentNode.className.indexOf("_hsb")?100:255,y:t.pageY,field:e,val:parseInt(e.val(),10),preview:u(this).parent().parent().data("colpick").livePreview};u(document).mouseup(i,a),u(document).mousemove(i,o)},o=function(t){return t.data.field.val(Math.max(0,Math.min(t.data.max,parseInt(t.data.val-t.pageY+t.data.y,10)))),t.data.preview&&b.apply(t.data.field.get(0),[!0]),!1},a=function(t){return b.apply(t.data.field.get(0),[!0]),t.data.el.removeClass("colpick_slider").find("input").focus(),u(document).off("mouseup",a),u(document).off("mousemove",o),!1},I=function(t){t.preventDefault?t.preventDefault():t.returnValue=!1;var e={cal:u(this).parent(),y:u(this).offset().top};u(document).on("mouseup touchend",e,l),u(document).on("mousemove touchmove",e,c);var i="touchstart"==t.type?t.originalEvent.changedTouches[0].pageY:t.pageY;return b.apply(e.cal.data("colpick").fields.eq(4).val(parseInt(360*(e.cal.data("colpick").height-(i-e.y))/e.cal.data("colpick").height,10)).get(0),[e.cal.data("colpick").livePreview]),!1},c=function(t){var e="touchmove"==t.type?t.originalEvent.changedTouches[0].pageY:t.pageY;return b.apply(t.data.cal.data("colpick").fields.eq(4).val(parseInt(360*(t.data.cal.data("colpick").height-Math.max(0,Math.min(t.data.cal.data("colpick").height,e-t.data.y)))/t.data.cal.data("colpick").height,10)).get(0),[t.data.preview]),!1},l=function(t){return f(t.data.cal.data("colpick").color,t.data.cal.get(0)),v(t.data.cal.data("colpick").color,t.data.cal.get(0)),u(document).off("mouseup touchend",l),u(document).off("mousemove touchmove",c),!1},M=function(t){t.preventDefault?t.preventDefault():t.returnValue=!1;var e,i,o={cal:u(this).parent(),pos:u(this).offset()};return o.preview=o.cal.data("colpick").livePreview,u(document).on("mouseup touchend",o,n),u(document).on("mousemove touchmove",o,r),"touchstart"==t.type?(e=t.originalEvent.changedTouches[0].pageX,i=t.originalEvent.changedTouches[0].pageY):(e=t.pageX,i=t.pageY),b.apply(o.cal.data("colpick").fields.eq(6).val(parseInt(100*(o.cal.data("colpick").height-(i-o.pos.top))/o.cal.data("colpick").height,10)).end().eq(5).val(parseInt(100*(e-o.pos.left)/o.cal.data("colpick").height,10)).get(0),[o.preview]),!1},r=function(t){var e,i;return"touchmove"==t.type?(e=t.originalEvent.changedTouches[0].pageX,i=t.originalEvent.changedTouches[0].pageY):(e=t.pageX,i=t.pageY),b.apply(t.data.cal.data("colpick").fields.eq(6).val(parseInt(100*(t.data.cal.data("colpick").height-Math.max(0,Math.min(t.data.cal.data("colpick").height,i-t.data.pos.top)))/t.data.cal.data("colpick").height,10)).end().eq(5).val(parseInt(100*Math.max(0,Math.min(t.data.cal.data("colpick").height,e-t.data.pos.left))/t.data.cal.data("colpick").height,10)).get(0),[t.data.preview]),!1},n=function(t){return f(t.data.cal.data("colpick").color,t.data.cal.get(0)),v(t.data.cal.data("colpick").color,t.data.cal.get(0)),u(document).off("mouseup touchend",n),u(document).off("mousemove touchmove",r),!1},C=function(){var t=u(this).parent(),e=t.data("colpick").color;t.data("colpick").origColor=e,_(e,t.get(0)),t.data("colpick").onSubmit(e,N(e),Y(e),t.data("colpick").el)},q=function(t){t&&t.stopPropagation();var e=u("#"+u(this).data("colpickId"));t&&!e.data("colpick").polyfill&&t.preventDefault(),e.data("colpick").onBeforeShow.apply(this,[e.get(0)]);var i=u(this).offset(),o=i.top+this.offsetHeight,a=i.left,c=s(),l=e.width();a+l>c.l+c.w&&(a-=l),e.css({left:a+"px",top:o+"px"}),0!=e.data("colpick").onShow.apply(this,[e.get(0)])&&e.show(),u("html").mousedown({cal:e},d),e.mousedown(function(t){t.stopPropagation()})},d=function(t){var e=u("#"+u(this).data("colpickId"));t&&(e=t.data.cal),0!=e.data("colpick").onHide.apply(this,[e.get(0)])&&e.hide(),u("html").off("mousedown",d)},s=function(){var t="CSS1Compat"==document.compatMode;return{l:window.pageXOffset||(t?document.documentElement.scrollLeft:document.body.scrollLeft),w:window.innerWidth||(t?document.documentElement.clientWidth:document.body.clientWidth)}},T=function(t){return{h:Math.min(360,Math.max(0,t.h)),s:Math.min(100,Math.max(0,t.s)),b:Math.min(100,Math.max(0,t.b))}},i=function(t){return{r:Math.min(255,Math.max(0,t.r)),g:Math.min(255,Math.max(0,t.g)),b:Math.min(255,Math.max(0,t.b))}},p=function(t){var e=6-t.length;if(3==e){for(var i=[],o=0;o<e;o++)i.push(t[o]),i.push(t[o]);t=i.join("")}else if(0<e){for(var a=[],c=0;c<e;c++)a.push("0");a.push(t),t=a.join("")}return t},S=function(){var t=u(this).parent(),e=t.data("colpick").origColor;t.data("colpick").color=e,f(e,t.get(0)),v(e,t.get(0)),h(e,t.get(0)),g(e,t.get(0)),k(e,t.get(0)),m(e,t.get(0))},{init:function(p){if("auto"===(p=u.extend({},t,p||{})).color);else if("string"==typeof p.color)p.color=D(p.color);else if(null!=p.color.r&&null!=p.color.g&&null!=p.color.b)p.color=P(p.color);else{if(null==p.color.h||null==p.color.s||null==p.color.b)return this;p.color=T(p.color)}return this.each(function(){if(!u(this).data("colpickId")){var t=u.extend({},p);if("auto"===p.color&&(t.color=u(this).val()?D(u(this).val()):{h:0,s:0,b:0}),t.origColor=t.color,"function"==typeof p.polyfill&&(t.polyfill=p.polyfill(this)),t.polyfill&&u(this).is("input")&&"color"===this.type)return;var e="collorpicker_"+parseInt(1e3*Math.random());u(this).data("colpickId",e);var i=u('<div class="colpick"><div class="colpick_color"><div class="colpick_color_overlay1"><div class="colpick_color_overlay2"><div class="colpick_selector_outer"><div class="colpick_selector_inner"></div></div></div></div></div><div class="colpick_hue"><div class="colpick_hue_arrs"><div class="colpick_hue_larr"></div><div class="colpick_hue_rarr"></div></div></div><div class="colpick_new_color"></div><div class="colpick_current_color"></div><div class="colpick_hex_field"><div class="colpick_field_letter">#</div><input type="text" maxlength="6" size="6" /></div><div class="colpick_rgb_r colpick_field"><div class="colpick_field_letter">R</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_rgb_g colpick_field"><div class="colpick_field_letter">G</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_rgb_b colpick_field"><div class="colpick_field_letter">B</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_hsb_h colpick_field"><div class="colpick_field_letter">H</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_hsb_s colpick_field"><div class="colpick_field_letter">S</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_hsb_b colpick_field"><div class="colpick_field_letter">B</div><input type="text" maxlength="3" size="3" /><div class="colpick_field_arrs"><div class="colpick_field_uarr"></div><div class="colpick_field_darr"></div></div></div><div class="colpick_submit"></div></div>').attr("id",e);i.addClass("colpick_"+t.layout+(t.submit?"":" colpick_"+t.layout+"_ns")),"light"!=t.colorScheme&&i.addClass("colpick_"+t.colorScheme),i.find("div.colpick_submit").html(t.submitText).click(C),t.fields=i.find("input").change(b).blur(y).focus(x),i.find("div.colpick_field_arrs").mousedown(w).end().find("div.colpick_current_color").click(S),t.selector=i.find("div.colpick_color").on("mousedown touchstart",M),t.selectorIndic=t.selector.find("div.colpick_selector_outer"),t.el=this,t.hue=i.find("div.colpick_hue_arrs");var o=t.hue.parent(),a=navigator.userAgent.toLowerCase(),c="Microsoft Internet Explorer"===navigator.appName,l=c?parseFloat(a.match(/msie ([0-9]*[\.0-9]+)/)[1]):0,r=["#ff0000","#ff0080","#ff00ff","#8000ff","#0000ff","#0080ff","#00ffff","#00ff80","#00ff00","#80ff00","#ffff00","#ff8000","#ff0000"];if(c&&l<10){var n,d;for(n=0;n<=11;n++)d=u("<div></div>").attr("style","height:8.333333%; filter:progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr="+r[n]+", endColorstr="+r[n+1]+'); -ms-filter: "progid:DXImageTransform.Microsoft.gradient(GradientType=0,startColorstr='+r[n]+", endColorstr="+r[n+1]+')";'),o.append(d)}else{var s=r.join(",");o.attr("style","background:-webkit-linear-gradient(top,"+s+"); background: -o-linear-gradient(top,"+s+"); background: -ms-linear-gradient(top,"+s+"); background:-moz-linear-gradient(top,"+s+"); -webkit-linear-gradient(top,"+s+"); background:linear-gradient(to bottom,"+s+"); ")}i.find("div.colpick_hue").on("mousedown touchstart",I),t.newColor=i.find("div.colpick_new_color"),t.currentColor=i.find("div.colpick_current_color"),i.data("colpick",t),f(t.color,i.get(0)),h(t.color,i.get(0)),v(t.color,i.get(0)),k(t.color,i.get(0)),g(t.color,i.get(0)),_(t.color,i.get(0)),m(t.color,i.get(0)),t.flat?(i.appendTo(t.appendTo||this).show(),i.css(t.styles||{position:"relative",display:"block"})):(i.appendTo(t.appendTo||document.body),u(this).on(t.showEvent,q),i.css(t.styles||{position:"absolute"}))}})},showPicker:function(){return this.each(function(){u(this).data("colpickId")&&q.apply(this)})},hidePicker:function(){return this.each(function(){u(this).data("colpickId")&&d.apply(this)})},setColor:function(e,i){if(i=void 0===i?1:i,"string"==typeof e)e=D(e);else if(null!=e.r&&null!=e.g&&null!=e.b)e=P(e);else{if(null==e.h||null==e.s||null==e.b)return this;e=T(e)}return this.each(function(){if(u(this).data("colpickId")){var t=u("#"+u(this).data("colpickId"));t.data("colpick").color=e,t.data("colpick").origColor=e,f(e,t.get(0)),h(e,t.get(0)),v(e,t.get(0)),k(e,t.get(0)),g(e,t.get(0)),m(e,t.get(0)),t.data("colpick").onChange.apply(t.parent(),[e,N(e),Y(e),t.data("colpick").el,1]),i&&_(e,t.get(0))}})},destroy:function(){u("#"+u(this).data("colpickId")).remove()}}),E=function(t){return{r:(t=parseInt(-1<t.indexOf("#")?t.substring(1):t,16))>>16,g:(65280&t)>>8,b:255&t}},D=function(t){return P(E(t))},P=function(t){var e={h:0,s:0,b:0},i=Math.min(t.r,t.g,t.b),o=Math.max(t.r,t.g,t.b),a=o-i;return e.b=o,e.s=0!=o?255*a/o:0,0!=e.s?t.r==o?e.h=(t.g-t.b)/a:t.g==o?e.h=2+(t.b-t.r)/a:e.h=4+(t.r-t.g)/a:e.h=-1,e.h*=60,e.h<0&&(e.h+=360),e.s*=100/255,e.b*=100/255,e},Y=function(t){var e={},i=t.h,o=255*t.s/100,a=255*t.b/100;if(0==o)e.r=e.g=e.b=a;else{var c=a,l=(255-o)*a/255,r=i%60*(c-l)/60;360==i&&(i=0),i<60?(e.r=c,e.b=l,e.g=l+r):i<120?(e.g=c,e.b=l,e.r=c-r):i<180?(e.g=c,e.r=l,e.b=l+r):i<240?(e.b=c,e.r=l,e.g=c-r):i<300?(e.b=c,e.g=l,e.r=l+r):i<360?(e.r=c,e.g=l,e.b=c-r):(e.r=0,e.g=0,e.b=0)}return{r:Math.round(e.r),g:Math.round(e.g),b:Math.round(e.b)}},H=function(t){var i=[t.r.toString(16),t.g.toString(16),t.b.toString(16)];return u.each(i,function(t,e){1==e.length&&(i[t]="0"+e)}),i.join("")},N=function(t){return H(Y(t))};u.fn.extend({colpick:e.init,colpickHide:e.hidePicker,colpickShow:e.showPicker,colpickSetColor:e.setColor,colpickDestroy:e.destroy}),u.extend({colpick:{rgbToHex:H,rgbToHsb:P,hsbToHex:N,hsbToRgb:Y,hexToHsb:D,hexToRgb:E}})});