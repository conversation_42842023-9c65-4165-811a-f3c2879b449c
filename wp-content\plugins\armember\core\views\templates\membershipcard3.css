@charset "utf-8";
.membershipcard3.arm_membership_card_template_wrapper {
	width: 620px;
	border-left: 1px solid #4cabe5;
	border-right: 1px solid #4cabe5;
	background-color: #4cabe5;
	border-radius: 10px;
	margin: 0 auto 20px auto;
	padding: 5px 0;
	max-width: 100%;
	position: relative;
	overflow: hidden;
}

.membershipcard3 .arm_card_content {
	overflow: hidden;
	background-color: #fff;
}

.membershipcard3 .arm_card_left {
	background-color: #f6f6f6;
	min-height: 223px;
	width: 190px;
	float: left;
	border-right: 1px solid #f6f6f6;
}

.membershipcard3 .arm_card_logo {
	height: 165px;
	padding: 10px;
	text-align: center;
}

.membershipcard3 .arm_card_logo img {
	width: 100%;
	height: 100%;
	margin: auto;
}

.membershipcard3 .arm_card_title {
	text-align: center;
	font-size: 20px;
	color: #003058;
	font-family: "Roboto";
	font-weight: bold;
	min-height: 58px;
	padding: 15px;
	border-top: 1px solid #ebebeb;
	vertical-align: middle;
    display: table-cell;
    width: 190px;
    line-height: normal;
    word-break: break-all;
    word-break: break-word;
}

.membershipcard3 .arm_card_title span {
	word-break: break-word;
}

.membershipcard3 .arm_card_details {
	float: left;
	width: calc(100% - 191px);
}

.membershipcard3 .arm_card_details ul {
	margin: 0;
	padding: 0;
	list-style-type: none;
}

.membershipcard3 .arm_card_details ul li {
	padding: 8px 0 8px 25px;
	border-bottom: 1px solid #ebebeb;
	overflow: hidden;
}

.membershipcard3 .arm_card_label {
	display: inline-block;
	font-size: 16px;
	color: #000000;
	width: 47%;
	word-break: break-all;
	vertical-align: middle;
	text-align: left;
	word-break: break-word;
	float: left;
}

.membershipcard3 .arm_card_value {
	display: inline-block;
	width: 47%;
	padding-left: 10px;
	word-break: break-word;
	vertical-align: middle;
	text-align: left;
	float: left;
}

.popup_wrapper.arm_mobile_wrapper .membershipcard3 .arm_card_left {
	width: 100% !important;
	float: none !important;
	height: 80px !important;
}

.popup_wrapper.arm_mobile_wrapper .membershipcard3 .arm_card_details {
	float: left;
	width: 100%;
}

.popup_wrapper.arm_mobile_wrapper .membershipcard3 .arm_card_logo img {
	width: 50%;
}

.popup_wrapper.arm_mobile_wrapper .membershipcard3 .arm_card_title {
	display: block;
	width: 100%;
	padding: 8px 0 0 0;
	height: auto;
}
.membershipcard3.arm_rtl_site .arm_card_left
{
	border-left: 1px solid #f6f6f6;
	border-right: none;
}
@media only screen and (max-width: 480px) {

	.membershipcard3 .arm_card_left {
		width: 100% !important;
		float: none !important;
	}

	.membershipcard3 .arm_card_details {
		float: left;
		width: 100%;
	}

	.membershipcard3 .arm_card_logo img {
		width: 50%;
	}

	.membershipcard3 .arm_card_title {
		display: block;
		width: 100%;
		padding: 5px;
		height: auto;
	}
}