.arm-material-style .arm-df__form-group{margin-bottom:32px}.arm-material-style:not(.arm--material-outline-style) .arm-df__label-text{position:absolute}.arm-material-style.arm--material-outline-style .arm-df__label-text{position:relative}.arm-material-style.arm--material-outline-style .arm-df__label-text.active{padding:4px 0}.arm-material-style .arm-df__label-text{font-size:16px;line-height:18px;color:var(--arm-dt-black-400);padding:0;cursor:text;left:0;transition:all .3s;-webkit-transition:all .3s;-o-transition:all .3s;-moz-transition:all .3s;-ms-transition:all .3s;z-index:1;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;width:inherit;max-width:100%}.arm-material-style .arm-df__label-text.active{top:-15px!important;font-size:12px}.arm-material-style .arm-df__label-text.--arm-has-prefix-icon{padding:0 0 0 40px}.arm-material-style:not(.arm--material-outline-style) .arm-df__dropdown-control .arm__dc--head,.arm-material-style:not(.arm--material-outline-style) input.arm-df__form-control,.arm-material-style:not(.arm--material-outline-style) textarea.arm-df__form-control{border-top:none!important;border-left:none!important;border-right:none!important;box-shadow:none!important;-webkit-box-shadow:none!important;-moz-box-shadow:none!important;-o-box-shadow:none!important;-ms-box-shadow:none!important}.arm-material-style .arm-df__dropdown-control .arm__dc--head,.arm-material-style input.arm-df__form-control,.arm-material-style textarea.arm-df__form-control{background-color:transparent;border-color:var(--arm-gt-gray-100);border-radius:0;-webkit-border-radius:0;-o-border-radius:0;-moz-border-radius:0;outline:0;width:100%;font-size:14px;line-height:16px;padding:4px 0 4px 0;color:var(--arm-dt-black-200);box-shadow:none;-o-box-shadow:none;-ms-box-shadow:none;-moz-box-shadow:none;-webkit-box-shadow:none;box-sizing:border-box;-webkit-box-sizing:border-box;-o-box-sizing:border-box;-moz-box-sizing:border-box;transition:all .3s cubic-bezier(.64,.09,.08,1);-webkit-transition:all .3s cubic-bezier(.64,.09,.08,1);-moz-transition:all .3s cubic-bezier(.64,.09,.08,1);-ms-transition:all .3s cubic-bezier(.64,.09,.08,1);-o-transition:all .3s cubic-bezier(.64,.09,.08,1)}.arm-default-form.arm-material-style .arm-df__form-field-wrap .arm-df__dropdown-control dt.arm__dc--head,.arm-default-form.arm-material-style .arm-df__form-field-wrap textarea.arm-df__form-control,.arm-material-style .arm-df__form-control:not([type=checkbox],[type=radio],.arm-df__dc--head__autocomplete){border-radius:0!important;-webkit-border-radius:0!important;-o-border-radius:0!important;-moz-border-radius:0!important}.arm-material-style .arm-df__dropdown-control .arm__dc--head .arm-df__dc--head__autocomplete,.arm-material-style .arm-df__dropdown-control .arm__dc--head .arm__dc--head__title{margin-right:35px}.arm_form_rtl.arm-material-style .arm-df__dropdown-control .arm__dc--head .arm-df__dc--head__autocomplete,.arm_form_rtl.arm-material-style .arm-df__dropdown-control .arm__dc--head .arm__dc--head__title{margin-right:0;margin-left:35px}.arm-material-style textarea.arm-df__form-control{resize:none;min-height:92px!important}.arm-material-style .arm-df__label-text .arm-df__label-asterisk{position:absolute;top:0;font-size:16px;margin-left:2px;font-weight:400;color:var(--arm-sc-error)}.arm-material-style .arm-df__fc-icon.--arm-prefix-icon{left:16px}.arm-material-style.arm_form_ltr .arm-df__fc-icon.--arm-suffix-icon{left:auto;right:16px}.arm-material-style .arm-df__fc-icon .armfa{font-size:18px}.arm-material-style .arm-df__checkbox input[type=checkbox]:checked,.arm-material-style .arm-df__checkbox input[type=checkbox]:not(:checked){position:absolute;opacity:0;pointer-events:none}.arm-material-style .arm-df__checkbox input[type=checkbox]+label{position:relative;padding-left:28px;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;margin-left:0!important}.arm-material-style .arm-df__checkbox input[type=checkbox]+label:after,.arm-material-style .arm-df__checkbox input[type=checkbox]+label:before{content:'';left:0;position:absolute;-webkit-transition:.2s;transition:.2s}.arm-material-style .arm-df__checkbox input[type=checkbox]+label:after{height:20px;width:20px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box;border-radius:var(--arm-radius-4px);-webkit-border-radius:var(--arm-radius-4px);-moz-border-radius:var(--arm-radius-4px);-o-border-radius:var(--arm-radius-4px);background-color:transparent;border:2px solid var(--arm-gt-gray-100);top:calc(50% - 10px);z-index:0}.arm-material-style .arm-df__checkbox input[type=checkbox]:checked+label:after{background-color:var(--arm-pt-theme-blue);border-color:var(--arm-pt-theme-blue)}.arm-material-style .arm-df__checkbox input[type=checkbox]:checked:focus+label:after{background-color:var(--arm-pt-theme-blue);border-color:var(--arm-pt-theme-blue)}.arm-material-style .arm-df__checkbox input[type=checkbox]:focus+label:after{-webkit-transform:scale(1);transform:scale(1);-webkit-box-shadow:0 0 0 8px rgba(26,37,56,.1);box-shadow:0 0 0 8px rgba(26,37,56,.1);background-color:rgba(26,37,56,.1);border-color:var(--arm-dt-black-100)}.arm-material-style .arm-df__checkbox input[type=checkbox]+label:before{width:0;height:0;border:3px solid transparent;left:6px;top:10px;-webkit-transform:rotateZ(40deg);transform:rotateZ(40deg);-webkit-transform-origin:100% 100%;transform-origin:100% 100%;-webkit-transition:border .25s,background-color .25s,width .2s .1s,height .2s .1s,top .2s .1s,left .2s .1s;transition:border .25s,background-color .25s,width .2s .1s,height .2s .1s,top .2s .1s,left .2s .1s}.arm-material-style .arm-df__checkbox input[type=checkbox]:checked+label:before{top:calc(50% - 6px);left:3px;width:5px;height:9px;border-top:2px solid transparent;border-left:2px solid transparent;border-right:2px solid #fff;border-bottom:2px solid #fff;z-index:1;-webkit-transform:rotateZ(40deg);transform:rotateZ(40deg)}.arm-material-style .arm-df__checkbox input[type=checkbox]:checked:disabled+label:before{border-right:2px solid rgba(26,37,56,.42);border-bottom:2px solid rgba(26,37,56,.42)}.arm-material-style .arm-df__checkbox input[type=checkbox]:disabled:not(:checked)+label:before{background-color:transparent;border:2px solid transparent}.arm-material-style .arm-df__checkbox input[type=checkbox]:disabled:not(:checked)+label:after{border-color:transparent;background-color:var(--arm-gt-gray-200)}.arm-material-style .arm-df__checkbox input[type=checkbox]:disabled:checked+label:before{background-color:transparent}.arm-material-style .arm-df__checkbox input[type=checkbox]:disabled:checked+label:after{background-color:var(--arm-gt-gray-200);border-color:var(--arm-gt-gray-200)}.arm-material-style .arm-df__radio input[type=radio]:checked,.arm-material-style .arm-df__radio input[type=radio]:not(:checked){position:absolute;opacity:0;pointer-events:none}.arm-material-style .arm-df__radio input[type=radio]+label{position:relative;padding-left:28px;cursor:pointer;-webkit-transition:.28s ease;transition:.28s ease;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;margin-left:0!important}.arm-material-style .arm-df__radio input[type=radio]+label:after,.arm-material-style .arm-df__radio input[type=radio]+label:before{content:'';position:absolute;left:0;top:calc(50% - 11px);width:20px;height:20px;z-index:0;-webkit-transition:.28s ease;transition:.28s ease;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;-o-box-sizing:border-box;box-sizing:border-box}.arm-material-style .arm-df__radio input[type=radio]+label:after,.arm-material-style .arm-df__radio input[type=radio]+label:before,.arm-material-style .arm-df__radio input[type=radio]:checked+label:after,.arm-material-style .arm-df__radio input[type=radio]:checked+label:before{border-radius:50%}.arm-material-style .arm-df__radio input[type=radio]+label:after,.arm-material-style .arm-df__radio input[type=radio]+label:before{border:2px solid var(--arm-gt-gray-100)}.arm-material-style .arm-df__radio input[type=radio]+label:after{-webkit-transform:scale(0);transform:scale(0)}.arm-material-style .arm-df__radio input[type=radio]:checked+label:before{border-color:transparent}.arm-material-style .arm-df__radio input[type=radio]:checked+label:after,.arm-material-style .arm-df__radio input[type=radio]:checked+label:before{border-color:var(--arm-pt-theme-blue)}.arm-material-style .arm-df__radio input[type=radio]:checked+label:after{background-color:var(--arm-pt-theme-blue)}.arm-material-style .arm-df__radio input[type=radio]:checked+label:after{-webkit-transform:scale(1.02);transform:scale(1.02)}.arm-material-style .arm-df__radio input[type=radio]:checked+label:after{-webkit-transform:scale(.5);transform:scale(.5)}.arm-material-style .arm-df__radio input[type=radio]:focus+label:before{-webkit-box-shadow:0 0 0 10px rgba(0,0,0,.1);box-shadow:0 0 0 10px rgba(0,0,0,.1)}.arm-material-style .arm-df__radio input[type=radio]:disabled:checked+label:before,.arm-material-style .arm-df__radio input[type=radio]:disabled:not(:checked)+label:before{background-color:transparent;border-color:rgba(26,37,56,.4)}.arm-material-style .arm-df__radio input[type=radio]:disabled+label{color:var(--arm-gt-gray-300)}.arm-material-style .arm-df__radio input[type=radio]:disabled:checked+label:after{background-color:rgba(26,37,56,.42);border-color:var(--arm-gt-gray-100)}.arm-waves-effect{position:relative;cursor:pointer;display:inline-block;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:transparent;vertical-align:middle;z-index:1;-webkit-transition:.3s ease-out;transition:.3s ease-out}.arm-waves-effect .arm-waves-ripple{position:absolute;border-radius:50%;width:20px;height:20px;margin-top:-10px;margin-left:-10px;opacity:0;background:rgba(0,0,0,.2);-webkit-transition:all .7s ease-out;transition:all .7s ease-out;-webkit-transition-property:opacity,-webkit-transform;transition-property:opacity,-webkit-transform;transition-property:transform,opacity;transition-property:transform,opacity,-webkit-transform;-webkit-transform:scale(0);transform:scale(0);pointer-events:none}.arm-waves-effect.arm-waves-light .arm-waves-ripple{background-color:rgba(255,255,255,.45)}.arm-material-style .arm-df__form-field-wrap_textarea.arm-df__form-material-field-wrap{padding-top:10px}.is_form_class_rtl.arm-material-style .arm-df__radio input[type=radio]+label:after,.is_form_class_rtl.arm-material-style .arm-df__radio input[type=radio]+label:before{right:0}.is_form_class_rtl.arm-material-style .arm-df__radio input[type=radio]+label{padding-left:0;padding-right:28px}.is_form_class_rtl.arm-material-style .arm-df__radio:not(:first-child){margin-right:10px}