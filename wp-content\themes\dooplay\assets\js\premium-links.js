jQuery(document).ready(function($) {
    
    // Override the existing LinkSave function to include premium checkbox
    $(document).off('click', '#doolinkeditor_save');
    $(document).on('click', '#doolinkeditor_save', function() {
        var linkId = $(this).data('id');
        var isPremium = $('#doolinkeditor_premium').is(':checked') ? '1' : '';
        
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'post',
            data: {
                id: linkId,
                url: $('#doolinkeditor_url').val(),
                lang: $('#doolinkeditor_lang').val(),
                type: $('#doolinkeditor_type').val(),
                quality: $('#doolinkeditor_quality').val(),
                size: $('#doolinkeditor_size').val(),
                premium: isPremium,
                action: 'doosaveformeditor_links'
            },
            error: function(xhr) {
                console.log('Error saving link:', xhr);
            },
            success: function(response) {
                $('#link-' + linkId).addClass('fadein');
                $('#link-' + linkId).html(response);
                $('.doo_link_editor').remove();
            }
        });
        
        return false;
    });
    
    // Add visual feedback for premium checkbox
    $(document).on('change', '#doolinkeditor_premium', function() {
        var $label = $(this).next('label');
        var $span = $label.find('span');
        
        if ($(this).is(':checked')) {
            $span.css({
                'color': '#d4af37',
                'font-weight': '700'
            });
            $label.css('background', 'linear-gradient(135deg, #fff9e6, #fff3cc)');
        } else {
            $span.css({
                'color': '#666',
                'font-weight': '600'
            });
            $label.css('background', '#f9f9f9');
        }
    });
    
    // Initialize premium checkbox styling when editor opens
    $(document).on('DOMNodeInserted', '.doo_link_editor', function() {
        setTimeout(function() {
            $('#doolinkeditor_premium').trigger('change');
        }, 100);
    });
    
});
