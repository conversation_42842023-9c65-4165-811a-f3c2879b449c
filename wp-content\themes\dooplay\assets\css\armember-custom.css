/*
ARMember Custom Styling for DeshiFlix Theme
This file contains custom styles for ARMember forms and components
*/

/* Global ARMember Form Styling */
.arm_form_wrapper {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.arm_form_wrapper .arm_form {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    border-radius: 0 !important;
}

/* Form Input Containers */
.arm_form_wrapper .arm_form_input_container {
    margin-bottom: 25px !important;
    position: relative !important;
}

/* Form Labels */
.arm_form_wrapper .arm_form_input_container label,
.arm_form_wrapper .arm_form_label_wrapper label {
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 8px !important;
    display: block !important;
    font-size: 1rem !important;
    text-transform: none !important;
}

/* Form Inputs */
.arm_form_wrapper .arm_form_input_container input[type="text"],
.arm_form_wrapper .arm_form_input_container input[type="email"],
.arm_form_wrapper .arm_form_input_container input[type="password"],
.arm_form_wrapper .arm_form_input_container input[type="tel"],
.arm_form_wrapper .arm_form_input_container input[type="url"],
.arm_form_wrapper .arm_form_input_container input[type="number"],
.arm_form_wrapper .arm_form_input_container select,
.arm_form_wrapper .arm_form_input_container textarea {
    width: 100% !important;
    padding: 15px 20px !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
    color: #333 !important;
    box-sizing: border-box !important;
}

/* Input Focus States */
.arm_form_wrapper .arm_form_input_container input:focus,
.arm_form_wrapper .arm_form_input_container select:focus,
.arm_form_wrapper .arm_form_input_container textarea:focus {
    border-color: #667eea !important;
    background: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* Form Buttons */
.arm_form_wrapper .arm_form_input_container .arm_form_input_box button,
.arm_form_wrapper .arm_form_input_container input[type="submit"],
.arm_form_wrapper .arm_form_input_container .arm_form_submit_btn,
.arm_form_wrapper .armSubmitBtn {
    width: 100% !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 18px 20px !important;
    border-radius: 10px !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-top: 20px !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_input_box button:hover,
.arm_form_wrapper .arm_form_input_container input[type="submit"]:hover,
.arm_form_wrapper .arm_form_input_container .arm_form_submit_btn:hover,
.arm_form_wrapper .armSubmitBtn:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
}

/* Checkbox and Radio Styling */
.arm_form_wrapper .arm_form_input_container input[type="checkbox"],
.arm_form_wrapper .arm_form_input_container input[type="radio"] {
    width: auto !important;
    margin-right: 10px !important;
    transform: scale(1.2) !important;
    accent-color: #667eea !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_checkbox_wrapper,
.arm_form_wrapper .arm_form_input_container .arm_form_radio_wrapper {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 15px !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_checkbox_wrapper label,
.arm_form_wrapper .arm_form_input_container .arm_form_radio_wrapper label {
    margin-bottom: 0 !important;
    font-weight: 500 !important;
    cursor: pointer !important;
}

/* Error Messages */
.arm_form_wrapper .arm_form_field_error,
.arm_form_wrapper .arm_error_msg {
    color: #dc3545 !important;
    font-size: 0.9rem !important;
    margin-top: 5px !important;
    padding: 8px 12px !important;
    background: #f8d7da !important;
    border: 1px solid #f5c6cb !important;
    border-radius: 5px !important;
}

/* Success Messages */
.arm_form_wrapper .arm_success_msg,
.arm_form_wrapper .arm_form_message_ok {
    color: #155724 !important;
    background: #d4edda !important;
    border: 1px solid #c3e6cb !important;
    padding: 12px 15px !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

/* Form Links */
.arm_form_wrapper .arm_form_links,
.arm_form_wrapper .arm_form_separator_links {
    text-align: center !important;
    margin-top: 20px !important;
}

.arm_form_wrapper .arm_form_links a,
.arm_form_wrapper .arm_form_separator_links a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    margin: 0 15px !important;
    transition: color 0.3s ease !important;
}

.arm_form_wrapper .arm_form_links a:hover,
.arm_form_wrapper .arm_form_separator_links a:hover {
    color: #5a6fd8 !important;
    text-decoration: underline !important;
}

/* Plan Selection */
.arm_form_wrapper .arm_form_plan_selection {
    background: #f8f9fa !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin-bottom: 30px !important;
    border: 2px solid #e1e5e9 !important;
}

.arm_form_wrapper .arm_form_plan_selection .arm_form_plan_option {
    background: white !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    margin-bottom: 15px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.arm_form_wrapper .arm_form_plan_selection .arm_form_plan_option:hover {
    border-color: #667eea !important;
    transform: translateY(-2px) !important;
}

.arm_form_wrapper .arm_form_plan_selection .arm_form_plan_option.selected {
    border-color: #667eea !important;
    background: #f0f4ff !important;
}

/* Loading States */
.arm_form_wrapper .arm_loading {
    text-align: center !important;
    padding: 20px !important;
}

.arm_form_wrapper .arm_loading::after {
    content: '' !important;
    display: inline-block !important;
    width: 20px !important;
    height: 20px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #667eea !important;
    border-radius: 50% !important;
    animation: arm_spin 1s linear infinite !important;
}

@keyframes arm_spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .arm_form_wrapper .arm_form_input_container input,
    .arm_form_wrapper .arm_form_input_container select,
    .arm_form_wrapper .arm_form_input_container textarea {
        padding: 12px 15px !important;
        font-size: 16px !important; /* Prevents zoom on iOS */
    }
    
    .arm_form_wrapper .arm_form_input_container .arm_form_input_box button,
    .arm_form_wrapper .arm_form_input_container input[type="submit"],
    .arm_form_wrapper .armSubmitBtn {
        padding: 15px 20px !important;
        font-size: 1.1rem !important;
    }
}

/* Account Detail Styling */
.arm_account_detail_wrapper {
    background: white !important;
    border-radius: 15px !important;
    padding: 30px !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08) !important;
}

.arm_account_detail_wrapper .arm_account_detail_header {
    border-bottom: 2px solid #f0f0f0 !important;
    padding-bottom: 20px !important;
    margin-bottom: 30px !important;
}

.arm_account_detail_wrapper .arm_account_detail_header h2 {
    color: #333 !important;
    font-size: 1.8rem !important;
    font-weight: 700 !important;
}

/* Membership Cards in Account Details */
.arm_account_detail_wrapper .arm_membership_card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin-bottom: 20px !important;
    border: none !important;
}

.arm_account_detail_wrapper .arm_membership_card .arm_membership_type {
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    margin-bottom: 10px !important;
}

.arm_account_detail_wrapper .arm_membership_card .arm_membership_details {
    opacity: 0.9 !important;
    margin-bottom: 15px !important;
}

/* Payment History Table */
.arm_account_detail_wrapper .arm_payment_history_table {
    width: 100% !important;
    border-collapse: collapse !important;
    margin-top: 20px !important;
}

.arm_account_detail_wrapper .arm_payment_history_table th {
    background: #f8f9fa !important;
    color: #333 !important;
    padding: 15px !important;
    text-align: left !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #e1e5e9 !important;
}

.arm_account_detail_wrapper .arm_payment_history_table td {
    padding: 12px 15px !important;
    border-bottom: 1px solid #e1e5e9 !important;
    color: #666 !important;
}

.arm_account_detail_wrapper .arm_payment_history_table tr:hover {
    background: #f8f9fa !important;
}

/* Custom Premium Badge Integration */
.arm_form_wrapper .premium-indicator {
    background: linear-gradient(135deg, #FFD700, #FFA500) !important;
    color: #000 !important;
    padding: 8px 15px !important;
    border-radius: 20px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: inline-block !important;
    margin-bottom: 20px !important;
}

.arm_form_wrapper .premium-indicator i {
    margin-right: 5px !important;
}

/* Hide default ARMember styling that conflicts */
.arm_form_wrapper .arm_form_style,
.arm_form_wrapper .arm_form_style_1,
.arm_form_wrapper .arm_form_style_2 {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Ensure proper spacing */
.arm_form_wrapper * {
    box-sizing: border-box !important;
}

/* Custom validation styling */
.arm_form_wrapper .arm_form_input_container.arm_form_field_error input {
    border-color: #dc3545 !important;
    background: #fff5f5 !important;
}

.arm_form_wrapper .arm_form_input_container.arm_form_field_success input {
    border-color: #28a745 !important;
    background: #f8fff8 !important;
}
