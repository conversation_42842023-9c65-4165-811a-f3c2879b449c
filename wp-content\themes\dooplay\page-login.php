<?php
/*
Template Name: Login Page
Description: Custom login page template for ARMember integration
*/

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

get_header(); ?>

<style>
/* Custom Login Page Styles */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.05"><circle cx="200" cy="50" r="30"/><circle cx="400" cy="30" r="20"/><circle cx="600" cy="70" r="25"/><circle cx="800" cy="40" r="35"/></svg>');
    background-size: cover;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.login-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    position: relative;
    z-index: 2;
}

.login-left {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    position: relative;
}

.login-left::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="white" opacity="0.1"><polygon points="0,0 100,0 80,100 0,100"/></svg>');
    background-size: cover;
}

.login-logo {
    font-size: 3rem;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.login-welcome {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

.login-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 30px;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.login-features {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 2;
}

.login-features li {
    padding: 10px 0;
    font-size: 1rem;
    opacity: 0.9;
    position: relative;
    padding-left: 30px;
}

.login-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #FFD700;
    font-weight: bold;
    font-size: 1.2rem;
}

.login-right {
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-form-title {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
}

.login-form-subtitle {
    color: #666;
    text-align: center;
    margin-bottom: 40px;
    font-size: 1rem;
}

.login-form-container {
    width: 100%;
}

/* Override ARMember form styles */
.arm_form_wrapper {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.arm_form_wrapper .arm_form {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.arm_form_wrapper .arm_form_input_container {
    margin-bottom: 25px !important;
}

.arm_form_wrapper .arm_form_input_container input[type="text"],
.arm_form_wrapper .arm_form_input_container input[type="email"],
.arm_form_wrapper .arm_form_input_container input[type="password"] {
    width: 100% !important;
    padding: 15px 20px !important;
    border: 2px solid #e1e5e9 !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background: #f8f9fa !important;
}

.arm_form_wrapper .arm_form_input_container input:focus {
    border-color: #667eea !important;
    background: white !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

.arm_form_wrapper .arm_form_input_container label {
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_input_box button,
.arm_form_wrapper .arm_form_input_container input[type="submit"] {
    width: 100% !important;
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 15px 20px !important;
    border-radius: 10px !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-top: 10px !important;
}

.arm_form_wrapper .arm_form_input_container .arm_form_input_box button:hover,
.arm_form_wrapper .arm_form_input_container input[type="submit"]:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4) !important;
}

.login-links {
    text-align: center;
    margin-top: 30px;
}

.login-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    margin: 0 15px;
    transition: color 0.3s ease;
}

.login-links a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

.signup-prompt {
    text-align: center;
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #e1e5e9;
    color: #666;
}

.signup-prompt a {
    color: #667eea;
    font-weight: 600;
    text-decoration: none;
}

.signup-prompt a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .login-left {
        padding: 40px 30px;
    }
    
    .login-right {
        padding: 40px 30px;
    }
    
    .login-welcome {
        font-size: 1.5rem;
    }
    
    .login-logo {
        font-size: 2rem;
    }
}

/* Loading animation */
.login-loading {
    display: none;
    text-align: center;
    margin-top: 20px;
}

.login-loading.active {
    display: block;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<div class="login-page">
    <div class="login-container">
        <div class="login-left">
            <div class="login-logo">🎬</div>
            <h1 class="login-welcome">Welcome Back!</h1>
            <p class="login-subtitle">Sign in to continue your entertainment journey with DeshiFlix</p>
            
            <ul class="login-features">
                <li>Access thousands of movies & TV shows</li>
                <li>Stream in HD & 4K quality</li>
                <li>Download for offline viewing</li>
                <li>Ad-free premium experience</li>
            </ul>
        </div>
        
        <div class="login-right">
            <h2 class="login-form-title">Sign In</h2>
            <p class="login-form-subtitle">Enter your credentials to access your account</p>
            
            <div class="login-form-container">
                <?php
                // Display ARMember login form
                // You can replace 'login_form_id' with your actual ARMember login form ID
                if (function_exists('arm_get_form_by_id')) {
                    echo do_shortcode('[arm_form id="101"]'); // Replace with your login form ID
                } else {
                    // Fallback if ARMember is not active
                    ?>
                    <form method="post" action="<?php echo wp_login_url(); ?>">
                        <div style="margin-bottom: 20px;">
                            <label for="user_login">Username or Email</label>
                            <input type="text" name="log" id="user_login" required style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="user_pass">Password</label>
                            <input type="password" name="pwd" id="user_pass" required style="width: 100%; padding: 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label>
                                <input type="checkbox" name="rememberme" value="forever"> Remember Me
                            </label>
                        </div>
                        
                        <input type="submit" value="Sign In" style="width: 100%; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 15px; border-radius: 10px; font-size: 1.1rem; font-weight: 600; cursor: pointer;">
                        
                        <input type="hidden" name="redirect_to" value="<?php echo home_url(); ?>">
                        <?php wp_nonce_field('login'); ?>
                    </form>
                    <?php
                }
                ?>
            </div>
            
            <div class="login-links">
                <a href="<?php echo wp_lostpassword_url(); ?>">Forgot Password?</a>
                <a href="<?php echo home_url('/membership'); ?>">View Plans</a>
            </div>
            
            <div class="signup-prompt">
                Don't have an account? <a href="<?php echo home_url('/register'); ?>">Sign up now</a>
            </div>
            
            <div class="login-loading">
                <div class="spinner"></div>
                <p>Signing you in...</p>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Add loading animation on form submit
    $('.arm_form_wrapper form, form').on('submit', function() {
        $('.login-loading').addClass('active');
    });
    
    // Add focus effects to form inputs
    $('.arm_form_wrapper input, form input').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });
});
</script>

<?php get_footer(); ?>
