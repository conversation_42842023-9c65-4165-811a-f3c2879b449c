<?php
/*
* -------------------------------------------------------------------------------------
* @author: Doothemes
* <AUTHOR> https://doothemes.com/
* @copyright: (c) 2021 Doothemes. All rights reserved
* -------------------------------------------------------------------------------------
*
* Genres Menu Module - Simple horizontal genre navigation
*
*/

// Get admin options with safe defaults
$module_title = dooplay_get_option('genresmenu_title', 'Browse Genres');
$show_count = dooplay_get_option('genresmenu_show_count', true);
$layout_type = dooplay_get_option('genresmenu_layout', 'all');
$selected_genres_raw = dooplay_get_option('genresmenu_selected_genres', '');
$order_by = dooplay_get_option('genresmenu_order', 'name');
$initial_show = dooplay_get_option('genresmenu_initial_show', 8); // Show 8 genres initially

// Safely handle selected genres - robust type conversion
$selected_genres = '';
try {
    if (is_array($selected_genres_raw)) {
        // Convert array to comma-separated string
        $selected_genres = implode(',', array_filter($selected_genres_raw, 'is_numeric'));
    } elseif (is_string($selected_genres_raw)) {
        // Use string as-is
        $selected_genres = trim($selected_genres_raw);
    } elseif (is_numeric($selected_genres_raw)) {
        // Convert number to string
        $selected_genres = (string) $selected_genres_raw;
    }
    // Remove any non-numeric characters except commas and spaces
    $selected_genres = preg_replace('/[^0-9,\s]/', '', $selected_genres);
} catch (Exception $e) {
    // Fallback to empty string on any error
    $selected_genres = '';
}

// Get genres taxonomy
$taxonomy = taxonomy_exists('genres') ? 'genres' : 'category';

// Prepare query args based on layout type
$query_args = array(
    'taxonomy' => $taxonomy,
    'hide_empty' => false
);

// Set ordering
switch ($order_by) {
    case 'count':
        $query_args['orderby'] = 'count';
        $query_args['order'] = 'DESC';
        break;
    case 'id':
        $query_args['orderby'] = 'term_id';
        $query_args['order'] = 'DESC';
        break;
    default: // name
        $query_args['orderby'] = 'name';
        $query_args['order'] = 'ASC';
        break;
}

// Apply layout filters
if ($layout_type === 'selected' && !empty($selected_genres)) {
    // Convert comma-separated string to array
    $genre_ids = array_map('trim', explode(',', $selected_genres));
    $genre_ids = array_filter($genre_ids, function($id) {
        return is_numeric($id) && $id > 0;
    });
    $genre_ids = array_map('intval', $genre_ids);

    if (!empty($genre_ids)) {
        $query_args['include'] = $genre_ids;
    }
} elseif ($layout_type === 'excluded' && !empty($selected_genres)) {
    // Convert comma-separated string to array
    $genre_ids = array_map('trim', explode(',', $selected_genres));
    $genre_ids = array_filter($genre_ids, function($id) {
        return is_numeric($id) && $id > 0;
    });
    $genre_ids = array_map('intval', $genre_ids);

    if (!empty($genre_ids)) {
        $query_args['exclude'] = $genre_ids;
    }
}

$terms = get_terms($query_args);

if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) :
?>
<div class="genres-menu-wrapper">
    <div class="container">
        <?php if($module_title): ?>
        <h2 class="genres-title"><?php echo esc_html($module_title); ?></h2>
        <?php endif; ?>

        <div class="genres-list">
            <?php
            $total_genres = count($terms);
            $show_see_more = $total_genres > $initial_show;
            ?>

            <?php foreach ( $terms as $index => $term ) : ?>
                <?php
                $is_hidden = $index >= $initial_show;
                $hidden_class = $is_hidden ? ' genre-hidden' : '';
                ?>
                <a href="<?php echo esc_url( get_term_link( $term ) ); ?>"
                   class="genre-item<?php echo $hidden_class; ?>"
                   <?php echo $is_hidden ? 'style="display: none;"' : ''; ?>>
                    <span class="genre-name"><?php echo esc_html( $term->name ); ?></span>
                    <?php if($show_count): ?>
                    <span class="genre-count"><?php echo $term->count; ?></span>
                    <?php endif; ?>
                </a>
            <?php endforeach; ?>

            <?php if ($show_see_more): ?>
                <div class="genre-see-more-wrapper">
                    <button class="genre-see-more" onclick="toggleGenres(this)">
                        <div class="see-more-content">
                            <span class="see-more-text">
                                <i class="see-more-icon">⋯</i>
                                Show All Genres
                                <span class="genre-count-badge"><?php echo $total_genres; ?></span>
                            </span>
                            <span class="see-less-text" style="display: none;">
                                <i class="see-less-icon">▲</i>
                                Show Less
                            </span>
                        </div>
                        <div class="see-more-arrow">
                            <span class="arrow-down">▼</span>
                            <span class="arrow-up" style="display: none;">▲</span>
                        </div>
                    </button>
                    <div class="see-more-hint">Click to view <?php echo ($total_genres - $initial_show); ?> more genres</div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.genres-menu-wrapper {
    background: var(--body-bg, #1a1a1a);
    padding: 20px 0;
    margin-bottom: 30px;
    border-top: 1px solid rgba(255,255,255,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.genres-title {
    color: var(--text-color, #ffffff);
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 20px 0;
    text-align: center;
}

.genres-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    align-items: center;
}

.genre-item {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: var(--link-color, #408bea);
    color: #ffffff;
    text-decoration: none;
    border-radius: 25px;
    font-size: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
}

.genre-item:hover {
    background: var(--link-hover, #357abd);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    color: #ffffff;
    text-decoration: none;
}

.genre-name {
    font-weight: 500;
}

.genre-count {
    background: rgba(255,255,255,0.2);
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .genres-list {
        gap: 8px;
    }

    .genre-item {
        padding: 10px 16px;
        font-size: 14px;
        gap: 8px;
    }

    .genres-title {
        font-size: 20px;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .genre-item {
        padding: 8px 14px;
        font-size: 13px;
        gap: 6px;
    }

    .genre-count {
        font-size: 12px;
        padding: 2px 6px;
    }

    .genres-title {
        font-size: 18px;
    }

    .genre-see-more {
        padding: 8px 16px;
        font-size: 12px;
        letter-spacing: 0.3px;
        gap: 6px;
    }

    .genre-count-badge {
        font-size: 10px;
        padding: 1px 5px;
    }

    .see-more-arrow {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .see-more-hint {
        font-size: 11px;
    }
}

/* See More Button Styles - Unique Design */
.genre-see-more-wrapper {
    text-align: center;
    margin: 30px 0 20px;
}

.genre-see-more {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff6b6b);
    background-size: 200% 200%;
    border: 2px solid #fff;
    color: white;
    padding: 10px 18px;
    border-radius: 40px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.4s ease;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4),
                0 0 0 0 rgba(255, 107, 107, 0.3);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4),
                    0 0 0 0 rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6),
                    0 0 0 10px rgba(255, 107, 107, 0.1);
    }
}

.genre-see-more:hover {
    transform: translateY(-2px) scale(1.03);
    background-position: 100% 100%;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6),
                0 0 0 10px rgba(255, 107, 107, 0.1);
    animation: none;
}

.see-more-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.see-more-icon, .see-less-icon {
    font-size: 18px;
    font-weight: bold;
}

.genre-count-badge {
    background: rgba(255, 255, 255, 0.3);
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 11px;
    margin-left: 4px;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.see-more-arrow {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.genre-see-more:hover .see-more-arrow {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.see-more-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    margin-top: 8px;
    font-style: italic;
}

/* Expanded State */
.genre-see-more.expanded {
    background: linear-gradient(45deg, #2ecc71, #27ae60, #2ecc71);
    animation: none;
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4),
                0 0 0 0 rgba(46, 204, 113, 0.3);
}

.genre-see-more.expanded:hover {
    box-shadow: 0 12px 35px rgba(46, 204, 113, 0.6),
                0 0 0 15px rgba(46, 204, 113, 0.1);
}

.genre-hidden {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.genre-hidden.show {
    opacity: 1;
    transform: scale(1);
    display: inline-flex !important;
}

/* Animation for showing genres */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.genre-item.animate-in {
    animation: fadeInUp 0.3s ease forwards;
}
</style>

<script>
function toggleGenres(button) {
    const hiddenGenres = document.querySelectorAll('.genre-hidden');
    const seeMoreText = button.querySelector('.see-more-text');
    const seeLessText = button.querySelector('.see-less-text');
    const arrowDown = button.querySelector('.arrow-down');
    const arrowUp = button.querySelector('.arrow-up');
    const hint = button.parentElement.querySelector('.see-more-hint');
    const isExpanded = button.classList.contains('expanded');

    if (!isExpanded) {
        // Show hidden genres with stagger animation
        hiddenGenres.forEach((genre, index) => {
            setTimeout(() => {
                genre.style.display = 'inline-flex';
                genre.classList.add('show', 'animate-in');
            }, index * 80); // Slower stagger for better effect
        });

        // Update button state
        button.classList.add('expanded');
        seeMoreText.style.display = 'none';
        seeLessText.style.display = 'flex';
        arrowDown.style.display = 'none';
        arrowUp.style.display = 'inline';

        // Hide hint
        if (hint) {
            hint.style.opacity = '0';
            setTimeout(() => {
                hint.style.display = 'none';
            }, 300);
        }

        // Add expanded animation
        button.style.transform = 'scale(1.1)';
        setTimeout(() => {
            button.style.transform = '';
        }, 200);

    } else {
        // Hide genres with reverse animation
        const reversedGenres = Array.from(hiddenGenres).reverse();
        reversedGenres.forEach((genre, index) => {
            setTimeout(() => {
                genre.classList.remove('show', 'animate-in');
                setTimeout(() => {
                    genre.style.display = 'none';
                }, 300);
            }, index * 50);
        });

        // Update button state
        setTimeout(() => {
            button.classList.remove('expanded');
            seeMoreText.style.display = 'flex';
            seeLessText.style.display = 'none';
            arrowDown.style.display = 'inline';
            arrowUp.style.display = 'none';

            // Show hint again
            if (hint) {
                hint.style.display = 'block';
                setTimeout(() => {
                    hint.style.opacity = '1';
                }, 100);
            }
        }, 400);

        // Smooth scroll to genres section
        setTimeout(() => {
            document.querySelector('.genres-menu-wrapper').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 300);
    }
}
</script>

<?php endif; ?>