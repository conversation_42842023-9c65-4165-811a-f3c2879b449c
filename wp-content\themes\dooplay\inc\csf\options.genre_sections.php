<?php
/**
 * Genre Sections Module Options
 * @since 2.5.0
 * @version 1.0
 */

CSF::createSection(DOO_OPTIONS,
    array(
        'title'  => __d('Genre Sections'),
        'parent' => 'homepage',
        'icon'   => 'fa fa-minus',
        'fields' => array(
            array(
                'id'      => 'genre_sections_enable',
                'type'    => 'switcher',
                'title'   => __d('Enable Genre Sections'),
                'subtitle' => __d('Enable or disable genre-based content sections'),
                'default' => true,
            ),
            array(
                'id'      => 'genre_sections_title',
                'type'    => 'text',
                'title'   => __d('Module Title'),
                'default' => __d('Browse by Genre'),
                'subtitle' => __d('Main title for the genre sections module'),
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_subtitle',
                'type'    => 'text',
                'title'   => __d('Module Subtitle'),
                'default' => __d('Discover content by your favorite genres'),
                'subtitle' => __d('Subtitle text for the genre sections module'),
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_layout',
                'type'    => 'radio',
                'title'   => __d('Layout Style'),
                'subtitle' => __d('Choose how to display genre sections'),
                'options' => array(
                    'grid'     => __d('Grid Layout'),
                    'carousel' => __d('Carousel Layout'),
                    'tabs'     => __d('Tabs Layout'),
                ),
                'default' => 'grid',
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_items_per_section',
                'type'    => 'number',
                'title'   => __d('Items per Section'),
                'subtitle' => __d('Number of items to show in each genre section'),
                'default' => 8,
                'min'     => 4,
                'max'     => 20,
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_columns',
                'type'    => 'slider',
                'title'   => __d('Columns per Row'),
                'subtitle' => __d('Number of columns to display per row'),
                'min'     => 2,
                'max'     => 8,
                'step'    => 1,
                'default' => 4,
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_show_count',
                'type'    => 'switcher',
                'title'   => __d('Show Item Count'),
                'subtitle' => __d('Display number of items in each genre'),
                'default' => true,
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_show_see_all',
                'type'    => 'switcher',
                'title'   => __d('Show "See All" Link'),
                'subtitle' => __d('Display "See All" link for each genre section'),
                'default' => true,
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_selected',
                'type'    => 'select',
                'title'   => __d('Select Genres'),
                'subtitle' => __d('Choose which genres to display as sections'),
                'multiple' => true,
                'chosen'   => true,
                'ajax'     => true,
                'options'  => 'dooplay_get_genres_for_options',
                'placeholder' => __d('Select genres to display...'),
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_order',
                'type'    => 'radio',
                'title'   => __d('Genre Order'),
                'subtitle' => __d('How to order the selected genres'),
                'options' => array(
                    'custom'  => __d('Custom Order (as selected above)'),
                    'name'    => __d('Alphabetical'),
                    'count'   => __d('By Item Count'),
                    'random'  => __d('Random'),
                ),
                'default' => 'custom',
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_content_type',
                'type'    => 'checkbox',
                'title'   => __d('Content Types'),
                'subtitle' => __d('Select which content types to include'),
                'options' => array(
                    'movies'   => __d('Movies'),
                    'tvshows'  => __d('TV Shows'),
                    'episodes' => __d('Episodes'),
                ),
                'default' => array('movies', 'tvshows'),
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_sort_content',
                'type'    => 'radio',
                'title'   => __d('Content Sorting'),
                'subtitle' => __d('How to sort content within each genre'),
                'options' => array(
                    'date'     => __d('Latest First'),
                    'title'    => __d('Alphabetical'),
                    'rating'   => __d('By Rating'),
                    'views'    => __d('Most Viewed'),
                    'random'   => __d('Random'),
                ),
                'default' => 'date',
                'dependency' => array('genre_sections_enable', '==', 'true'),
            ),
            array(
                'id'      => 'genre_sections_autoplay',
                'type'    => 'switcher',
                'title'   => __d('Autoplay Carousel'),
                'subtitle' => __d('Enable autoplay for carousel layout'),
                'default' => false,
                'dependency' => array(
                    array('genre_sections_enable', '==', 'true'),
                    array('genre_sections_layout', '==', 'carousel'),
                ),
            ),
            array(
                'id'      => 'genre_sections_autoplay_speed',
                'type'    => 'number',
                'title'   => __d('Autoplay Speed'),
                'subtitle' => __d('Autoplay speed in milliseconds'),
                'default' => 5000,
                'min'     => 1000,
                'max'     => 10000,
                'dependency' => array(
                    array('genre_sections_enable', '==', 'true'),
                    array('genre_sections_layout', '==', 'carousel'),
                    array('genre_sections_autoplay', '==', 'true'),
                ),
            ),
        )
    )
);
