<?php
/*
* Live TV Channels Archive Page
* @since 2.5.0
*/

get_header(); 

// Get current category filter
$current_category = isset($_GET['category']) ? sanitize_text_field($_GET['category']) : '';

// Get all categories
$categories = array(
    'news' => __('News', 'dooplay'),
    'entertainment' => __('Entertainment', 'dooplay'),
    'sports' => __('Sports', 'dooplay'),
    'music' => __('Music', 'dooplay'),
    'kids' => __('Kids', 'dooplay'),
    'religious' => __('Religious', 'dooplay'),
    'other' => __('Other', 'dooplay')
);

// Query args
$args = array(
    'post_type' => 'live_tv_channels',
    'posts_per_page' => 24,
    'post_status' => 'publish',
    'meta_query' => array(
        array(
            'key' => '_live_tv_is_active',
            'value' => '1',
            'compare' => '='
        )
    )
);

if (!empty($current_category)) {
    $args['meta_query'][] = array(
        'key' => '_live_tv_category',
        'value' => $current_category,
        'compare' => '='
    );
}

$channels_query = new WP_Query($args);
?>

<div id="archive" class="live-tv-archive">
    <div class="container">
        <div class="live-tv-header">
            <h1 class="page-title">
                <i class="fas fa-tv"></i>
                <?php _e('Live TV Channels', 'dooplay'); ?>
            </h1>
            <p class="page-description"><?php _e('Watch live TV channels online for free', 'dooplay'); ?></p>
        </div>

        <!-- Category Filter -->
        <div class="live-tv-filters">
            <div class="filter-tabs">
                <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" 
                   class="filter-tab <?php echo empty($current_category) ? 'active' : ''; ?>">
                    <i class="fas fa-th-large"></i>
                    <?php _e('All', 'dooplay'); ?>
                </a>
                <?php foreach ($categories as $cat_key => $cat_name): ?>
                <a href="<?php echo add_query_arg('category', $cat_key, get_post_type_archive_link('live_tv_channels')); ?>" 
                   class="filter-tab <?php echo $current_category === $cat_key ? 'active' : ''; ?>">
                    <?php 
                    $icons = array(
                        'news' => 'fas fa-newspaper',
                        'entertainment' => 'fas fa-film',
                        'sports' => 'fas fa-futbol',
                        'music' => 'fas fa-music',
                        'kids' => 'fas fa-child',
                        'religious' => 'fas fa-pray',
                        'other' => 'fas fa-ellipsis-h'
                    );
                    ?>
                    <i class="<?php echo $icons[$cat_key]; ?>"></i>
                    <?php echo $cat_name; ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Channels Grid -->
        <div class="live-tv-grid">
            <?php if ($channels_query->have_posts()): ?>
                <?php while ($channels_query->have_posts()): $channels_query->the_post(); ?>
                    <?php
                    $stream_url = get_post_meta(get_the_ID(), '_live_tv_stream_url', true);
                    $channel_logo = get_post_meta(get_the_ID(), '_live_tv_channel_logo', true);
                    $channel_category = get_post_meta(get_the_ID(), '_live_tv_category', true);
                    ?>
                    <div class="live-tv-channel-card" data-stream="<?php echo esc_attr($stream_url); ?>">
                        <div class="channel-thumbnail">
                            <?php if ($channel_logo): ?>
                                <img src="<?php echo esc_url($channel_logo); ?>" alt="<?php the_title(); ?>" />
                            <?php else: ?>
                                <div class="channel-placeholder">
                                    <i class="fas fa-tv"></i>
                                </div>
                            <?php endif; ?>
                            <div class="channel-overlay">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="channel-status">
                                    <span class="live-indicator">
                                        <i class="fas fa-circle"></i>
                                        <?php _e('LIVE', 'dooplay'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="channel-info">
                            <h3 class="channel-title"><?php the_title(); ?></h3>
                            <span class="channel-category">
                                <?php echo isset($categories[$channel_category]) ? $categories[$channel_category] : ucfirst($channel_category); ?>
                            </span>
                        </div>
                    </div>
                <?php endwhile; ?>
                <?php wp_reset_postdata(); ?>
            <?php else: ?>
                <div class="no-channels-found">
                    <i class="fas fa-tv"></i>
                    <h3><?php _e('No channels found', 'dooplay'); ?></h3>
                    <p><?php _e('No live TV channels are available at the moment.', 'dooplay'); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($channels_query->max_num_pages > 1): ?>
        <div class="live-tv-pagination">
            <?php
            echo paginate_links(array(
                'total' => $channels_query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 1,
                'mid_size' => 2,
                'prev_next' => true,
                'prev_text' => '<i class="fas fa-chevron-left"></i>',
                'next_text' => '<i class="fas fa-chevron-right"></i>',
                'type' => 'list'
            ));
            ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Live TV Player Modal -->
<div id="live-tv-player-modal" class="live-tv-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="player-channel-title"><?php _e('Live TV Player', 'dooplay'); ?></h3>
            <button class="modal-close" id="close-player">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="live-tv-player-container">
                <video id="live-tv-player" controls autoplay>
                    <source src="" type="application/x-mpegURL">
                    <?php _e('Your browser does not support the video tag.', 'dooplay'); ?>
                </video>
            </div>
            <div class="player-controls">
                <button id="player-fullscreen" class="control-btn">
                    <i class="fas fa-expand"></i>
                    <?php _e('Fullscreen', 'dooplay'); ?>
                </button>
                <button id="player-volume" class="control-btn">
                    <i class="fas fa-volume-up"></i>
                    <?php _e('Volume', 'dooplay'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.live-tv-archive {
    padding: 20px 0;
}

.live-tv-header {
    text-align: center;
    margin-bottom: 30px;
}

.live-tv-header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    color: #333;
}

.live-tv-header i {
    margin-right: 10px;
    color: #e74c3c;
}

.page-description {
    font-size: 1.1em;
    color: #666;
    margin: 0;
}

.live-tv-filters {
    margin-bottom: 30px;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.filter-tab {
    padding: 10px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-tab:hover,
.filter-tab.active {
    background: #e74c3c;
    border-color: #e74c3c;
    color: white;
    text-decoration: none;
}

.live-tv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.live-tv-channel-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.live-tv-channel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.channel-thumbnail {
    position: relative;
    height: 160px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.channel-thumbnail img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.channel-placeholder {
    font-size: 3em;
    color: #dee2e6;
}

.channel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.live-tv-channel-card:hover .channel-overlay {
    opacity: 1;
}

.play-button {
    background: #e74c3c;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5em;
}

.channel-status {
    position: absolute;
    top: 10px;
    right: 10px;
}

.live-indicator {
    background: #e74c3c;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
}

.live-indicator i {
    animation: pulse 1.5s infinite;
    margin-right: 4px;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.channel-info {
    padding: 15px;
}

.channel-title {
    margin: 0 0 5px 0;
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
}

.channel-category {
    color: #666;
    font-size: 0.9em;
    text-transform: capitalize;
}

.no-channels-found {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-channels-found i {
    font-size: 4em;
    margin-bottom: 20px;
    color: #dee2e6;
}

.live-tv-modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    overflow: auto;
}

.modal-content {
    position: relative;
    margin: 3% auto;
    width: 85%;
    max-width: 1000px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.modal-header {
    background: #333;
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 500;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.8em;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.3s ease;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255,255,255,0.1);
}

.modal-body {
    padding: 0;
    position: relative;
}

#live-tv-player-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #000;
}

#live-tv-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
}

.player-controls {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
}

.control-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.control-btn:hover {
    background: #c0392b;
}

@media (max-width: 768px) {
    .filter-tabs {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .filter-tab {
        flex-shrink: 0;
    }

    .live-tv-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-width: none;
    }

    .modal-header {
        padding: 10px 15px;
    }

    .modal-header h3 {
        font-size: 1.1em;
        padding-right: 50px;
    }

    .modal-close {
        font-size: 1.5em;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 1% auto;
        border-radius: 0;
    }

    .modal-header {
        padding: 8px 12px;
    }

    .modal-header h3 {
        font-size: 1em;
    }

    .modal-close {
        font-size: 1.3em;
        width: 30px;
        height: 30px;
    }
}
</style>

<?php get_footer(); ?>
