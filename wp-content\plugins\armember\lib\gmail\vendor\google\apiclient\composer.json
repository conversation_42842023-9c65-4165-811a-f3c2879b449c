{"name": "google/apiclient", "type": "library", "description": "Client library for Google APIs", "keywords": ["google"], "homepage": "http://developers.google.com/api-client-library/php", "license": "Apache-2.0", "require": {"php": "^5.6|^7.0|^8.0", "google/auth": "^1.10", "google/apiclient-services": "~0.200", "firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0||~6.0", "monolog/monolog": "^1.17||^2.0||^3.0", "phpseclib/phpseclib": "~2.0||^3.0.2", "guzzlehttp/guzzle": "~5.3.3||~6.0||~7.0", "guzzlehttp/psr7": "^1.8.4||^2.2.1"}, "require-dev": {"squizlabs/php_codesniffer": "^3.0", "symfony/dom-crawler": "~2.1", "symfony/css-selector": "~2.1", "cache/filesystem-adapter": "^0.3.2|^1.1", "phpcompatibility/php-compatibility": "^9.2", "composer/composer": "^1.10.22", "yoast/phpunit-polyfills": "^1.0", "phpspec/prophecy-phpunit": "^1.1||^2.0", "phpunit/phpunit": "^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "autoload": {"psr-4": {"Google\\": "src/"}, "files": ["src/aliases.php"], "classmap": ["src/aliases.php"]}, "extra": {"branch-alias": {"dev-main": "2.x-dev"}}}