function ARMFormInitValidation(a){a=void 0===a?'form[name="arm_form"],form[name="arm_update_card_form"],form[name="arm_form_ca"]':"#"+a,jQuery(a).find("input,select,textarea").not("[type=submit]").ARMjqBootstrapValidation({preventSubmit:!0,prependExistingHelpBlock:!1,autoAdd:{helpBlocks:!0},submitError:function(a,e,t){var r,n=jQuery(a).attr("id");return 0<Object.keys(t).length&&(r="1",jQuery.each(t,function(a,e){var t;"1"==r&&(0<=(t=jQuery("#"+n+" [name='"+a+"']")[0].getBoundingClientRect()).top&&t.bottom<=window.innerHeight?setTimeout(function(){jQuery("#"+n+" input[type='text'][name='"+a+"'],#"+n+" input[type='password'][name='"+a+"'],#"+n+" input[type='email'][name='"+a+"']").focus()},200):(jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery("#"+n+" [name='"+a+"']").offset().top-200},500),setTimeout(function(){jQuery("#"+n+" input[type='text'][name='"+a+"'],#"+n+" input[type='password'][name='"+a+"'],#"+n+" input[type='email'][name='"+a+"']").focus()},500))),r=parseInt(r)+1})),e.preventDefault(),!1},submitSuccess:function(a,e){var t=jQuery(a).attr("id"),r=jQuery(a).attr("name");return"arm_update_card_form"==r?arm_update_card_form_ajax_action(a):"arm_form_ca"==r?arm_form_close_account_action(jQuery(a)):"arm_form"==r&&armFormSubmit(t,e),setTimeout(function(){jQuery("#"+t+" [name]").removeClass("arm_invalid")},1e3),e.preventDefault(),!1},filter:function(){return jQuery(this).is(":visible")}})}function armPlanChange(a){var e,t,r=jQuery("#"+a+" .arm_module_plan_input"),n=jQuery(r).val(),_=jQuery("#"+a).find(".arm_plan_option_check_"+n),i=(jQuery("#"+a+' input:radio[name="arm_selected_payment_mode"]').length&&jQuery("#"+a+' input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0),armSetupHideShowSections1("#"+a,_),jQuery("#"+a+' [data-id="arm_front_gateway_skin_type"]').val()),d=jQuery("#"+a).find('[data-id="arm_front_plan_skin_type"]').val(),_=(jQuery("#"+a).find('[data-id="arm_form_plan_type"]').val(_.attr("data-type")),n);0<jQuery("#"+a+" .arm_gm_sub_user_"+_).length&&(e=jQuery("#"+a+" #gm_sub_user_select_"+_).val(),t=(t=parseFloat(jQuery("#"+a+" .arm_plan_amount_text").html())/e-parseFloat(jQuery("#"+a+" .arm_discount_amount_text").html().substr(1)))*e,arm_gm_tax_calculation(t,_),jQuery("span").hasClass("arm_selected_child_users")&&jQuery(".arm_selected_child_users").html("<b>"+e+"</b>")),"dropdown"!=i&&"skin5"!=d||(r=jQuery("#"+a+" .arm_module_gateway_input"),armPaymentGatewayChange(a,jQuery(r).val()),_=n,0<jQuery("#"+a+" .arm_gm_sub_user_"+_).length?(jQuery("#"+a+" .arm_gm_setup_sub_user_selection_main_wrapper").css("display","none"),jQuery("#"+a+" #arm_gm_sub_user_select_"+_).css("display","block")):jQuery("#"+a+" .arm_gm_setup_sub_user_selection_main_wrapper").css("display","none"))}function armPaymentGatewayChange(a,e=""){""==e&&(e="radio"==jQuery('[data-id="arm_front_gateway_skin_type"]').val()?jQuery("#"+a).find(".arm_module_gateways_container input.arm_module_gateway_input:checked").val():(t=jQuery("#"+a).find(".arm_module_gateway_input"),jQuery(t).val()));var t,r=jQuery("#"+a+" .arm_module_gateway_input"),n=jQuery("#"+a+' [data-id="arm_total_payable_amount"]').val(),r=jQuery(r).parent("dl").find('li.arm__dc--item[data-value="'+e+'"]').attr("data-payment_mode");"both"==r&&(r="auto_debit_subscription"),("0.000"==n||"0.00"==n||"0.0"==n||"0"==n)&&"auto_debit_subscription"!=r?jQuery("#"+a+" .arm_module_gateway_fields").slideUp("slow").addClass("arm_hide"):(jQuery("#"+a+" .arm_module_gateway_fields").not(".arm_module_gateway_fields_"+e).slideUp("slow").addClass("arm_hide"),jQuery("#"+a+" .arm_module_gateway_fields_"+e).slideDown("slow").removeClass("arm_hide")),"skin5"!=jQuery("#"+a+' [data-id="arm_front_plan_skin_type"]').val()?armSetupHideShowSections("#"+a):(n=(t=jQuery("#"+a+" .arm_module_plan_input")).val(),""!=(r=t.parent("dl").find('li.arm__dc--item[data-value="'+n+'"]'))&&armSetupHideShowSections1("#"+a,r))}function armPaymentCycleChange(a,e){var t,e=jQuery("#"+e),r=e.find('input[name="payment_cycle_'+a+'"]').val(),n=jQuery(e).find(".arm_payment_cycle_box_"+a+' [data-value="'+r+'"][data-plan_id="'+a+'"]').attr("data-plan_amount"),_=jQuery(e).find(".arm_payment_cycle_box_"+a+' [data-value="'+r+'"][data-plan_id="'+a+'"]').attr("data-cycle_label"),i=jQuery(e).find('[data-id="arm_front_plan_skin_type"]').val(),d=e.find('input[name="payment_cycle_'+a+'"]').attr("data-old_value"),d=(""!=d&&void 0!==d||(d=0),e.find('input[name="payment_cycle_'+a+'"]').parent().find('li[data-value="'+d+'"]').attr("data-plan_amount"));"skin5"==i?(i=jQuery(e).find('input[name="subscription_plan"]'),t=jQuery(i).val(),t=jQuery(i).parent("dl").find('li.arm__dc--item[data-value="'+t+'"]'),cycle_label=jQuery(e).find('input[name="payment_cycle_'+a+'"][value="'+r+'"]').parents(".arm-df__form-field-wrap").find('li[data-value="'+r+'"][data-plan_id="'+a+'"]').attr("data-label"),jQuery(e).find(".arm_payment_cycle_box_"+a+' [data-value="'+r+'"][data-plan_id="'+a+'"]').attr("data-cycle_label",cycle_label),t.parents(".arm_setup_column_item").find(".arm_module_plan_cycle_price").html(n),r=jQuery(i).parent("dl").find(".arm__dc--head__title").html().replace(d,n),jQuery(i).parent("dl").find(".arm__dc--head__title").html(r),jQuery(i).parent("dl").find(".arm-df__dc--head__autocomplete").val(r),t.attr("data-label",r).html(r)):(t=jQuery(e).find("input.arm_module_plan_input:checked")).parents(".arm_setup_column_item").find(".arm_module_plan_cycle_price").html(n),t.attr("data-amt",n),t.attr("data-cycle_label",_),armResetCouponCode(e),armUpdateOrderAmount1(t,e)}function armSubmitBtnClick(a){if(null!=a.isTrigger&&a.isTrigger)return a.preventDefault(),!1}function resetApplyCouponBox(){}function arm_df__dropdown_control_init(){jQuery(".arm-df__dropdown-control:not(.arm_achive_badges_edit_selectbox)").each(function(){var a=jQuery(this).parents("form").hasClass("arm--material-outline-style")||jQuery(this).parents("form").hasClass("arm-material-style"),r=jQuery(this),e=r.find("dd ul"),n=e.attr("data-id"),_=jQuery("#"+n).val();e.find("li").each(function(){var a=jQuery(this).text(),e=jQuery(this).attr("data-value"),t=jQuery(this).attr("data-type");e==_&&("0"!=e&&""!=e&&r.find("dt span").text(a),r.find("dt input").val(a),jQuery("#"+n).attr("data-type",t))}),1==a&&(""!=_&&0!=_?(r.parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").addClass("arm-df__form-material-field-wrap"),r.parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find("label").addClass("active"),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").show()):(r.parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").removeClass("arm-df__form-material-field-wrap"),r.parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find("label").removeClass("active"),r.parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").hide()))}),jQuery(".arm_multiple_selectbox").each(function(){var t,a=jQuery(this),e=a.find("dd ul"),r=e.attr("data-id"),n=e.attr("data-placeholder"),r=jQuery("#"+r).val(),_=[];""!=r&&null!=r?(t=r.split(","),e.find("li").each(function(){var a=jQuery(this).text(),e=jQuery(this).attr("data-value");jQuery(this).attr("data-type");-1!=jQuery.inArray(e,t)&&(jQuery(this).find('input[type="checkbox"]').prop("checked",!0),_.push(a))}),""!=_&&(a.find("dt span").text(_.join(", ")),a.find("dt input").val(_.join(", ")))):a.find("dt span").text(n)})}function armresetradiofield(a){jQuery("."+a).prop("checked",!1).trigger("change")}function arm_existcheck_function(a,e,t){var r=jQuery(a).attr("data-validation-callback-message"),n=jQuery(a).attr("name"),a=jQuery(a).attr("id"),_=jQuery("#"+a).closest("form").attr("id");""!=e&&(jQuery("#"+_+" [name='"+n+"']").removeClass("arm_invalid"),a=jQuery("#"+_).find('input[name="arm_wp_nonce"]').last().val(),jQuery.post(__ARMAJAXURL,{action:"arm_check_exist_field",field:n,value:e,_wpnonce:a}).then(function(a){"0"==jQuery.parseJSON(a).check?(t({value:e,valid:!1,message:r}),jQuery("#"+_+" [name='"+n+"']").addClass("arm_invalid")):t({value:e,valid:!0,message:r})}))}function arm_usernamecheck_function(a,e,t){var r=jQuery(a).attr("data-validation-usernamecheck-message"),n=/^[0-9a-z_]+$/i;t({value:e,valid:(n=jQuery(a).hasClass("arm_multisite_validate")?/^[0-9a-z_]+$/:n).test(e),message:r})}function arm_flnamecheck_function(a,e,t){a=jQuery(a).attr("data-validation-flnamecheck-message");t({value:e,valid:!/[%`\^&*(){}[\]<>?|]/.test(e),message:a})}function arm_customvalidationalpha_function(a,e,t){a=jQuery(a).attr("data-validation-customvalidationalpha-message");t({value:e,valid:!/[^a-zA-Z._]/.test(e),message:a})}function arm_customvalidationnumber_function(a,e,t){a=jQuery(a).attr("data-validation-customvalidationnumber-message");t({value:e,valid:!/[^0-9._]/.test(e),message:a})}function arm_customvalidationalphanumber_function(a,e,t){a=jQuery(a).attr("data-validation-customvalidationalphanumber-message");t({value:e,valid:!/[^a-zA-Z0-9._]/.test(e),message:a})}function armlowercase_function(a,e,t){a=jQuery(a).attr("data-validation-armlowercase-message");t({value:e,valid:/[a-z]/.test(e),message:a})}function armuppercase_function(a,e,t){a=jQuery(a).attr("data-validation-armuppercase-message");t({value:e,valid:/[A-Z]/.test(e),message:a})}function armnumeric_function(a,e,t){a=jQuery(a).attr("data-validation-armnumeric-message");t({value:e,valid:/[0-9]/.test(e),message:a})}function armspecial_function(a,e,t){a=jQuery(a).attr("data-validation-armspecial-message");t({value:e,valid:/[!,%,&,@,#,$,^,*,?,_,~,-,+]/.test(e),message:a})}function arm_urlcheck_function(a,e,t){a=jQuery(a).attr("data-validation-urlcheck-message");t({value:e,valid:new RegExp("((https?|ftp)\\:\\/\\/)?((HTTPS?|ftp)\\:\\/\\/)?([A-Za-z0-9+!*(),;?&=\\$_.-]+(\\:[A-Za-z0-9+!*(),;?&=\\$_.-]+)?@)?([A-Za-z0-9-.]*)\\.([A-Za-z]+)(\\:[0-9]{2,5})?(\\/([A-Za-z0-9+!$_-]\\.?)+)*\\/?(\\?[A-Za-z+&\\$_.-][A-Za-z0-9;:@&%=+\\/\\$_.-]*)?(#[A-Za-z_.-][A-Za-z0-9+\\$_.-]*)?").test(e),message:a})}function armFormSubmit(a,e){if(1<=jQuery("#"+a).find(".arm-df__form-control_sms_otp").length){var t=jQuery("#"+a).find("input[type='hidden'][name='arm_sms_nonce']").val(),r=jQuery("#"+a).find("input[type='text'][name='arm_sms_otp']").val();if(""!=t&&void 0!==t&&""!=r&&void 0!==r&&r!=(t=atob(t)))return!1}"radio"==jQuery('[data-id="arm_front_gateway_skin_type"]').val()?jQuery(".arm_module_gateways_container input.arm_module_gateway_input:checked").val():(r=jQuery(".arm_module_gateway_input"),jQuery(r).val());t=jQuery("#"+a).attr("data-random-id");return jQuery("#"+a).hasClass("arm_membership_setup_form")?0<jQuery("#arm_setup_form"+t).find("#arm_captcha_"+t).length?("undefined"!=typeof grecaptcha&&grecaptcha.reset(),arm_form_ajax_action_after_reload_recaptcha(jQuery("#"+a),"setup_form")):arm_setup_form_ajax_action(jQuery("#"+a)):0<jQuery("#"+a).parent(".arm-form-container").find('input[name="arm_captcha_'+t+'"]').length?("undefined"!=typeof grecaptcha&&grecaptcha.reset(),arm_form_ajax_action_after_reload_recaptcha(jQuery("#"+a),"other_form")):arm_form_ajax_action(jQuery("#"+a)),!1}function armSetTax(a){var e,t=jQuery("#"+a);"skin5"==t.find('[data-id="arm_front_plan_skin_type"]').val()?(a=jQuery(t).find(".arm_module_plan_input").val(),void 0!==(e=jQuery(t).find(".arm_plan_option_check_"+a)).attr("data-tax")&&setTimeout(function(){armResetCouponCode(t),armUpdateOrderAmount1(e,t,0)},350)):setTimeout(function(){armResetCouponCode(t),armUpdateOrderAmount(t,0,"")},350)}function isPaymentGatewayField(a){var e,t=jQuery('[data-id="arm_form_plan_type"]').val();return(null==t||"free"!=t)&&(("radio"==jQuery('[data-id="arm_front_gateway_skin_type"]').val()?jQuery(".arm_module_gateways_container input.arm_module_gateway_input:checked"):e=jQuery(".arm_module_gateway_input")).val()==a&&(t=jQuery('[data-id="arm_total_payable_amount"]').val(),a=jQuery("[name=arm_selected_payment_mode]:checked").val(),"recurring"==("skin5"==jQuery('[data-id="arm_front_plan_skin_type"]').val()?(e=jQuery(".arm_module_gateway_input"),jQuery(e).val()):jQuery(".arm_module_plan_input:checked").attr("data-type"))?"0.00"!=t&&"0"!=t||"auto_debit_subscription"==a:"0.00"!=t&&"0"!=t))}jQuery(document).ready(function(){ARMFormInitValidation(),jQuery(".arm_membership_setup_form").each(function(a,e){var t="";"skin5"!=jQuery(this).find('[data-id="arm_front_plan_skin_type"]').val()&&(t=":checked"),jQuery(this).find("input.arm_module_plan_input"+t).trigger("change")});var n={colors:["#F00","#F90","#FF0","#9F0","#0F0"],mesureStrength:function(a){var e=0,t=/[a-z]+/.test(a),r=/[A-Z]+/.test(a),n=/[0-9]+/.test(a),_=/[!,%,&,@,#,$,^,*,?,_,~,-,+]/g.test(a),t=jQuery.grep([t,r,n,_],function(a){return!0===a}).length,e=e+(2*a.length+(8<=a.length?1:0))+10*t;return e=a.length<=5?Math.min(e,10):e,e=1==t?Math.min(e,20):e,e=2==t?Math.min(e,30):e,e=3==t?Math.min(e,40):e,e=(e=(e=(e=4==t?Math.min(e,50):e)<30&&10<=a.length?30:e)<40&&15<=a.length?40:e)<50&&20<=a.length?50:e},getColor:function(a){var e=0;return{idx:(e=a<=10?0:a<=20?1:a<=30?2:a<=40?3:a<=50?4:5)+1,col:this.colors[e]}}};jQuery(".armFileUploadWrapper input[armfileuploader]").each(function(a,e){var t=jQuery(e).prev().find(".armFileUploaderWrapper")[0];t&&t.addEventListener("drop",function(a){e.parents(".armFileUploadWrapper").find("input.arm_file_url").on("change",function(a){jQuery(this).val()})})}),jQuery(".arm_strength_meter_block_container[check-strength]").each(function(a,e){var t=jQuery(this),r=(jQuery(this).closest("form").attr("id"),jQuery(this).attr("check-strength"));t.html('<li class="arm_strength_meter_block arm_strength_meter_rank1" style="background: rgb(255, 0, 0);"></li><li class="arm_strength_meter_block" style="background: rgb(221, 221, 221);"></li><li class="arm_strength_meter_block" style="background: rgb(221, 221, 221);"></li><li class="arm_strength_meter_block" style="background: rgb(221, 221, 221);"></li><li class="arm_strength_meter_block" style="background: rgb(221, 221, 221);"></li>'),jQuery(document).on("change, keyup","#"+r,function(){var a=jQuery(this).val(),e=jQuery(this).attr("id"),e=jQuery('.arm_strength_meter_block_container[check-strength="'+e+'"]');arm_pwdstrength_vweak=e.attr("data-field-veryweak"),arm_pwdstrength_weak=e.attr("data-field-weak"),arm_pwdstrength_good=e.attr("data-field-good"),arm_pwdstrength_vgood=e.attr("data-field-strong"),""===a||null==a?(e.css({display:"inline"}),e.children("li").css({background:"#DDD"}).slice(0,1).css({background:"#F00"}),e.parents(".arm-df__form-field").find(".arm_strength_meter_label").addClass("too_short").html(arm_pwdstrength_vweak)):(e.parents(".arm_pass_strength_meter").find(".arm_strength_meter_block").removeClass("arm_strength_meter_rank1").removeClass("arm_strength_meter_rank2").removeClass("arm_strength_meter_rank3").removeClass("arm_strength_meter_rank4").removeClass("arm_strength_meter_rank5"),a=n.getColor(n.mesureStrength(a)),e.css({display:"inline"}),e.children("li").css({background:"#DDD"}).slice(0,a.idx).css({background:a.col}).addClass("arm_strength_meter_rank"+a.idx),a.idx<2?e.parents(".arm_pass_strength_meter").find(".arm_strength_meter_label").addClass("too_short").html(arm_pwdstrength_vweak):2==a.idx?e.parents(".arm_pass_strength_meter").find(".arm_strength_meter_label").addClass("weak").html(arm_pwdstrength_weak):2<a.idx&&a.idx<5?e.parents(".arm_pass_strength_meter").find(".arm_strength_meter_label").addClass("good").html(arm_pwdstrength_good):4<a.idx&&e.parents(".arm_pass_strength_meter").find(".arm_strength_meter_label").addClass("strong").html(arm_pwdstrength_vgood))})}),arm_df__dropdown_control_init()}),jQuery(document).on("click",".arm_coupon_submit_wrapper .arm_apply_coupon_btn",function(a){if(null!=a.isTrigger&&a.isTrigger)return a.preventDefault(),!1;var e,t,i,r,d=jQuery(this).parents(".arm_apply_coupon_container"),l=d.parents("form"),a=(jQuery(this).attr("disabled",!0),d.find(".arm_coupon_code").val()),n=l.find('[data-id="arm_front_plan_skin_type"]').val(),_=l.find('[data-id="arm_front_gateway_skin_type"]').val(),o=l.find('[data-id="arm_user_old_plan"]').val(),m=l.find('[data-id="arm_setup_id"]').val(),u=l.find('input[name="arm_wp_nonce"]').val(),s=d.find(".arm_coupon_code").attr("data-checkcouponcode-message"),_=(i="skin5"==n?(p=l.find(".arm_module_plan_input"),e=jQuery(p).val(),t=l.find(".arm_payment_cycle_box_"+e+" .arm_module_cycle_input"),t=jQuery(t).val(),r=l.attr("id"),r=jQuery("#"+r+" .arm_module_plan_input"),jQuery(r).parent("dl").find('li.arm__dc--item[data-value="'+e+'"]')):(e=l.find(".arm_module_plans_container .arm_module_plan_input:checked").val(),t=l.find(".arm_payment_cycle_box_"+e+" .arm_module_cycle_input:checked").val(),l.find("input.arm_module_plan_input:checked")),r="radio"==_?l.find(".arm_module_gateways_container .arm_module_gateway_input:checked").val():(p=l.find(".arm_module_gateway_input"),jQuery(p).val()),null==t&&(t=0),l.find('input[name="arm_selected_payment_mode"]:checked').val()),p=(d.find(".arm-df__form-field-wrap_coupon_code").parent().find("span.notify_msg").remove(),0);d.find("div.arm-df__fc--validation").html(""),""!=a&&void 0!==a?(a={action:"arm_apply_coupon_code",coupon_code:a,plan_id:e,setup_id:m,gateway:r,payment_mode:_,payment_cycle:t,user_old_plan:o,arm_gm_child_users:p="undefined"!=typeof __ARMGM&&0<l.find("#gm_sub_user_select_"+e).length?l.find("#gm_sub_user_select_"+e).val():p,arm_selected_child_users:p,_wpnonce:u},d.find(".arm_apply_coupon_btn").addClass("active"),jQuery.post(__ARMAJAXURL,a).then(function(a){var e,a=jQuery.parseJSON(a),t="",r="",n=i.attr("data-amt"),_=0;"success"==a.status?(d.find(".arm_apply_coupon_btn").removeClass("active"),t=a.total_amt,r=a.coupon_amt,e=a.arm_coupon_on_each_subscriptions,_=a.discount,a.discount_type,null!=_&&(_=_.replace(",","")),""!=e&&"0"!=e&&(null!=(e=i.attr("data-amt"))&&(e=e.replace(",","")),(n="fixed"==a.discount_type?parseFloat(e)-parseFloat(_):(n=parseFloat(e)*parseFloat(_)/parseFloat(100),parseFloat(e)-parseFloat(n)))<=0&&(n=1e-4)),d.find(".arm-df__form-field-wrap_coupon_code").after('<span class="success notify_msg">'+a.message+"</span>")):(d.find(".arm_apply_coupon_btn").removeClass("active"),d.find("div.arm-df__fc--validation").html('<div class="arm-df__fc--validation__wrap" style="display:block;"><div class="arm_error_box_arrow"></div>'+a.message+"</div>").show()),"true"==l.find('input[name="arm_coupon_code"]').attr("data-isRequiredCoupon")?(0<t&&armUpdateOrderAmount(l,r,t,n),""!=r&&(armAnimateCounter(l.find(".arm_discount_amount_text")),armAnimateCounter(l.find(".arm_payable_amount_text")),""!=n&&"0"!=n&&armAnimateCounter(l.find(".arm_plan_amount_text")))):(armUpdateOrderAmount(l,r,t,n),""!=r&&"0"!=r&&(armAnimateCounter(l.find(".arm_discount_amount_text")),armAnimateCounter(l.find(".arm_payable_amount_text")),""!=n&&"0"!=n&&armAnimateCounter(l.find(".arm_plan_amount_text")))),d.find(".arm_apply_coupon_btn").removeClass("active"),d.find(".arm_apply_coupon_btn").attr("disabled",!1)})):(d.find(".arm_apply_coupon_btn").attr("disabled",!1),d.find("div.arm-df__fc--validation").html('<div class="arm-df__fc--validation__wrap" style="display:block;"><div class="arm_error_box_arrow"></div>'+s+"</div>").show(),"skin5"==n?armUpdateOrderAmount1(i,l,"",""):armUpdateOrderAmount(l,"","",""))}),jQuery(document).on("click",'.arm-df__checkbox input[type="checkbox"]',function(){if(void 0!==jQuery(this).attr("readonly"))return!1;var a=jQuery(this).attr("id");1==jQuery(this).is(":checked")?jQuery("."+a+"_default_val").val(jQuery(this).val()):jQuery("."+a+"_default_val").val("")}),jQuery(document).on("click",'.arm-df__radio input[type="radio"]',function(){if(void 0!==jQuery(this).attr("readonly"))return!1;var a=jQuery(this).attr("data-id");1==jQuery(this).is(":checked")?jQuery("#"+a+"_default_val").val(jQuery(this).val()):jQuery("#"+a+"_default_val").val("")}),jQuery(document).on("click",".arm-df__dropdown-control, .arm_multiple_selectbox",function(){if(void 0!==jQuery(this).parents(".arm-df__form-field-wrap").find('input[type="text"]').attr("readonly"))return!1;jQuery(this).find("dd ul").toggle()}),jQuery(document).on("click",".arm-df__dropdown-control dt, .arm_multiple_selectbox dt",function(a){if(void 0!==jQuery(this).parents(".arm-df__form-field-wrap").find('input[type="text"]').attr("readonly"))return!1;var e=jQuery(this);0==e.parent().find("dd ul").is(":visible")?(jQuery("dd ul").not(this).hide(),e.find("span").hide(),e.find("input").show(),e.find("input").focus(),e.parent(".arm-df__dropdown-control").addClass("arm-is-active")):(e.parent().find("dd ul").show(),e.parent(".arm-df__dropdown-control").removeClass("arm-is-active")),e.parent().find("dd ul li:not(.field_inactive)").show()}),jQuery(document).on("keyup",".arm-df__dropdown-control dt input, .arm_multiple_selectbox dt input",function(a){if(a.stopPropagation(),void 0!==jQuery(this).parents(".arm-df__form-field-wrap").find('input[type="text"]').attr("readonly"))return!1;var e,a=a.keyCode;-1===jQuery.inArray(a,[16,17,18,19,20,33,34,35,36,37,38,39,40,45,91,92,112,113,114,115,116,117,118,119,120,121,122,123,144,145])&&(jQuery(this).parent().parent().find("dd ul").scrollTop(),e=(e=jQuery(this).val()).toLowerCase(),jQuery(this).parent().parent().find("dd ul").show(),jQuery(this).parent().parent().find("dd ul li:not(.field_inactive)").each(function(a){-1!=jQuery(this).attr("data-label").toLowerCase().indexOf(e)?jQuery(this).show():jQuery(this).hide()}))}),jQuery(document).on("click",".arm-df__dropdown-control dd ul li:not(.field_inactive)",function(a){jQuery(document).find(".arm-df__dropdown-control:active dd ul").hide(),jQuery(this).parents(".arm-df__dropdown-control").removeClass("arm-is-active");jQuery(this).attr("data-value");var e=jQuery(this).attr("data-label"),t=(""!=e&&null!=e||jQuery(this).html(),jQuery(this).attr("data-type"),jQuery(this).parents(".arm-df__dropdown-control")),e=""!=(e=jQuery(this).attr("data-label"))&&null!=e?e:jQuery(this).html(),r=jQuery(this).attr("data-value"),n=jQuery(this).attr("data-type"),_=t.find("dd ul"),i=_.attr("data-id"),d=jQuery("input#"+i).val();t.find("dt span").html(e).show(),t.find("dt input").val(e).hide(),jQuery("input#"+i).val(r),setTimeout(function(){jQuery("input#"+i).attr("value",r),jQuery("input#"+i+"_default_val").attr("value",r)},500),document.getElementById(i).setAttribute("value",r),document.getElementById(i+"_default_val")&&document.getElementById(i+"_default_val").setAttribute("value",r),jQuery("input#"+i).attr("data-type",n),d!=r&&(jQuery("input#"+i).attr("data-old_value",d),jQuery("input#"+i).trigger("change")),(jQuery(this).parents("form").hasClass("arm--material-outline-style")||jQuery(this).parents("form").hasClass("arm-material-style"))&&(""!=r&&0!=r?(jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").addClass("arm-df__form-material-field-wrap"),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find("label").addClass("active"),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").html(e),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").show()):(jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").removeClass("arm-df__form-material-field-wrap"),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find("label").removeClass("active"),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").html(""),jQuery(this).parents(".arm-df__form-field-wrap_select,.arm-df__form-field-wrap_roles").find(".arm__dc--head__title").hide())),_.find("li:not(.field_inactive)").show()}),jQuery(document).on("click",function(a){var a=jQuery(a.target),e=(a.parents().hasClass("arm-df__dropdown-control")||(jQuery(".arm-df__dropdown-control").removeClass("arm-is-active"),jQuery(".arm-df__dropdown-control dd ul").hide(),jQuery(".arm-df__dropdown-control dt span").show(),jQuery(".arm-df__dropdown-control dt input").hide(),jQuery(".arm_autocomplete").each(function(){""==jQuery(this).val()&&jQuery(this).val(jQuery(this).parent().find("span").html())})),a.hasClass("arm__dc--item")&&(a.parents(".arm-df__dropdown-control").removeClass("arm-is-active"),a.parents(".arm-df__dropdown-control dd ul").hide(),a.parents(".arm-df__dropdown-control dt span").show(),a.parents(".arm-df__dropdown-control dt input").hide(),a.parents(".arm_autocomplete").each(function(){""==jQuery(this).val()&&jQuery(this).val(jQuery(this).parent().find("span").html())})),jQuery(".arm_multiple_selectbox"));e.is(a)||0!==e.has(a).length||(jQuery(".arm_multiple_selectbox dd ul").hide(),jQuery(".arm_multiple_selectbox dt span").show(),jQuery(".arm_multiple_selectbox dt input").hide(),jQuery(".arm_autocomplete").each(function(){""==jQuery(this).val()&&jQuery(this).val(jQuery(this).parent().find("span").html())}))});