<?php
/*
Template Name: Membership Page
Description: Custom membership page template for ARMember integration
*/

get_header(); ?>

<style>
/* Custom Membership Page Styles */
.membership-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.membership-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.membership-hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 2;
}

.membership-hero p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

.membership-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.membership-plans {
    padding: 80px 0;
    background: #f8f9fa;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.plan-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.plan-card.featured {
    border: 3px solid #FFD700;
    transform: scale(1.05);
}

.plan-card.featured::before {
    content: 'MOST POPULAR';
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    padding: 8px 40px;
    font-size: 12px;
    font-weight: bold;
    transform: rotate(45deg);
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.plan-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.plan-price {
    font-size: 3rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 5px;
}

.plan-period {
    color: #666;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.plan-features li {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    color: #555;
    position: relative;
    padding-left: 30px;
}

.plan-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
    font-size: 1.2rem;
}

.plan-features li.unavailable {
    color: #ccc;
    text-decoration: line-through;
}

.plan-features li.unavailable:before {
    content: '✗';
    color: #dc3545;
}

.plan-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-top: 20px;
}

.plan-button:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.plan-button.featured {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
}

.plan-button.featured:hover {
    background: linear-gradient(135deg, #FFC700, #FF9500);
    color: #000;
}

.features-section {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.feature-item {
    text-align: center;
    padding: 30px 20px;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    background: white;
    color: #667eea;
    padding: 18px 40px;
    border: none;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: #667eea;
    text-decoration: none;
}

@media (max-width: 768px) {
    .membership-hero h1 {
        font-size: 2.5rem;
    }
    
    .membership-hero p {
        font-size: 1.1rem;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .plan-card.featured {
        transform: none;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}
</style>

<div class="membership-hero">
    <div class="membership-container">
        <h1>🎬 Join DeshiFlix Premium</h1>
        <p>Unlock unlimited movies, TV shows, and exclusive content with our premium membership plans</p>
    </div>
</div>

<div class="membership-plans">
    <div class="membership-container">
        <h2 style="text-align: center; font-size: 2.5rem; color: #333; margin-bottom: 20px;">Choose Your Plan</h2>
        <p style="text-align: center; color: #666; font-size: 1.2rem;">Select the perfect plan for your entertainment needs</p>
        
        <div class="plans-grid">
            <!-- Free Plan -->
            <div class="plan-card">
                <h3 class="plan-name">Free</h3>
                <div class="plan-price">$0</div>
                <div class="plan-period">Forever</div>
                <ul class="plan-features">
                    <li>Limited movie access</li>
                    <li>Standard quality streaming</li>
                    <li>Ads supported</li>
                    <li>10-second download delays</li>
                    <li class="unavailable">No premium content</li>
                    <li class="unavailable">No direct downloads</li>
                    <li class="unavailable">No ad-free experience</li>
                </ul>
                <a href="#" class="plan-button">Current Plan</a>
            </div>
            
            <!-- Premium Monthly Plan -->
            <div class="plan-card featured">
                <h3 class="plan-name">Premium Monthly</h3>
                <div class="plan-price">$9.99</div>
                <div class="plan-period">per month</div>
                <ul class="plan-features">
                    <li>Unlimited movie access</li>
                    <li>4K Ultra HD streaming</li>
                    <li>Ad-free experience</li>
                    <li>Instant direct downloads</li>
                    <li>Premium exclusive content</li>
                    <li>Early access to new releases</li>
                    <li>Priority customer support</li>
                </ul>
                <a href="<?php echo home_url('/register'); ?>" class="plan-button featured">Get Started</a>
            </div>
            
            <!-- Premium Yearly Plan -->
            <div class="plan-card">
                <h3 class="plan-name">Premium Yearly</h3>
                <div class="plan-price">$99.99</div>
                <div class="plan-period">per year <span style="color: #28a745; font-size: 0.9rem;">(Save 17%)</span></div>
                <ul class="plan-features">
                    <li>Everything in Monthly plan</li>
                    <li>2 months free</li>
                    <li>Download for offline viewing</li>
                    <li>Multiple device streaming</li>
                    <li>Family sharing (up to 5 users)</li>
                    <li>Exclusive member events</li>
                    <li>VIP customer support</li>
                </ul>
                <a href="<?php echo home_url('/register'); ?>" class="plan-button">Best Value</a>
            </div>
        </div>
    </div>
</div>

<div class="features-section">
    <div class="membership-container">
        <h2 style="text-align: center; font-size: 2.5rem; color: #333; margin-bottom: 20px;">Why Choose Premium?</h2>
        <p style="text-align: center; color: #666; font-size: 1.2rem;">Experience the best of DeshiFlix with these exclusive features</p>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">🚀</div>
                <h3 class="feature-title">Instant Downloads</h3>
                <p class="feature-description">Skip the wait! Get direct download links without any delays or redirects.</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🛡️</div>
                <h3 class="feature-title">Ad-Free Experience</h3>
                <p class="feature-description">Enjoy uninterrupted streaming and browsing without any advertisements.</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">👑</div>
                <h3 class="feature-title">Premium Content</h3>
                <p class="feature-description">Access exclusive movies, shows, and content available only to premium members.</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">Multi-Device Access</h3>
                <p class="feature-description">Stream on any device - phone, tablet, laptop, or smart TV.</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">4K Ultra HD</h3>
                <p class="feature-description">Watch your favorite content in stunning 4K resolution with crystal clear quality.</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">Early Access</h3>
                <p class="feature-description">Be the first to watch new releases before they're available to free users.</p>
            </div>
        </div>
    </div>
</div>

<div class="cta-section">
    <div class="membership-container">
        <h2 class="cta-title">Ready to Upgrade?</h2>
        <p class="cta-description">Join thousands of satisfied premium members and transform your entertainment experience today!</p>
        <a href="<?php echo home_url('/register'); ?>" class="cta-button">Start Your Premium Journey</a>
    </div>
</div>

<?php get_footer(); ?>
