function armToast(e,a,r,t){""!=t&&void 0!==t||(t=!1),""!=r&&void 0!==r||(r=2500);var n="arm_error_message",n=("success"==a?n="arm_success_message":"error"!=a&&"info"!=a||(n="arm_error_message"),'<div class="arm_toast arm_message '+n+'" id="'+n+'"><div class="arm_message_text">'+e+"</div></div>");0<jQuery(".arm_toast_container .arm_toast").length&&jQuery(".arm_toast_container .arm_toast").remove(),jQuery(n).appendTo(".arm_toast_container").show("slow").addClass("arm_toast_open").delay(r).queue(function(){var e;"error"!=a&&"buddypress_error"!=a?(e=jQuery(this),jQuery(".arm_already_clicked").removeClass("arm_already_clicked").removeAttr("disabled"),e.addClass("arm_toast_close"),!0===t&&location.reload()):(e=jQuery(this)).addClass("arm_toast_close"),setTimeout(function(){e.remove()},1e3)})}function armCopyToClipboardForm(e){var a=document.getElementsByClassName("armCopyText"),e=(jQuery(a).removeAttr("readonly"),jQuery(a).val(e),jQuery(a).select(),!1);try{e=document.execCommand("copy")}catch(e){}return jQuery(a).attr("readonly","readonly"),e}function armCopyToClipboard(e){var a=document.createElement("textarea"),e=(a.id="armCopyTextarea",a.style.position="fixed",a.style.top=0,a.style.left=0,a.style.width="2em",a.style.height="2em",a.style.padding=0,a.style.border="none",a.style.outline="none",a.style.boxShadow="none",a.style.background="transparent",a.value=e,document.body.appendChild(a),a.select(),document.getElementById("armCopyTextarea").select(),!1);try{e=document.execCommand("copy")}catch(e){}return document.body.removeChild(a),e}function arm_reset_form_popup(e){0<jQuery("."+e).find("form").length&&(e=jQuery("."+e).find("form").attr("id"),jQuery("#"+e)[0].reset(),jQuery("#"+e).find(".arm_invalid").removeClass("arm_invalid"),jQuery("#"+e).find(".arm-df__fc--validation").html(""))}function arm_adjust_form_popup(){jQuery(".arm_popup_member_form").each(function(){var e=jQuery(this).attr("data-width"),a=jQuery(window).height(),r=jQuery(window).width(),a=(r<e?(jQuery(this).css({top:"0"}),jQuery(this).addClass("popup_wrapper_responsive"),jQuery(this).find(".popup_content_text").css({height:a-65+"px"})):(a=jQuery(this).height()>a?jQuery(window).scrollTop()+50:jQuery(window).scrollTop()+(a-jQuery(this).height())/2,jQuery(this).css({top:a+"px"}),jQuery(this).removeClass("popup_wrapper_responsive"),a=jQuery(this).find(".popup_content_text").attr("data-height"),jQuery(this).find(".popup_content_text").css({height:a})),(r-e)/2);jQuery(this).css({left:a+"px"})}),jQuery(".arm_popup_member_setup_form").each(function(){var e=jQuery(this).attr("data-width"),a=jQuery(window).height(),r=jQuery(window).width(),a=(r<e?(jQuery(this).css({top:"0px"}),jQuery(this).addClass("popup_wrapper_responsive"),jQuery(this).find(".popup_content_text").css({height:a-65+"px"})):(a=jQuery(this).height()>a?jQuery(window).scrollTop()+50:jQuery(window).scrollTop()+(a-jQuery(this).height())/2,jQuery(this).css({top:a+"px"}),jQuery(this).removeClass("popup_wrapper_responsive"),a=jQuery(this).find(".popup_content_text").attr("data-height"),jQuery(this).find(".popup_content_text").css({height:a})),(r-e)/2);jQuery(this).css({left:a+"px"})})}function armSetupHideShowSections(e){var a,r,t,n,_,i=jQuery(e).find('[data-id="arm_front_gateway_skin_type"]').val(),o=jQuery(e).find('[data-id="arm_front_plan_skin_type"]').val(),m=(r="skin5"==o?(a=(_=jQuery(e).find(".arm_module_plan_input")).val(),(a=_.parent("dl").find('li.arm__dc--item[data-value="'+a+'"]')).attr("data-value")):(a=jQuery(e).find("input.arm_module_plan_input:checked")).val(),a.attr("data-type")),s=a.attr("data-cycle"),u=jQuery(e).find('[data-id="arm_user_selected_payment_mode_'+r+'"]').val(),c=jQuery(e).find('[data-id="arm_user_old_plan"]').val(),l=(c=null!=c&&c.search(",")?c.split(","):[],jQuery(e).find('[data-id="arm_user_old_plan_total_cycle_'+r+'"]').val()),d=jQuery(e).find('[data-id="arm_user_last_payment_status_'+r+'"]').val(),p=jQuery(e).find('[data-id="arm_user_done_payment_'+r+'"]').val(),y=jQuery(e).find('[data-id="arm_user_selected_payment_cycle_'+r+'"]').val(),i=("radio"==i?(n=jQuery(e).find(".arm_module_gateway_input:checked"),t=jQuery(e).find(".arm_module_gateway_input:checked").val()):(t=(_=jQuery(e).find(".arm_module_gateway_input")).val(),n=_.parent("dl").find('li.arm__dc--item[data-value="'+t+'"]')),n.attr("data-payment_mode"));jQuery(e).find(".arm_module_plans_ul").find(".arm_setup_column_item").removeClass("arm_active"),jQuery(a).parents(".arm_setup_column_item").addClass("arm_active"),jQuery(e).find('input[name="arm_plan_type"]').val(m).trigger("change"),"free"==m?(jQuery(e).find(".arm_setup_gatewaybox_wrapper").hide("slow"),jQuery(e).find(".arm_payment_mode_wrapper").hide("slow"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")):("recurring"!=m||-1==jQuery.inArray(r,c)||l==p&&"infinite"!=l||"manual_subscription"!=u?("recurring"==m?"failed"==d?(jQuery(e).find(".arm_payment_mode_wrapper").hide(),jQuery(e).find(".arm_setup_couponbox_wrapper").hide("slow"),("skin5"==o?jQuery(e).find('input[name="payment_cycle_'+r+'"][value="'+y+'"]'):jQuery(e).find('input:radio[name="payment_cycle_'+r+'"]').filter('[value="'+y+'"]').prop("checked",!0)).trigger("change"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")):(jQuery(e).find(".arm_module_payment_cycle_container").not(".arm_payment_cycle_box_"+r).slideUp("slow").addClass("arm_hide"),jQuery(e).find(".arm_payment_cycle_box_"+r).slideDown("slow").removeClass("arm_hide"),1<s?(jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideDown("slow").removeClass("arm_hide"),jQuery(e).find(".arm_setup_payment_cycle_title_wrapper").slideDown("slow").removeClass("arm_hide")):(jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide"),jQuery(e).find(".arm_setup_payment_cycle_title_wrapper").slideUp("slow").addClass("arm_hide")),_=jQuery(e).find('[data-id="arm_payment_cycle_plan_'+r+'"]').val(),("skin5"==o?jQuery(e).find('input[name="payment_cycle_'+r+'"][value="'+y+'"]'):jQuery(e).find(".arm_payment_cycle_box_"+r).find(".arm_module_cycle_input:radio").filter('[value="'+_+'"]')).trigger("change")):jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide"),"both"==i&&"recurring"==m?(jQuery(e).find('input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0).trigger("change"),jQuery(e).find(".arm_payment_mode_wrapper").show()):(jQuery(e).find('input:radio[name="arm_selected_payment_mode"]').filter('[value="'+i+'"]').prop("checked",!0).trigger("change"),jQuery(e).find(".arm_payment_mode_wrapper").hide()),jQuery(e).find(".arm_setup_couponbox_wrapper").show("slow")):(jQuery(e).find(".arm_payment_mode_wrapper").hide(),jQuery(e).find(".arm_setup_couponbox_wrapper").hide("slow"),("skin5"==o?jQuery(e).find('input[name="payment_cycle_'+r+'"][value="'+y+'"]'):jQuery(e).find('input:radio[name="payment_cycle_'+r+'"]').filter('[value="'+y+'"]').prop("checked",!0)).trigger("change"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")),jQuery(e).find(".arm_setup_gatewaybox_wrapper").show("slow")),armUpdateOrderAmount(e,0),armResetCouponCode(e)}function armSetupHideShowSections1(e,a){var r,t,n=jQuery(e).find('[data-id="arm_front_gateway_skin_type"]').val(),_=jQuery(e).find('[data-id="arm_front_plan_skin_type"]').val(),i=a.attr("data-value"),o=a.attr("data-cycle"),m=jQuery(e).find('[data-id="arm_user_selected_payment_mode_'+i+'"]').val(),s=jQuery(e).find('[data-id="arm_user_old_plan"]').val(),u=(s=null!=s&&s.search(",")?s.split(","):[],a.attr("data-type")),c=jQuery(e).find('[data-id="arm_user_old_plan_total_cycle_'+i+'"]').val(),l=jQuery(e).find('[data-id="arm_user_done_payment_'+i+'"]').val(),d=jQuery(e).find('[data-id="arm_user_selected_payment_cycle_'+i+'"]').val(),p=jQuery(e).find('[data-id="arm_user_last_payment_status_'+i+'"]').val(),y=("radio"==n?(r=jQuery(e).find(".arm_module_gateway_input:checked"),t=jQuery(e).find(".arm_module_gateway_input:checked").val()):(t=(y=jQuery(e).find(".arm_module_gateway_input")).val(),r=y.parent("dl").find('li.arm__dc--item[data-value="'+t+'"]')),r.attr("data-payment_mode"));jQuery(e).find('input[name="arm_plan_type"]').val(u).trigger("change"),"free"==u?(jQuery(e).find(".arm_setup_gatewaybox_wrapper").hide("slow"),jQuery(e).find(".arm_payment_mode_wrapper").hide("slow"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")):("recurring"!=u||-1==jQuery.inArray(i,s)||c==l&&"infinite"!=c||"manual_subscription"!=m?("recurring"==u?"failed"==p?(jQuery(e).find(".arm_payment_mode_wrapper").hide(),jQuery(e).find(".arm_setup_couponbox_wrapper").hide("slow"),("skin5"==_?jQuery(e).find('input[name="payment_cycle_'+i+'"][value="'+d+'"]'):jQuery(e).find('input:radio[name="payment_cycle_'+i+'"]').filter('[value="'+d+'"]').prop("checked",!0)).trigger("change"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")):(jQuery(e).find(".arm_module_payment_cycle_container").not(".arm_payment_cycle_box_"+i).slideUp("slow").addClass("arm_hide"),jQuery(e).find(".arm_payment_cycle_box_"+i).slideDown("slow").removeClass("arm_hide"),1<o?(jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideDown("slow").removeClass("arm_hide"),jQuery(e).find(".arm_setup_payment_cycle_title_wrapper").slideDown("slow").removeClass("arm_hide")):(jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide"),jQuery(e).find(".arm_setup_payment_cycle_title_wrapper").slideUp("slow").addClass("arm_hide")),t=jQuery(e).find('[data-id="arm_payment_cycle_plan_'+i+'"]').val(),("skin5"==_?jQuery(e).find(".arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+i+' input[name="payment_cycle_'+i+'"]'):jQuery(e).find(".arm_payment_cycle_box_"+i).find(".arm_module_cycle_input:radio:[value="+t+"]")).trigger("change")):jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide"),"both"==y&&"recurring"==u?(jQuery(e).find('input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0).trigger("change"),jQuery(e).find(".arm_payment_mode_wrapper").show()):(jQuery(e).find('input:radio[name="arm_selected_payment_mode"]').filter('[value="'+y+'"]').prop("checked",!0).trigger("change"),jQuery(e).find(".arm_payment_mode_wrapper").hide()),jQuery(e).find(".arm_setup_couponbox_wrapper").show("slow")):(jQuery(e).find(".arm_payment_mode_wrapper").hide(),jQuery(e).find(".arm_setup_couponbox_wrapper").hide("slow"),"skin5"==_?(jQuery(e).find(".arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+i+' input[name="payment_cycle_'+i+'"]').attr("data-old_value","0"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+i+' input[name="payment_cycle_'+i+'"]').trigger("change")):jQuery(e).find('input:radio[name="payment_cycle_'+i+'"]').filter('[value="'+d+'"]').prop("checked",!0).trigger("change"),jQuery(e).find(".arm_setup_paymentcyclebox_wrapper").slideUp("slow").addClass("arm_hide")),jQuery(e).find(".arm_setup_gatewaybox_wrapper").show("slow")),armUpdateOrderAmount1(a,e,0),"radio"==n&&(jQuery(e).find(".arm_module_gateway_input:radio:first").val(),jQuery(e).find(".arm_module_gateway_input:radio:first").trigger("change")),armResetCouponCode(e)}function armUpdateOrderAmount(e,a,r,t){var n,_=jQuery(e).find('[data-id="arm_front_plan_skin_type"]').val(),i=(n="skin5"==_?(n=e.attr("id"),i=(n=jQuery("#"+n+" .arm_module_plan_input")).val(),n.parent("dl").find('li.arm__dc--item[data-value="'+i+'"]')):jQuery(e).find("input.arm_module_plan_input:checked"),0);0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&(i=1);g="undefined"==(g=jQuery(e).find(".arm_global_currency").val())?"":g;var o,m,s,u=jQuery(e).find(".arm_global_currency_sym").val(),c=(u=""==(u="undefined"==u?"":u)?g:u,jQuery(e).find(".arm_global_currency_decimal").val()),u=(""==c&&(c=2),jQuery(e).find(".arm_order_currency").text(u),n.attr("data-plan_name")),l=n.attr("data-cycle"),d=(o="skin5"!=_?n.val():n.attr("data-value"),plan_amt_main=n.attr("data-amt")),p=jQuery(".arm_pro_rata_feature_"+o).val(),y=(void 0===p&&(p=0),jQuery(e).find('[data-id="arm_front_gateway_skin_type"]').val()),f=(1==p&&(j=n.attr("data-type"),Q=jQuery(".arm_prorata_supported_gateway").val(),f=("radio"==y?jQuery(e).find(".arm_module_gateway_input:checked"):jQuery(e).find(".arm_module_gateway_input")).val(),h=jQuery(e).find('input:radio[name="arm_selected_payment_mode"]:checked').val(),Q.includes(f)||"auto_debit_subscription"!=h||"recurring"!=j||(p=0)),"undefined"!=typeof __ARMGM&&0<jQuery(e).find("#gm_sub_user_select_"+o).length&&(v=(v=d).replaceAll(",",""),Q=jQuery(e).find("#gm_sub_user_select_"+o).attr("data-default-val"),d=v*jQuery(e).find("#gm_sub_user_select_"+o).val()/Q,v=parseFloat(v).toFixed(c)),jQuery(e).find(".arm_plan_name_text").text(u),1==i?(s=arm_get_amount_currency_wise_separator(d),jQuery(e).find(".arm_plan_amount_text").text(s)):("undefined"!=typeof __ARMGM&&0<jQuery(e).find("#gm_sub_user_select_"+o).length&&(d=d.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")),0<jQuery(e).find("#gm_sub_user_select_"+o).length?jQuery(e).find(".arm_plan_amount_text").text(v):jQuery(e).find(".arm_plan_amount_text").text(d),0==p&&jQuery(e).find(".arm_payable_amount_text").text(d)),n.attr("data-is_trial")),h=jQuery(e).find('[data-id="arm_user_old_plan"]').val(),j=(0!=h&&""!=h&&1!=p&&(f="0"),n.attr("data-trial_amt")),Q=(void 0!==j&&""!=j||(j="0.00"),null!=f&&"1"==f&&1!=p&&(d=j=n.attr("data-trial_amt")),1==i?(s=arm_get_amount_currency_wise_separator(j),jQuery(e).find(".arm_trial_amount_text").text(s)):jQuery(e).find(".arm_trial_amount_text").text(j),""!=r&&null!=r||(r=d),null!=(r=0!=a&&null!=a?r:d)&&("string"==typeof r&&(r=r.replaceAll(",","")),d=d.toString().replaceAll(",",""),plan_amt_main=plan_amt_main.toString().replaceAll(",","")),a=0==a||""==a||null==a?jQuery('[data-id="arm_zero_amount_discount"]').val():a,""),g=("subscription"==n.attr("data-recurring")?(Q=jQuery(e).find("[name=arm_selected_payment_mode]:checked").val(),1<l?(u=jQuery(e).find("input[name='payment_cycle_"+o+"']:checked").parents(".arm_module_payment_cycle_option").find(".arm_module_payment_cycle_span").html(),jQuery(e).find(".arm_plan_cycle_name_text").text(u)):jQuery(e).find(".arm_plan_cycle_name_text").text("")):jQuery(e).find(".arm_plan_cycle_name_text").text(""),(r<=0||"1"==f&&r<=0)&&"auto_debit_subscription"!=Q&&"radio"==y&&jQuery(e).find(".arm_module_gateway_fields").slideUp("slow").addClass("arm_hide"),jQuery(".arm_global_currency").val()),v=(armManageTax(e,r=r<=0?0:r),jQuery(".arm_tax_include_exclude_flag").val()),h=n.attr("data-tax"),j=(total_tax=void 0===v||0==v?parseFloat(h)*parseFloat(r)/100:0,total_tax=!(null==g&&""==g||"JPY"!=g&&"HUF"!=g&&"TWD"!=g)?total_tax.toFixed(0):total_tax.toFixed(c),null!=r&&0==p&&(r=(r=(r=parseFloat(r)+parseFloat(total_tax)).toFixed(c)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")),jQuery(".arm_current_plan_id").val());void 0!==v&&1==v&&(r=r.replaceAll(",",""),total_tax=parseFloat(h)/100,total_tax+=1,total_tax=(total_tax=r-(total_tax=parseFloat(r)/total_tax)).toFixed(c)),1==p&&void 0===t&&j!=o&&"skin5"!=_&&(m=jQuery('input[data-id="subscription_plan_'+o+'"]').attr("data-pro_rata"),""==(m=(m=parseFloat(m)).toFixed(c))&&(m=(m=0).toFixed(c)),jQuery(".arm_pro_ration_amount_text").text(m),r=jQuery('input[data-id="subscription_plan_'+o+'"]').attr("data-pro_rata_amount"),r=(r=parseFloat(r)).toFixed(c)),0==p&&(m=(m=0).toFixed(c),jQuery(".arm_pro_ration_amount_text").text(m),jQuery(e).find(".arm_payable_amount_text").text(r)),1==i?(s=arm_get_amount_currency_wise_separator(total_tax),jQuery(e).find(".arm_tax_amount_text").text(s),s=arm_get_amount_currency_wise_separator(r),0==p&&jQuery(e).find(".arm_payable_amount_text").text(s),s=arm_get_amount_currency_wise_separator(a),jQuery(e).find(".arm_discount_amount_text").text(s)):(a=parseFloat(a).toFixed(c),jQuery(e).find(".arm_tax_amount_text").text(total_tax),1==p&&"skin5"!=_&&jQuery(e).find(".arm_payable_amount_text").text(r),jQuery(e).find(".arm_discount_amount_text").text(a)),jQuery('input[name="arm_total_payable_amount"]').val(r)}function armUpdateOrderAmount1(e,a,r,t){var n=jQuery(a).find(".arm_global_currency").val(),_=(n="undefined"==n?"":n,jQuery(a).find(".arm_global_currency_sym").val()),i=(_=""==(_="undefined"==_?"":_)?n:_,jQuery(a).find(".arm_global_currency_decimal").val()),_=(""==i&&(i=2),jQuery(a).find('[data-id="arm_front_plan_skin_type"]').val(),jQuery(a).find(".arm_order_currency").text(_),e.attr("data-plan_name")),o=e.attr("data-amt"),m=e.attr("data-cycle_label"),_=(jQuery(a).find(".arm_plan_name_text").text(_),jQuery(a).find(".arm_payable_amount_text").text(o),e.attr("data-value")),s=jQuery(".arm_pro_rata_feature_"+_).val(),u=(void 0===s&&(s=0),jQuery(a).find('[data-id="arm_front_gateway_skin_type"]').val()),c=(1==s&&(d=e.attr("data-type"),p=jQuery(".arm_prorata_supported_gateway").val(),c=("radio"==u?jQuery(a).find(".arm_module_gateway_input:checked"):jQuery(a).find(".arm_module_gateway_input")).val(),l=jQuery(a).find('input:radio[name="arm_selected_payment_mode"]:checked').val(),p.includes(c)||"auto_debit_subscription"!=l||"recurring"!=d||(h=s=0,h=parseFloat(h).toFixed(i),jQuery(".arm_pro_ration_amount_text").text(h))),"undefined"!=typeof __ARMGM&&0<jQuery(a).find("#gm_sub_user_select_"+_).length&&(f=(f=o).replaceAll(",",""),p=jQuery(a).find("#gm_sub_user_select_"+_).attr("data-default-val"),o=f*jQuery(a).find("#gm_sub_user_select_"+_).val()/p,f=parseFloat(f).toFixed(i)),0),l=(1==(c=0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()?1:c)?(j=arm_get_amount_currency_wise_separator(y),jQuery(a).find(".arm_plan_amount_text").text(j),o=parseFloat(o).toFixed(i)):(jQuery(a).find(".arm_plan_amount_text").text(o),"undefined"!=typeof __ARMGM&&0<jQuery(a).find("#gm_sub_user_select_"+_).length&&(o=o.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),0<jQuery(a).find("#gm_sub_user_select_"+_).length&&(f=parseFloat(f).toFixed(i),o=parseFloat(o).toFixed(i),jQuery(a).find(".arm_plan_amount_text").text(f),0==s&&jQuery(a).find(".arm_payable_amount_text").text(o)))),e.attr("data-is_trial")),d=e.attr("data-trial_amt"),p=(void 0!==d&&""!=d||(d="0.00"),jQuery(a).find('[data-id="arm_user_old_plan"]').val()),y=(null!=(l=0!=p&&""!=p?"0":l)&&"1"==l?o=d=e.attr("data-trial_amt"):d="0.00",1==c?(j=arm_get_amount_currency_wise_separator(d),jQuery(a).find(".arm_trial_amount_text").text(j)):jQuery(a).find(".arm_trial_amount_text").text(d),""!=t&&null!=t||(t=o),null!=(t=0!=r&&null!=r?t:o)&&"string"==typeof t&&(t=t.replaceAll(",","")),0==r||""==r||null==r?r:""),f=(r=0==r||""==r||null==r?jQuery('[data-id="arm_zero_amount_discount"]').val():r,""),p=("subscription"==e.attr("data-recurring")?(f=jQuery(a).find("[name=arm_selected_payment_mode]:checked").val(),jQuery(a).find(".arm_plan_cycle_name_text").text(m)):jQuery(a).find(".arm_plan_cycle_name_text").text(""),t<=0&&"auto_debit_subscription"!=f&&"radio"==u&&jQuery(a).find(".arm_module_gateway_fields").hide("slow"),armManageTax(a,t),jQuery(".arm_tax_include_exclude_flag").val()),l=e.attr("data-tax");total_tax=void 0===p||0==p?parseFloat(l)*parseFloat(t)/100:0,total_tax=!(null==n&&""==n||"JPY"!=n&&"HUF"!=n&&"TWD"!=n)?total_tax.toFixed(0):total_tax.toFixed(i),total_tax=parseFloat(total_tax);total_tax=!(null==(n=jQuery(".arm_global_currency").val())&&""==n||"JPY"!=n&&"HUF"!=n&&"TWD"!=n)?total_tax.toFixed(0):total_tax.toFixed(i);var h,j,d=jQuery(".arm_current_plan_id").val();1!=s||void 0!==y&&0!=y||d==_||(h=jQuery(".arm_plan_option_check_"+_).attr("data-pro_rata"),""==(h=(h=parseFloat(h)).toFixed(i))&&(h=(h=0).toFixed(i)),jQuery(".arm_pro_ration_amount_text").text(h),t=jQuery(".arm_plan_option_check_"+_).attr("data-pro_rata_amount"),t=(t=parseFloat(t)).toFixed(i),jQuery(a).find(".arm_payable_amount_text").text(t)),null!=t&&(t=(t=(t=parseFloat(t)+parseFloat(total_tax)).toFixed(i)).toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")),void 0!==p&&1==p&&(t=t.replaceAll(",",""),total_tax=parseFloat(l)/100,total_tax+=1,total_tax=(total_tax=t-(total_tax=parseFloat(t)/total_tax)).toFixed(i)),1==c?(j=arm_get_amount_currency_wise_separator(total_tax),jQuery(a).find(".arm_tax_amount_text").text(j)):jQuery(a).find(".arm_tax_amount_text").text(total_tax),null!=y&&(1==c?(j=arm_get_amount_currency_wise_separator(r),jQuery(a).find(".arm_discount_amount_text").text(j)):jQuery(a).find(".arm_discount_amount_text").text(r)),1==c?(j=arm_get_amount_currency_wise_separator(total_tax),jQuery(a).find(".arm_tax_amount_text").text(j),j=arm_get_amount_currency_wise_separator(t),0==s&&jQuery(a).find(".arm_payable_amount_text").text(j),j=arm_get_amount_currency_wise_separator(r),jQuery(a).find(".arm_discount_amount_text").text(j)):(r=parseFloat(r).toFixed(i),jQuery(a).find(".arm_tax_amount_text").text(total_tax),1==s&&jQuery(a).find(".arm_payable_amount_text").text(t),jQuery(a).find(".arm_discount_amount_text").text(r)),jQuery(a).find('[data-id="arm_total_payable_amount"]').val(t)}function armManageTax(e,a){var r,t,n,_,i,o=jQuery(e).find('[data-id="arm_front_plan_skin_type"]').val(),m="0.00",s=jQuery(e).find(jQuery("input[type='hidden'][name='arm_tax_type']")).val(),u=jQuery(".arm_global_currency_decimal").val(),c=(""==u&&(u=2),0);0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&(c=1),void 0!==s&&""!=s&&("country_tax"==s?(s=jQuery(e).find(jQuery("input[type='hidden'][name='arm_country_tax_field']")).val(),t=jQuery(e).find(jQuery("input[type='hidden'][name='arm_country_tax_field_opts']")).val(),n=jQuery(e).find(jQuery("input[type='hidden'][name='arm_country_tax_amount']")).val(),r=jQuery(e).find(jQuery("input[type='hidden'][name='arm_country_tax_default_val']")).val(),m=parseFloat(r),void 0!==s&&""!=s&&(0<jQuery(e).find(jQuery("input[type='text'][name='"+s+"']")).length?""!=jQuery(e).find(jQuery("input[type='text'][name='"+s+"']")).val()?(i=jQuery(e).find(jQuery("input[type='text'][name='"+s+"']")).val(),t=jQuery.parseJSON(t),n=jQuery.parseJSON(n),-1!=(_=jQuery.inArray(i,t))&&(m=parseFloat(n[_]).toFixed(u))):m=parseFloat(r).toFixed(u):0<jQuery(e).find(jQuery("input[type='radio'][name='"+s+"']")).length&&(""!=jQuery(e).find(jQuery("input[type='radio'][name='"+s+"']:checked")).val()?(i=jQuery(e).find(jQuery("input[type='radio'][name='"+s+"']:checked")).val(),t=jQuery.parseJSON(t),n=jQuery.parseJSON(n),-1!=(_=jQuery.inArray(i,t))&&(m=parseFloat(n[_]).toFixed(u))):m=parseFloat(r).toFixed(u)))):(s=jQuery(e).find(jQuery("input[type='hidden'][name='arm_common_tax_amount']")).val(),m=parseFloat(s).toFixed(u))),1==c?(i=arm_get_amount_currency_wise_separator(m),jQuery(e).find(".arm_tax_percentage_text").text(i)):jQuery(e).find(".arm_tax_percentage_text").text(m),jQuery(e).find("[data-tax]").attr("data-tax",m),"skin5"==o&&jQuery(".md-select-menu-container .armMDOption").attr("data-tax",m)}function armAnimateCounter(a){var e=jQuery(a).text(),r=0,t=(1==(r=0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()?1:r)&&(e=arm_get_amount_standard_separator(e)),jQuery(".arm_global_currency_decimal").val()),n=(""==t&&(t=0),e);"string"==typeof e&&(e=e.replace(/,/g,""),e=(e=parseFloat(e)).toFixed(t)),1==r&&(e=arm_get_amount_currency_wise_separator(e)),jQuery(a).prop("Counter",0).animate({Counter:e},{duration:500,easing:"swing",step:function(e){jQuery(a).text(e.toFixed(t))},complete:function(){1==r&&(e=arm_get_amount_currency_wise_separator(e)),jQuery(a).text(e),setTimeout(function(){1==r&&(e=arm_get_amount_currency_wise_separator(n)),jQuery(a).text(e)},1)}})}function arm_tooltip_init(){jQuery.isFunction(jQuery().tipso)&&(jQuery(".armhelptip").each(function(){jQuery(this).tipso({position:"top",size:"small",background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".arm_helptip_icon").each(function(){jQuery(this).tipso({position:"top",size:"small",tooltipHover:!0,background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".arm_helptip_icon_ui").each(function(){jQuery.isFunction(jQuery().tooltip)&&jQuery(this).tooltip({tooltipClass:"arm_helptip_ui_content",position:{my:"center bottom-20",at:"center top",using:function(e,a){jQuery(this).css(e),jQuery("<div>").addClass("arm_arrow").addClass(a.vertical).addClass(a.horizontal).appendTo(this)}},content:function(){return jQuery(this).prop("title")},show:{duration:0},hide:{duration:0}})}),jQuery(".arm_email_helptip_icon").each(function(){jQuery(this).tipso({position:"left",size:"small",tooltipHover:!0,background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".armhelptip_front").each(function(){jQuery(this).tipso({position:"top",size:"small",background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}))}function arm_transaction_init(){jQuery(".arm_transaction_list_header th.arm_sortable_th").each(function(){var a=jQuery(this).parents(".arm_user_transaction_list_table"),e=jQuery(this),r=e.index(),t=!1;e.on("click",function(){a.find("th").removeClass("armAsc").removeClass("armDesc"),a.find("td").filter(function(){return jQuery(this).index()===r}).armSortElements(function(e,a){return jQuery.text([e])>jQuery.text([a])?t?-1:1:t?1:-1},function(){return this.parentNode});var e=t?"armDesc":"armAsc";jQuery(this).addClass(e),t=!t})})}function arm_current_membership_init(){jQuery(".arm_current_membership_list_header th.arm_sortable_th").each(function(){var a=jQuery(this).parents(".arm_user_current_membership_list_table"),e=jQuery(this),r=e.index(),t=!1;e.on("click",function(){a.find("th").removeClass("armAsc").removeClass("armDesc"),a.find("td").filter(function(){return jQuery(this).index()===r}).armSortElements(function(e,a){return jQuery.text([e])>jQuery.text([a])?t?-1:1:t?1:-1},function(){return this.parentNode});var e=t?"armDesc":"armAsc";jQuery(this).addClass(e),t=!t})})}function arm_get_directory_list(a,r){var t,e,n;return void 0!==a&&(t=a.attr("data-temp"),e=a.serialize(),n=a.find('input[name="arm_wp_nonce"]').val(),(1==r.length?r.find("#arm_loader_img_left"):a.find("#arm_loader_img")).css("display","inline-block"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_directory_paging_action&"+e+"&_wpnonce="+n,beforeSend:function(){a.find(".arm_template_container").css("opacity","0.5"),a.find(".arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_search_btn, .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_clear_btn").attr("disabled","disabled")},success:function(e){a.find(".arm_template_container").css("opacity","1"),r.find("#arm_loader_img_left").hide(),a.find("#arm_loader_img").hide(),a.find(".arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_search_btn, .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_clear_btn").removeAttr("disabled","disabled");jQuery(".arm_template_wrapper_"+t).find(".arm_temp_field_pagination").val();return jQuery(".arm_template_wrapper_"+t).find(".arm_user_block, .arm_directory_paging_container").remove(),jQuery(".arm_template_wrapper_"+t).find(".arm_template_container").prepend(e),arm_tooltip_init(),setTimeout(function(){armAdjustDirectoryTemplateBox()},100),!1},error:function(e){a.find(".arm_template_container").css("opacity","1"),a.find("#arm_loader_img").hide(),r.find("#arm_loader_img_left").hide(),a.find(".arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_search_btn, .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_clear_btn").removeAttr("disabled","disabled")}})),!1}function arm_hide_show_section(e,a){""!=a&&"checkbox"==jQuery(e).attr("type")&&(jQuery(e).is(":checked")?jQuery(a).show():jQuery(a).hide())}function arm_form_ajax_action_after_reload_recaptcha(r,t=""){var e=jQuery(".arm_settings_recaptcha_site_key").val();void 0!==window.arm_recaptcha_v3&&"undefined"!=typeof grecaptcha?grecaptcha.ready(function(){grecaptcha.execute(e).then(function(e){for(var a in window.arm_recaptcha_v3)jQuery("#"+a).val(e).trigger("change");"other_form"==t?arm_form_ajax_action(r):"setup_form"==t&&arm_setup_form_ajax_action(r)})}):"other_form"==t?arm_form_ajax_action(r):"setup_form"==t&&arm_setup_form_ajax_action(r)}function arm_form_ajax_action(r){var e=jQuery(r).find("#arm_2fa_wordfence_ls_authenticate").val();if("1"==e&&"function"==typeof arm_handle_wordfence_2fa_login)return arm_handle_wordfence_2fa_login(r),!1;var a,t,n=jQuery(r).attr("data-random-id"),_=jQuery(r).parent(".arm-form-container").find('input[name="arm_captcha_'+n+'"]').length,i=jQuery(r).find('input[name="arm_wp_nonce"]').val(),e=(jQuery(r).find('input[name="arm_filter_input"]').remove(),jQuery(r).prepend('<input type="hidden" name="form_random_key" value="'+n+'" />'),jQuery(r).serialize()),o=(jQuery(r).find('input[name="form_random_key"]').remove(),jQuery(r).find('input[name="arm_action"]').val()),m=window.location.href;t=0<=m.indexOf("key")&&0<=m.indexOf("login")&&0<=m.indexOf("action")?(m=(m=m.split("?")[1].split("&"))[0].match(/^(page_id)+.*$/)?(t=m[1].split("="),a=m[2].split("="),m[3].split("=")):(t=m[0].split("="),a=m[1].split("="),m[2].split("=")),"action=arm_shortcode_form_ajax_action&"+e+"&action2="+t[1]+"&key2="+a[1]+"&login2="+m[1]):"action=arm_shortcode_form_ajax_action&"+e,jQuery(".arm_form_message_container").html(""),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:t,beforeSend:function(){jQuery(r).find("input[type='submit'], button[type='submit']").attr("disabled","disabled").addClass("active")},success:function(e){if(0<_&&("undefined"!=typeof grecaptcha&&grecaptcha.reset(),arm_reload_captcha.call()),"success"==e.status){if(void 0!==e.dis_form&&1==e.dis_form&&1<=jQuery(r).find(".arm_auth_form_field_container").length)return jQuery(r).find(".arm-df__fields-wrapper").slideUp(0),jQuery(r).find(".arm-df__form-group_text, .arm-df__form-group_password, .arm-df__form-group_rememberme, .arm-df__form-group_forgot_link").fadeOut(0),jQuery(r).find("input[name='arm_auth_form_type']").val(1),jQuery(r).find(".arm_auth_form_field_container").fadeIn(500),jQuery(r).find(".arm-df__fields-wrapper").slideDown(500),jQuery(r).find("#arm_auth_label").focus(),arm_manage_form_scroll(r,".arm-form-container",".arm_form_message_container",e.is_action),jQuery(r).parent(".arm-form-container").find(".arm_form_message_container").html(e.message).slideDown().delay(5e3).slideUp(),void jQuery(r).find("input[type='submit'], button[type='submit']").removeAttr("disabled").removeClass("active");"armrp"==e.is_action?(jQuery(r).slideUp("slow"),jQuery(r).parent(".arm-form-container").find(".arm_form_message_container").html(e.message).slideDown("slow"),jQuery(r).parent(".arm-form-container").find(".arm_form_message_container1").hide()):(void 0!==e.script&&""!==e.script&&jQuery("body").append(e.script),"redirect"!=e.type?jQuery(r).parent(".arm-form-container").find(".arm_form_message_container").html(e.message).slideDown().delay(5e3).slideUp():window.location.href=e.message),jQuery(r).hasClass("arm_form_edit_profile")||"function"==typeof armResetFileUploader&&armResetFileUploader(r),jQuery(r).hasClass("arm_form_edit_profile")||"redirect"==e.type||jQuery(r).trigger("reset"),"redirect"!=e.type&&arm_reinit_session_var(r,__ARMAJAXURL,n,i)}else jQuery(r).parent(".arm-form-container").find(".arm_form_message_container").html(e.message).slideDown("slow").delay(5e3).slideUp(),jQuery(r).parent(".arm-form-container").find(".arm_form_message_container i.armfa-times").on("click",function(){jQuery(r).parent(".arm-form-container").find(".arm_form_message_container .arm_error_msg").delay(100).fadeOut(2e3)}),arm_manage_form_scroll(r,".arm-form-container",".arm_form_message_container",e.is_action),arm_reinit_session_var(r,__ARMAJAXURL,n,i);var a;-1!=o.indexOf("change-password")?1==e.current_pass_error?0<=(a=jQuery(r).find("input[name='current_user_pass']")[0].getBoundingClientRect()).top&&a.bottom<=window.innerHeight?setTimeout(function(){jQuery(r).find("input[name='current_user_pass']").focus()},200):(arm_manage_form_scroll(r,".arm-form-container",".arm_form_message_container",e.is_action),setTimeout(function(){jQuery(r).find("input[name='current_user_pass']").focus()},500)):arm_manage_form_scroll(r,".arm-form-container",".arm_form_message_container",e.is_action):"redirect"!=e.type&&arm_manage_form_scroll(r,".arm-form-container",".arm_form_message_container",e.is_action),jQuery(r).find("input[type='submit'], button[type='submit']").removeAttr("disabled").removeClass("active")},error:function(e){jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery("body").offset().top-80},1e3);var a="";"object"==typeof e?a=e.responseText:"string"==typeof e&&(a=e),-1!==a.indexOf("<!DOCTYPE html>")?jQuery("html").html(a):(jQuery(r).parent(".arm-form-container").find(".arm_form_message_container").html(a).show(),jQuery(r).find("input[type='submit'], button[type='submit']").removeAttr("disabled").removeClass("active"))}})}function arm_manage_form_scroll(e,a,r,t){var n,_;"armrp"!=t&&(_=(n=(_=(0<(t=jQuery(e).closest(".arm_popup_wrapper").length)?jQuery(e).closest(".arm_popup_wrapper"):jQuery(e)).get(0).getBoundingClientRect()).top,_.bottom),0<=n&&_<=window.innerHeight||(0<t?jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(e).closest(".arm_popup_wrapper").offset().top-80},1e3):jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(e).parent(a).find(r).offset().top-80},1e3)))}function arm_setup_form_ajax_action(r){var t=jQuery(r).attr("data-random-id"),n=jQuery("#arm_setup_form"+t).find("#arm_captcha_"+t).length,_=(jQuery(r).find('input[name="arm_filter_input"]').remove(),jQuery('input[name="arm_wp_nonce"]').val()),i=(jQuery(r).prepend('<input type="hidden" name="form_random_key" value="'+t+'" />'),jQuery(r).parents(".arm_setup_form_container")),e=jQuery(r).serialize();jQuery(r).find('input[name="form_random_key"]').remove(),jQuery(".arm_setup_messages").html(""),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_membership_setup_form_ajax_action&"+e,beforeSend:function(){jQuery(r).find("input[type='submit'], button[type='submit']").attr("disabled","disabled").addClass("active")},success:function(e){jQuery(r).find("input[type='submit'], button[type='submit']").removeAttr("disabled").removeClass("active");var a=e.message;"success"==e.status?(void 0!==e.script&&""!==e.script&&jQuery("body").append(e.script),void 0!==e.isHide&&1!=e.isHide||i.find(".arm_setup_messages").html(a).slideDown().delay(5e3).slideUp(),"redirect"!=e.type&&("function"==typeof armResetFileUploader&&armResetFileUploader(r),jQuery(r).find(".arm_module_gateway_input").trigger("change"),jQuery(r).find(".arm_module_plan_input").trigger("change")),"redirect"!=e.type&&(jQuery(r).trigger("reset"),arm_reinit_session_var(r,__ARMAJAXURL,t,_))):(i.find(".arm_setup_messages").html(a).show(),arm_reinit_session_var(r,__ARMAJAXURL,t,_)),"redirect"!=e.type&&(arm_manage_form_scroll(r,".arm_setup_form_container",".arm_setup_messages",""),void 0!==e.isHide&&1!=e.isHide||(0<n&&arm_reload_captcha.call(),i.find(".arm_setup_messages").html(a).slideDown().delay(5e3).slideUp()))}})}function armResetFileUploader(e){jQuery(e).find(".armFileUploadWrapper").each(function(){var e=jQuery(this).find(".armFileUploadProgressBar");e.hide(),e.find(".armbar").css("width","0%"),jQuery(this).find(".armFileUploadContainer").show(),jQuery(this).find(".armFileRemoveContainer").hide(),jQuery(this).find(".armFileUploadProgressInfo").html(""),jQuery(this).find("input").val(""),jQuery(this).find(".armFileMessages").html(""),jQuery(this).find(".arm_old_file").remove()})}function IsEmail(e){return/^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(e)}function FacebookInit(e){var a,r,t;""!=e&&(window.fbAsyncInit=function(){FB.init({appId:e,status:!0,cookie:!0,xfbml:!0,version:"v2.4"})},a=document,r="facebook-jssdk",t=a.getElementsByTagName("script")[0],a.getElementById(r)||((a=a.createElement("script")).id=r,a.src="//connect.facebook.net/en_US/sdk.js",t.parentNode.insertBefore(a,t)))}function FacebookLoginInit(){var t=jQuery('[data-id="arm_social_login_redirect_to"]').val(),n=jQuery('input[name="arm_wp_nonce"]').val(),e=(jQuery(this),["public_profile","email"].join(",")),_=["id","name","first_name","last_name","email","gender","picture"].join(",");"undefined"!=typeof FB&&FB.login(function(e){var a,r;e.authResponse&&(a=e.authResponse.userID,r=e.authResponse.accessToken,FB.api("/me",{fields:_},function(e){e.token=r,e.redirect_to=t,e.userId=a,e.nonce=n,jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_facebook_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),FacebookLoginCallBack(e)}))},{scope:e})}function FacebookLoginCallBack(e){e={action:"arm_social_login_callback",action_type:"facebook",token:e.token,fbuserId:e.userId,_wpnonce:e.nonce};return jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:e,success:function(e){return"redirect"==e.type?location.href=e.message:"success"!=e.status&&(jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").show(),alert(e.message)),!1}}),!1}function arm_open_linked_auth_win(e,a){arm_linkedin_auth_win&&!arm_linkedin_auth_win.closed||(arm_linkedin_auth_win=window.open(e,"popupWindow","width=700,height=400,scrollbars=yes"),arm_linkedin_timer=setInterval(a,1e3)),arm_linkedin_auth_win.focus()}function LinkedInLoginInit(){arm_open_linked_auth_win("https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id="+jQuery('[data-id="arm_social_linkedin_client_id"]').val()+"&redirect_uri="+jQuery('[data-id="arm_social_linkedin_login_auth_url"]').val()+"&scope=openid%20profile%20email","arm_linkedin_auth_win_pooling()")}function arm_linkedin_auth_win_pooling(){var a,r;arm_linkedin_auth_win&&arm_linkedin_auth_win.closed&&(clearInterval(arm_linkedin_timer),a=jQuery("#arm_social_linkedin_access_token").val(),r=jQuery('input[name="arm_wp_nonce"]').val(),""!=a&&(jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_linkedin_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),jQuery.ajax({url:__ARMAJAXURL,data:"action=arm_linkedin_login_callback&access_token="+a+"&_wpnonce="+r,type:"POST",dataType:"json",success:function(e){LinkedInLoginCallBack(e,a,r),jQuery(".arm_social_connect_loader").hide()}})))}function LinkedInLoginCallBack(e,a,r){jQuery('[data-id="arm_social_login_redirect_to"]').val();jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_linkedin_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show();a={action:"arm_social_login_callback",action_type:"linkedin",token:a,_wpnonce:r};return jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:a,success:function(e){return"redirect"==e.type?location.href=e.message:"success"!=e.status&&(jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").show(),alert(e.message)),!1}}),!1}function PinterestInit(e){var a,r,t;""!=e&&(window.pAsyncInit=function(){PDK.init({appId:e,cookie:!0})},a=document,r="pinterest-jssdk",t=a.getElementsByTagName("script")[0],a.getElementById(r)||((a=a.createElement("script")).id=r,a.src="//assets.pinterest.com/sdk/sdk.js",t.parentNode.insertBefore(a,t)))}function PinterestLoginInit(){PDK.login({scope:"read_public"},function(e){e?PDK.me(function(e){e&&!e.error||alert(pinterestError)}):alert(pinterestPermissionError)})}function setCookie(e,a,r,t,n,_){_.cookie=e+"="+escape(a)+"; path=/"+(t?"; domain="+t:"")+(n?"; secure":"")}function arm_VKAuthCallBack(e){jQuery.parseJSON(jQuery("#arm_vk_user_data").val());var a=jQuery('input[name="arm_wp_nonce"]').val(),e=(jQuery('[data-id="arm_social_login_redirect_to"]').val(),jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_vk_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),{action:"arm_social_login_callback",action_type:"vk",token:e,_wpnonce:a});jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:e,success:function(e){return"redirect"==e.type?location.href=e.message:"success"!=e.status&&(jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").show(),alert(e.message)),!1}})}function arm_InstaAuthCallBack(e){jQuery.parseJSON(jQuery("#arm_insta_user_data").val());var a=jQuery('input[name="arm_wp_nonce"]').val(),e=(jQuery('[data-id="arm_social_login_redirect_to"]').val(),jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_insta_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),{action:"arm_social_login_callback",action_type:"insta",token:e,_wpnonce:a});jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:e,success:function(e){return"redirect"==e.type?location.href=e.message:"success"!=e.status&&(jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").show(),alert(e.message)),!1}})}function armAdjustAccountTabs(){jQuery(".arm_account_tabs_wrapper").each(function(){var e=jQuery(this).find(".arm_account_link_tab_active"),a=e.position(),r=e.outerWidth(!0),e=e.outerHeight();jQuery(this).find(".arm_account_slider").css({width:r+"px",left:a.left+"px",top:a.top+e+"px"})})}function arm_form_close_account_action(a){var r=jQuery(a).attr("data-random-id"),e=(jQuery(a).find('input[name="arm_filter_input"]').remove(),jQuery(a).find(".arm_close_account_btn").addClass("active"),jQuery(a).prepend('<input type="hidden" name="form_random_key" value="'+r+'" />'),jQuery(a).serialize());return jQuery(a).find('input[name="form_random_key"]').remove(),jQuery.ajax({url:__ARMAJAXURL,type:"POST",dataType:"json",data:"action=arm_close_account_form_submit_action&"+e,success:function(e){"success"==e.type?location.href=e.url:(e=""!=e.msg?e.msg:closeAccountError,e+='<i class="armfa armfa-times"></i>',jQuery(".arm_close_account_form_container").find("#arm_message_text.arm_error_msg").html(e),jQuery(".arm_close_account_form_container").find(".arm_form_message_container").show().delay(1e4).fadeOut(2e3),jQuery(".arm_close_account_form_container").find(".arm_form_message_container .arm_error_msg").show().delay(1e4).fadeOut(2e3),arm_manage_form_scroll(a,".arm_close_account_form_container",".arm_form_message_container",""),arm_reinit_session_var(a,__ARMAJAXURL,r)),jQuery(a).find(".arm_close_account_btn").removeClass("active")}}),!1}function armvalidatenumber(e){var a=e.key;""==a?armvalidatenumber_extended(e):!/^[0-9.]*$/.test(a)&&["ArrowRight","ArrowLeft","ArrowUp","ArrowRight","Backspace","Home","End","Delete","Tab","Alt","Control","Shift"].indexOf(a)<0&&e.preventDefault()}function armvalidatenumber_extended(e){navigator.appVersion;var a,r,t=navigator.userAgent,n=navigator.appName;parseFloat(navigator.appVersion),parseInt(navigator.appVersion,10);-1!=(r=t.indexOf("OPR/"))||-1!=(r=t.indexOf("Opera"))?n="Opera":-1!=(r=t.indexOf("MSIE"))?(n="Netscape",t.substring(r+5)):-1!=(r=t.indexOf("Chrome"))?n="Chrome":-1!=(r=t.indexOf("Safari"))?n="Safari":-1!=(r=t.indexOf("Firefox"))?n="Firefox":(a=t.lastIndexOf(" ")+1)<(r=t.lastIndexOf("/"))&&(n=t.substring(a,r)).toLowerCase()==n.toUpperCase()&&(n=navigator.appName),"Chrome"==n||"Safari"==n||"Opera"==n?46==e.keyCode||8==e.keyCode||9==e.keyCode||27==e.keyCode||13==e.keyCode||116==e.keyCode||107==e.keyCode||109==e.keyCode||110==e.keyCode||190==e.keyCode&&0==e.shiftKey||61==e.keyCode&&1==e.shiftKey||173==e.keyCode&&0==e.shiftKey||189==e.keyCode&&0==e.shiftKey||187==e.keyCode&&1==e.shiftKey||65==e.keyCode&&!0===e.ctrlKey||67==e.keyCode&&!0===e.ctrlKey||88==e.keyCode&&!0===e.ctrlKey||35<=e.keyCode&&e.keyCode<=39||(e.shiftKey||(e.keyCode<48||57<e.keyCode)&&(e.keyCode<96||105<e.keyCode))&&e.preventDefault():"Firefox"==n?46==e.keyCode||8==e.keyCode||9==e.keyCode||27==e.keyCode||13==e.keyCode||116==e.keyCode||107==e.keyCode||109==e.keyCode||110==e.keyCode||189==e.keyCode||190==e.keyCode&&0==e.shiftKey||61==e.keyCode&&1==e.shiftKey||173==e.keyCode&&0==e.shiftKey||187==e.keyCode&&1==e.shiftKey||65==e.keyCode&&!0===e.ctrlKey||67==e.keyCode&&!0===e.ctrlKey||88==e.keyCode&&!0===e.ctrlKey||35<=e.keyCode&&e.keyCode<=39||(e.shiftKey||(e.keyCode<48||57<e.keyCode)&&(e.keyCode<96||105<e.keyCode))&&e.preventDefault():"Microsoft Internet Explorer"==n||"Netscape"==n?46==e.keyCode||8==e.keyCode||9==e.keyCode||27==e.keyCode||13==e.keyCode||116==e.keyCode||107==e.keyCode||109==e.keyCode||110==e.keyCode||190==e.keyCode&&0==e.shiftKey||61==e.keyCode&&1==e.shiftKey||173==e.keyCode&&0==e.shiftKey||187==e.keyCode&&1==e.shiftKey||189==e.keyCode&&0==e.shiftKey||65==e.keyCode&&!0===e.ctrlKey||67==e.keyCode&&!0===e.ctrlKey||88==e.keyCode&&!0===e.ctrlKey||35<=e.keyCode&&e.keyCode<=39||(e.shiftKey||(e.keyCode<48||57<e.keyCode)&&(e.keyCode<96||105<e.keyCode))&&(e.preventDefault?e.preventDefault():e.returnValue=!1):46==e.keyCode||8==e.keyCode||9==e.keyCode||27==e.keyCode||13==e.keyCode||116==e.keyCode||107==e.keyCode||109==e.keyCode||110==e.keyCode||187==e.keyCode||190==e.keyCode&&0==e.shiftKey||61==e.keyCode&&1==e.shiftKey||173==e.keyCode&&0==e.shiftKey||189==e.keyCode&&1==e.shiftKey||65==e.keyCode&&!0===e.ctrlKey||67==e.keyCode&&!0===e.ctrlKey||88==e.keyCode&&!0===e.ctrlKey||35<=e.keyCode&&e.keyCode<=39||(e.shiftKey||(e.keyCode<48||57<e.keyCode)&&(e.keyCode<96||105<e.keyCode))&&e.preventDefault()}function arm_equal_hight_setup_plan(){jQuery(window).outerWidth()<=500?jQuery(".arm_membership_setup_form").find(".arm_module_plans_ul").each(function(){jQuery(this).find(".arm_module_plan_option").css("height",""),jQuery(this).find(".arm_module_plan_name").css("height","")}):jQuery(".arm_membership_setup_form").each(function(){var e=jQuery(".arm_membership_setup_form");0<e.find(".arm_module_plans_main_container").length&&0<e.find(".arm_module_plans_ul li").length&&e.find(".arm_module_plans_ul").each(function(){jQuery(this).find("li").each(function(){jQuery(this).find(".arm_module_plan_option").css("height","auto"),jQuery(this).find(".arm_module_plan_name").css("height","auto")});var r=0,t=(jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_plan_name").height();a&&r<a&&(r=a)}),0<r&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_plan_name").height(r)}),0);jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_plan_option").outerHeight();a&&t<a&&(t=a)}),0<t&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_plan_option").parent().attr("style","height:"+t+"px;"),jQuery(this).find(".arm_module_plan_option").attr("style","height:"+t+"px;")})}),0<e.find(".arm_setup_paymentcyclebox_main_wrapper").length&&0<e.find(".arm_setup_paymentcyclebox_wrapper .arm_module_payment_cycle_container").length&&jQuery(".arm_membership_setup_form").find(".arm_module_payment_cycle_container").each(function(){0<jQuery(".arm_membership_setup_form").find(".arm_module_payment_cycle_ul li").length&&jQuery(".arm_membership_setup_form").find(".arm_module_payment_cycle_ul").each(function(){jQuery(this).find("li").each(function(){jQuery(this).find(".arm_module_payment_cycle_option").css("height","auto"),jQuery(this).find(".arm_module_payment_cycle_name").css("height","auto")});var r=0,t=(jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_payment_cycle_name").height();a&&r<a&&(r=a)}),0<r&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_payment_cycle_name").height(r),jQuery(this).find(".arm_module_payment_cycle_name").css("line-height",r+"px")}),0);jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_payment_cycle_option").outerHeight();a&&t<a&&(t=a)}),0<t&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_payment_cycle_option").parent().attr("style","height:"+t+"px;"),jQuery(this).find(".arm_module_payment_cycle_option").attr("style","height:"+t+"px;")})})}),0<e.find(".arm_setup_gatewaybox_main_wrapper").length&&0<e.find(".arm_module_gateways_ul li").length&&jQuery(".arm_membership_setup_form").find(".arm_module_gateways_ul").each(function(){jQuery(this).find("li").each(function(){jQuery(this).find(".arm_module_gateway_option").css("height","auto"),jQuery(this).find(".arm_module_gateway_name").css("height","auto")});var r=0,t=(jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_gateway_name").height();a&&r<a&&(r=a)}),0<r&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_gateway_name").height(r),jQuery(this).find(".arm_module_gateway_name").css("line-height",r+"px")}),0);jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_gateway_option").outerHeight();a&&t<a&&(t=a)}),0<t&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_gateway_option").parent().attr("style","height:"+t+"px;"),jQuery(this).find(".arm_module_gateway_option").attr("style","height:"+t+"px;")})})})}function armAdjustDirectoryTemplateBox(){jQuery(".arm_template_wrapper_directorytemplate1, .arm_template_wrapper_directorytemplate3").each(function(){var r;0<jQuery(this).find(".arm_directory_container .arm_user_block").length&&(jQuery(this).find(".arm_directory_container .arm_user_block").css("height","auto"),r=0,jQuery(this).find(".arm_directory_container .arm_user_block").each(function(e){var a=jQuery(this).height();a&&r<a&&(r=a)}),jQuery(this).find(".arm_directory_container .arm_user_block").height(r),jQuery(this).find(".arm_directory_container .arm_user_block").css("min-height","350px"))}),arm_set_directory_template_style()}function arm_set_plan_width(){0<jQuery(".arm_membership_setup_form").length&&(jQuery(".arm_plan_separator").remove(),jQuery(".arm_membership_setup_form").each(function(){var e=jQuery(this),a=e.find(".arm_module_plans_ul").hasClass("arm_column_2"),r=e.find(".arm_module_plans_ul").hasClass("arm_column_3"),t=e.find(".arm_module_plans_ul").hasClass("arm_column_4");a&&e.find("ul.arm_module_plans_ul > li").each(function(e){(e+1)%2==0&&jQuery(this).after("<li class='arm_plan_separator'></li>")}),r&&e.find("ul.arm_module_plans_ul > li").each(function(e){(e+1)%3==0&&jQuery(this).after("<li class='arm_plan_separator'></li>")}),t&&e.find("ul.arm_module_plans_ul > li").each(function(e){(e+1)%4==0&&jQuery(this).after("<li class='arm_plan_separator'></li>")})}))}function arm_set_directory_template_style(){var e,a=jQuery(window).width(),i=jQuery(".arm_template_wrapper").width();jQuery(".arm_user_block").removeClass("remove_bottom_border"),jQuery(".arm_user_block").removeClass("remove_bottom_border_preview"),0<jQuery(".arm_template_wrapper").length&&jQuery(".arm_template_wrapper").each(function(){var a,r,t=jQuery(this),n=4,_="";i<=768&&500<i&&(n=3,_="arm_3_column"),i<=500&&(n=2,_="arm_2_column"),i<=340&&(n=1,_="arm_1_column"),jQuery(".arm_directorytemplate1_seperator").remove(),t.hasClass("arm_template_wrapper_directorytemplate1")&&0<(a=t.find(".arm_user_block").length)&&(r=1,jQuery(".arm_user_block").removeClass("arm_directorytemplate1_last_field"),jQuery(".arm_user_block").removeClass("arm_first_user_block"),t.find(".arm_user_block").each(function(e){($this_=jQuery(this)).addClass(_),0==e||r%n==0?$this_.addClass("arm_first_user_block"):e%n==0&&$this_.addClass("arm_directorytemplate1_last_field"),r==a&&t.prev(2).hasClass("arm_directorytemplate1_last_field"),a==r&&$this_.hasClass("arm_first_user_block")&&$this_.addClass("arm_last_row_first_user_block"),r%n==0&&r!=a&&$this_.after('<div class="arm_user_block arm_directorytemplate1_seperator"></div>'),r++}))}),(a<=500||jQuery(".arm_template_preview_popup").hasClass("arm_mobile_wrapper"))&&(a=jQuery(".arm_directory_paging_container").prev().attr("class"),e=jQuery(".arm_template_preview_popup").hasClass("arm_mobile_wrapper")?"remove_bottom_border_preview":"remove_bottom_border",/arm_user_block/gi.test(a)&&jQuery(".arm_directory_paging_container").prev().addClass(e))}function arm_slider_widget_init(){jQuery.isFunction(jQuery().carouFredSel)&&jQuery(".arm_widget_slider_wrapper_container").each(function(){var e=jQuery(this).attr("data-effect"),e={circular:!0,items:1,responsive:!0,width:"100%",auto:{items:1,play:!0,fx:void 0!==e&&""!=e?e:"slide",easing:!1,duration:1e3}};jQuery(this).carouFredSel(e)})}function arm_do_bootstrap_angular(){var r=[],e=(jQuery(".arm-form-container").each(function(e){var a=jQuery(this).find("form").attr("id");void 0!==a&&r.push(a)}),jQuery(".arm_setup_form_container").each(function(){var e=jQuery(this).find("form").attr("id");void 0!==e&&r.push(e)}),jQuery(".arm_close_account_container").each(function(){var e=jQuery(this).find("form").attr("id");void 0!==e&&r.push(e)}),0);if(void 0!==r&&0<r.length){var a,t,n,_,i=[],o=[],m=jQuery('input[name="arm_wp_nonce"]').val();for(a in r)void 0!==r[a]&&(m=(t=jQuery("#"+r[a])).find('input[name="arm_wp_nonce"]').val(),arm_spam_filter_keypress_check(t.find('[data-id="nonce_start_time"]').val(),t.find('[data-id="nonce_keyboard_press"]').val()),void 0!==t.attr("data-submission-key")?(_=t.attr("data-submission-key"),(n=document.createElement("input")).setAttribute("type","text"),n.setAttribute("style","visibility:hidden !important;display:none !important;opacity:0 !important;"),n.setAttribute("name",_),t.removeAttr("data-submission-key"),t.append(n),_=t.attr("data-random-id"),i.push(t),o.push(_),e=1):void 0!==t.attr("data-random-id")&&(_=t.attr("data-random-id"),i.push(t),o.push(_),e=2));1!=e&&2!=e||arm_reinit_session_var_multiple_form(i,__ARMAJAXURL,o,m,e)}0==e&&0<jQuery("input[name='arm_wp_nonce_check']").length&&arm_reinit_nonce_var()}function arm_spam_filter_keypress_check(e,r){var t=0;{function a(e){var a=new Array,r=0,t=document.getElementsByTagName("input");for(_=0;_<t.length;_++)t[_].className==e&&(a[r]=t[_],r++);return a}for(var n=a("stime"),_=0;_<n.length;_++)n[_].setAttribute("name",e);var i=a("kpress");return document.onkeydown=function(){t++;for(var e=0;e<i.length;e++)i[e].setAttribute("name",r),i[e].value=t},void document.addEventListener("click",function(e){if("submit"==((e=e||window.event).target||e.srcElement).type){t++;for(var a=0;a<i.length;a++)i[a].setAttribute("name",r),i[a].value=t}},!1)}}function arm_reinit_session_var(t,e,a,r){jQuery.ajax({type:"POST",url:e,dataType:"json",data:"action=arm_reinit_session&form_key="+a+"&_wpnonce="+r,success:function(e){var a=e.new_var,r=jQuery(t).find('input:not([name="ct_bot_detector_event_token"],[name="apbct_visible_fields"],[name="ct_no_cookie_hidden_field"])').last();r[0].setAttribute("name",a),r.before("<input type='text' name='arm_filter_input' value='' style='opacity:0 !important;display:none !important;visibility:hidden !important;' />"),""!=e.nonce&&jQuery(document).find('input[name="arm_wp_nonce"]').val(e.nonce)},error:function(e){console.log("Error State:"+e)}})}function arm_reinit_nonce_var(){var e=jQuery(".arm_current_membership_form_container, .arm_transaction_form_container, .arm_template_wrapper").attr("data-random-id");""!=e&&jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_reinit_nonce_var&form_key="+e,success:function(e){""!=e&&(e=e.nonce,jQuery(document).find('input[name="arm_wp_nonce"]').val(e))},error:function(e){console.log("Error State:"+e)}})}function arm_reinit_session_var_multiple_form(e,a,r,t,n){jQuery.ajax({type:"POST",url:a,dataType:"json",data:"action=arm_reinit_session_multiple_form&form_key_arr="+r+"&_wpnonce="+t,success:function(t){if(""!=t){if(1==n)for(let r in t){var e=jQuery("form[data-random-id='"+r+"']");0<e.length&&e.each(function(){var e=t[r],a=jQuery(this).find('input:not([name="ct_bot_detector_event_token"],[name="apbct_visible_fields"],[name="ct_no_cookie_hidden_field"])').last();a[0].setAttribute("name",e),a.before("<input type='text' name='arm_filter_input' value='' style='opacity:0 !important;display:none !important;visibility:hidden !important;' />")})}""!=t.nonce&&jQuery(document).find('input[name="arm_wp_nonce"]').val(t.nonce)}},error:function(e){console.log("Error State Forms:"+e)}})}function armResetCouponCode(e){jQuery(e).find(".arm_apply_coupon_container").find(".notify_msg").remove(),void 0!==jQuery(e).find('input[name="arm_coupon_code"]').val()&&jQuery(e).find('input[name="arm_coupon_code"]').val("")}function arm_update_card_form_ajax_action(r){var t=jQuery(r).attr("data-random-id"),n=(jQuery(r).prepend('<input type="hidden" name="form_random_key" value="'+t+'" />'),jQuery(r).parents(".arm_update_card_form_container")),e=jQuery(r).serialize();jQuery(r).find('input[name="form_random_key"]').remove(),jQuery(".arm_setup_messages").html(""),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_membership_update_card_form_ajax_action&"+e,beforeSend:function(){jQuery(r).find("input[type='submit'], button[type='submit']").attr("disabled","disabled").addClass("active")},success:function(e){jQuery(r).find("input[type='submit'], button[type='submit']").removeAttr("disabled").removeClass("active");var a=e.message;"success"==e.status?(a='<div class="arm_success_msg"><ul><li>'+a+"</li></ul></div>",void 0!==e.script&&""!==e.script&&jQuery("body").append(e.script),n.find(".arm_setup_messages").html(""),n.find(".arm_setup_messages").html(a).slideDown().delay(5e3).slideUp(),"redirect"!=e.type&&("function"==typeof armResetFileUploader&&armResetFileUploader(r),jQuery(r).find("input").trigger("change"),jQuery(r).find(".arm_module_plan_input").trigger("change")),"redirect"!=e.type&&(jQuery(r).trigger("reset"),jQuery(r).find("input").trigger("change"),jQuery(r).find("input").parent().removeClass("md-input-has-value").trigger("change"),arm_reinit_session_var(r,__ARMAJAXURL,t))):(a='<div class="arm_error_msg"><ul><li>'+a+"</li></ul></div>",n.find(".arm_setup_messages").html(""),n.find(".arm_setup_messages").html(a).show(),arm_reinit_session_var(r,__ARMAJAXURL,t)),"redirect"!=e.type&&(arm_manage_form_scroll(r,".arm_update_card_form_container",".arm_setup_messages",""),n.find(".arm_setup_messages").html(a).slideDown().delay(5e3).slideUp())}})}function arm_create_script_node(e,a,r,t){var n=e.getElementsByTagName(a)[0];e.getElementById(r)||((e=e.createElement(a)).id=r,e.src=t,n.parentNode.insertBefore(e,n))}function arm_create_link_node(e,a,r,t){var n=e.getElementsByTagName(a)[0];e.getElementById(r)||((e=e.createElement(a)).id=r,e.href=t,e.rel="stylesheet",n.parentNode.insertBefore(e,n))}function current_membership_manage_scroll(){jQuery(".arm_current_membership_container").length&&jQuery(".arm_current_membership_container").outerWidth()<=768&&(jQuery(".arm_current_membership_container").css("overflow-x","auto"),jQuery(".arm_current_membership_list_header th#arm_cm_plan_action_btn").css("min-width","100px"),jQuery(".arm_cm_renew_btn_div").css("width","100%"),jQuery(".arm_cm_cancel_btn_div").css("width","100%"),jQuery(".arm_cm_cancel_btn_div").css("margin-top","10px"),jQuery(".arm_cm_update_btn_div").css("margin-top","10px"))}function paid_post_current_membership_manage_scroll(){jQuery(".arm_paid_post_current_membership_container").length&&jQuery(".arm_paid_post_current_membership_container").outerWidth()<=768&&(jQuery(".arm_paid_post_current_membership_container").css("overflow-x","auto"),jQuery(".arm_current_membership_list_header th#arm_cm_plan_action_btn").css("min-width","100px"),jQuery(".arm_cm_renew_btn_div").css("width","100%"),jQuery(".arm_cm_cancel_btn_div").css("width","100%"),jQuery(".arm_cm_cancel_btn_div").css("margin-top","10px"),jQuery(".arm_cm_update_btn_div").css("margin-top","10px"))}function arm_set_plan_height(e){0<e.find(".arm_setup_gatewaybox_main_wrapper").length&&0<e.find(".arm_module_gateways_ul li").length&&jQuery(".arm_membership_setup_form").find(".arm_module_gateways_ul").each(function(){jQuery(this).find("li").each(function(){jQuery(this).find(".arm_module_gateway_option").css("height","auto"),jQuery(this).find(".arm_module_gateway_name").css("height","auto")});var r=0,t=(jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_gateway_name").height();a&&r<a&&(r=a)}),0<r&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_gateway_name").height(r),jQuery(this).find(".arm_module_gateway_name").css("line-height",r+"px")}),0);jQuery(this).find("li.arm_setup_column_item").each(function(e){var a=jQuery(this).find(".arm_module_gateway_option").outerHeight();a&&t<a&&(t=a)}),0<t&&jQuery(this).find("li.arm_setup_column_item").each(function(){jQuery(this).find(".arm_module_gateway_option").parent().attr("style","height:"+t+"px;"),jQuery(this).find(".arm_module_gateway_option").attr("style","height:"+t+"px;")})})}function armGetLastScrollableElement(e){for(var a=jQuery(e).parentsUntil("body"),r=a.length,t=0,n=[],_=0;t<r;){var i=a[t];void 0!==i&&jQuery(i).hasScrollBar()&&(n[_]=jQuery(i),_++),t++}return n[n.length-1]}function hideConfirmBoxCallbackCover(){jQuery(".arm_delete_cover_popup").removeClass("armopen").slideUp()}function GoogleSigninInit(){arm_open_google_auth_win(jQuery('[id="arm_social_google_site_redirect"]').val(),"arm_google_auth_win_pooling()")}function arm_open_google_auth_win(e,a){arm_google_auth_win&&!arm_google_auth_win.closed||(arm_google_auth_win=window.open(e,"popupWindow","width=800,height=500,scrollbars=yes"),arm_google_timer=setInterval(a,1e3)),arm_google_auth_win.focus()}function arm_google_auth_win_pooling(){var a,r;return arm_google_auth_win&&arm_google_auth_win.closed&&(clearInterval(arm_google_timer),""!=(a=jQuery('[id="arm_social_google_access_token"]').val())&&(r=jQuery('input[name="arm_wp_nonce"]').val(),jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_google_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),jQuery.ajax({url:__ARMAJAXURL,data:"action=arm_google_login_callback&access_token="+a+"&_wpnonce="+r,type:"POST",dataType:"json",success:function(e){GoogleLoginCallBack(e,a,r)}}))),!1}function GoogleLoginCallBack(e,a,r){return jQuery(".arm_social_login_main_container").hide(),jQuery(".arm_social_google_container").parent(".arm_social_login_main_container").next(".arm_social_connect_loader").show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:{action:"arm_social_login_callback",action_type:"google",token:a,_wpnonce:r},success:function(e){return"redirect"==e.type?location.href=e.message:"success"!=e.status&&(jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").show(),alert(e.message)),!1},error:function(e){return jQuery(".arm_social_connect_loader").hide(),jQuery(".arm_social_login_main_container").hide(),!1}}),!1}function arm_get_amount_currency_wise_separator(e){var a;return 0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&void 0!==e&&""!=e?(a=jQuery.parseJSON(jQuery(".arm_global_currency_separators").val()),e=(e=(e=(e=e.toString().replace(".","DECIMAL")).toString().replace(",","THOUSAND")).toString().replace("DECIMAL",a.decimal)).toString().replace("THOUSAND",a.thousand)):e}function arm_get_amount_standard_separator(e){var a;return 0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&void 0!==e&&""!=e?(a=jQuery.parseJSON(jQuery(".arm_global_currency_separators").val()),e=(e=(e=(e=e.toString().replace(a.decimal,"DECIMAL")).toString().replace(a.thousand,"THOUSAND")).toString().replace("DECIMAL",".")).toString().replace("THOUSAND",",")):e}function arm_remove_thousand_separator(e){var a;return 0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&void 0!==e&&""!=e?(a=jQuery.parseJSON(jQuery(".arm_global_currency_separators").val()),e=e.toString().replace(a.thousand,"")):e}function armSetDefaultPaymentGateway(e){arm_form_payment_gateway=e}function arm_init_selectpicker_keyscroll(e,a,r){var t=r,n=t.querySelector("dd ul"),_=jQuery(n).find("li:visible"),i=jQuery(n).find("li.hovered:visible")[0]||null;if("ArrowUp"==a)armpreventDefault(e),armpreventDefaultForScrollKeys(e),null===i||0===selected_li?arm_selects(n,_,_[_.length-1]):arm_selects(n,_,_[selected_li-1]);else if("ArrowDown"==a)armpreventDefault(e),armpreventDefaultForScrollKeys(e),null===i||selected_li===_.length-1?arm_selects(n,_,_[0]):arm_selects(n,_,_[selected_li+1]);else if("Enter"==a){if(armpreventDefault(e),null!==i){var o=i.getAttribute("data-value"),m=i.getAttribute("data-label"),n=(i.getAttribute("data-type"),n.getAttribute("data-id"));if(t.querySelector("dt span").innerHTML=m,document.getElementById(n).value=o,document.getElementById(n).setAttribute("value",o),i.click(),t.classList.remove("arm-is-active"),t.querySelector("dd ul").style.top="",null!=t.querySelector("dd ul li.hovered"))for(var s=t.querySelectorAll("dd ul li.hovered").length,u=0;u<s;u++)void 0!==t.querySelectorAll("dd ul li.hovered")[u]&&t.querySelectorAll("dd ul li.hovered")[u].classList.remove("hovered");m=jQuery(r).parent().find("input").attr("name");jQuery(r).parents("form").find("input[name="+m+"]").trigger("change")}}else if("Escape"==a){var c=document.querySelector(".arm-df__dropdown-control.arm-is-active");if(null!==c&&(c.classList.remove("arm-is-active"),c.querySelector("dd ul").style.display="none",null!=c.querySelector("dd ul li.hovered")))for(s=c.querySelectorAll("dd ul li.hovered").length,u=0;u<s;u++)void 0!==c.querySelectorAll("dd ul li.hovered")[u]&&c.querySelectorAll("dd ul li.hovered")[u].classList.remove("hovered")}else if("Shift"!=e.key&&"Control"!=e.key&&"Alt"!=e.key&&"Tab"!=e.key){n=e.key.toLocaleLowerCase();arm_keyhistory+=n,arm_resetKeyhistory.cancel&&clearInterval(arm_resetKeyhistory.cancel),arm_resetKeyhistory.cancel=arm_resetKeyhistory.start(),/^(.)\1+$/.test(arm_keyhistory)&&(arm_keyhistory=arm_keyhistory.charAt(0));for(let r=0;r<_.length;r++){let a=_[r],e=(null!==i&&i.classList.remove("hovered"),a.getAttribute("data-label").toLocaleLowerCase());if((e=e.replace(/<[^>]+>/g,"")).ARMstringStartsWith(arm_keyhistory)&&!a.classList.contains("hovered")){a.classList.add("hovered");var l=a;let e=l.parentNode;var d=e.querySelectorAll("li");arm_selects(e,d,l);break}}}}function arm_selects(e,a,r){var t,n,_,a=[].indexOf.call(a,r);-1!==a&&(selected_li=a,a=r.offsetHeight,n=(t=e.scrollTop)+e.offsetHeight,_=a*selected_li,null!==document.querySelector("li.hovered")&&document.querySelector("li.hovered").classList.remove("hovered"),r.classList.add("hovered"),(_<t||n<_+a)&&(e.scrollTop=_))}function armpreventDefault(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.returnValue=!1}function armpreventDefaultForScrollKeys(e){if({37:0,38:1,39:0,40:1}[e.keyCode])return armpreventDefault(e),!1}function armdisableScroll(){window.addEventListener&&window.addEventListener("wheel  DOMMouseScroll",armpreventDefault,!1),document.onkeydown=armpreventDefaultForScrollKeys}function armenableScroll(){window.removeEventListener&&(window.removeEventListener("wheel",armpreventDefault,!1),window.removeEventListener("DOMMouseScroll",armpreventDefault,!1)),document.onkeydown=null}var arm_linkedin_auth_win,arm_google_auth_win,Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var a,r,t,n,_,i,o="",m=0;for(e=Base64._utf8_encode(e);m<e.length;)t=(a=e.charCodeAt(m++))>>2,n=(3&a)<<4|(a=e.charCodeAt(m++))>>4,_=(15&a)<<2|(r=e.charCodeAt(m++))>>6,i=63&r,isNaN(a)?_=i=64:isNaN(r)&&(i=64),o=o+this._keyStr.charAt(t)+this._keyStr.charAt(n)+this._keyStr.charAt(_)+this._keyStr.charAt(i);return o},decode:function(e){var a,r,t,n,_,i,o="",m=0;for(e=e.replace(/[^A-Za-z0-9+/=]/g,"");m<e.length;)t=this._keyStr.indexOf(e.charAt(m++)),a=(15&(n=this._keyStr.indexOf(e.charAt(m++))))<<4|(_=this._keyStr.indexOf(e.charAt(m++)))>>2,r=(3&_)<<6|(i=this._keyStr.indexOf(e.charAt(m++))),o+=String.fromCharCode(t<<2|n>>4),64!=_&&(o+=String.fromCharCode(a)),64!=i&&(o+=String.fromCharCode(r));return o=Base64._utf8_decode(o)},_utf8_encode:function(e){e=e.replace(/rn/g,"n");for(var a="",r=0;r<e.length;r++){var t=e.charCodeAt(r);t<128?a+=String.fromCharCode(t):a=127<t&&t<2048?(a+=String.fromCharCode(t>>6|192))+String.fromCharCode(63&t|128):(a=(a+=String.fromCharCode(t>>12|224))+String.fromCharCode(t>>6&63|128))+String.fromCharCode(63&t|128)}return a},_utf8_decode:function(e){var a,r="",t=0;for(c1=c2=0;t<e.length;)(a=e.charCodeAt(t))<128?(r+=String.fromCharCode(a),t++):191<a&&a<224?(c2=e.charCodeAt(t+1),r+=String.fromCharCode((31&a)<<6|63&c2),t+=2):(c2=e.charCodeAt(t+1),c3=e.charCodeAt(t+2),r+=String.fromCharCode((15&a)<<12|(63&c2)<<6|63&c3),t+=3);return r}},arm_keyhistory="",arm_resetKeyhistory={start:function(){return setTimeout(function(){arm_keyhistory=""},800)}},render_arm_captcha_v3=(String.prototype.ARMstringStartsWith||!function(){function e(e){if(null==this)throw new TypeError;var a=String(this);if(e&&"[object RegExp]"==m.call(e))throw new TypeError;var r=a.length,t=String(e),n=t.length,_=1<arguments.length?arguments[1]:void 0,i=((_=_?Number(_):0)!=_&&(_=0),Math.min(Math.max(_,0),r));if(r<n+i)return!1;for(var o=-1;++o<n;)if(a.charCodeAt(i+o)!=t.charCodeAt(o))return!1;return!0}var a=function(){try{var e={},a=Object.defineProperty,r=a(e,e,e)&&a}catch(e){}return r}(),m={}.toString;a?a(String.prototype,"ARMstringStartsWith",{value:e,configurable:!0,writable:!0}):String.prototype.ARMstringStartsWith=e}(),jQuery(document).ready(function(e){jQuery.isFunction(jQuery().datetimepicker)&&jQuery(".arm_datepicker").each(function(){var e=jQuery(this),a=(new Date,e.attr("data-cal_localization")),r=(e.attr("data-date_field"),e.attr("data-dateformat")),t=e.attr("data-show_timepicker");""!=r&&void 0!==r||(r="MM/DD/YYYY"),""!=t&&void 0!==t&&1==t&&(r+=" hh:mm A"),e.datetimepicker({useCurrent:!1,format:r,locale:a}).on("dp.change",function(e){jQuery(this).trigger("input")})}),jQuery(window).on("load",function(){jQuery("input.arm_module_plan_input:checked").each(function(){armSetupHideShowSections(jQuery(this).parents(".arm_membership_setup_form"))})}),jQuery(document).on("change","input.arm_module_plan_input",function(){var e=jQuery(this).parents("form").find('[data-id="arm_front_plan_skin_type"]').val(),a=jQuery(this).parents("form").find('[data-id="arm_front_gateway_skin_type"]').val();jQuery(this).parents(".arm_membership_setup_form").find('input:radio[name="arm_selected_payment_mode"]').length&&jQuery(this).parents(".arm_membership_setup_form").find('input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0).trigger("change"),"skin5"!=e&&("radio"==a?jQuery(this).parents(".arm_membership_setup_form").find(".arm_module_gateway_input:radio:first").trigger("change"):jQuery(this).parents(".arm_membership_setup_form").find(".payment_gateway_dropdown_"+e+" li:first").trigger("click"),armSetupHideShowSections(jQuery("#"+jQuery(this).parents(".arm_membership_setup_form").attr("id"))))}),jQuery(document).on("click",".arm_login_popup_form_links",function(){var e=jQuery(this).attr("data-form_id");jQuery("."+e).trigger("click")}),jQuery(document).on("click",".arm_reg_popup_form_links",function(){var e=jQuery(this).attr("data-form_id");jQuery("."+e).trigger("click")}),jQuery.isFunction(jQuery().bPopup)&&(jQuery(document).on("click",".arm_form_popup_link",function(e){var a=jQuery(this).attr("data-form_id"),r=""!=(r=jQuery(this).attr("data-overlay"))?r:.5,t=""!=(t=jQuery(this).attr("data-modal_bg"))?t:"#000000";jQuery(".popup_close_btn").trigger("click"),jQuery(".arm_popup_member_form_"+a).bPopup({opacity:r,modalColor:t,closeClass:"popup_close_btn",zIndex:99999,follow:[!1,!1],onClose:function(){arm_adjust_form_popup(),arm_reset_form_popup("arm_popup_member_form_"+a),0<jQuery(".md-select-backdrop").length&&jQuery(".md-select-backdrop").trigger("click")},onOpen:function(){1<jQuery(".arm_popup_member_form_"+a).length&&jQuery(".arm_popup_wrapper.arm_popup_member_form_"+a).each(function(e){$this=jQuery(this),1<e+1&&setTimeout(function(){$this.css("display","none")},10)})}}),setTimeout(function(){arm_adjust_form_popup()},10)}),jQuery(document).on("click",".arm_modal_forgot_form_link",function(){var e=jQuery(this).attr("data-form_id");jQuery(".arm_modal_forgot_form_"+e).bPopup({opacity:.5,closeClass:"popup_close_btn",follow:[!1,!1],onOpen:function(){1<jQuery(".arm_modal_forgot_form_"+e).length&&jQuery(".arm_popup_wrapper.arm_modal_forgot_form_"+e).each(function(e){$this=jQuery(this),1<e+1&&setTimeout(function(){$this.css("display","none")},10)})},onClose:function(){0<jQuery(".md-select-backdrop").length&&jQuery(".md-select-backdrop").trigger("click")}})}),jQuery(document).on("click",".arm_setup_form_popup_link",function(){var e=jQuery(this).attr("data-form_id"),a=""!=(a=jQuery(this).attr("data-overlay"))?a:.5,r=""!=(r=jQuery(this).attr("data-modal_bg"))?r:"#000000";jQuery(".popup_close_btn").trigger("click"),jQuery(".arm_popup_member_setup_form_"+e).bPopup({opacity:a,modalColor:r,closeClass:"popup_close_btn",zIndex:99999,follow:[!1,!1],onClose:function(){arm_adjust_form_popup(),arm_reset_form_popup("arm_popup_member_setup_form_"+e),0<jQuery(".md-select-backdrop").length&&jQuery(".md-select-backdrop").trigger("click")},onOpen:function(){1<jQuery(".arm_popup_member_setup_form_"+e).length&&jQuery(".arm_popup_wrapper.arm_popup_member_setup_form_"+e).each(function(e){$this=jQuery(this),1<e+1&&setTimeout(function(){$this.css("display","none")},10)})}}),setTimeout(function(){arm_adjust_form_popup(),arm_equal_hight_setup_plan()},50)})),jQuery(document).on("keypress",".arm_directory_form_container input",function(e){if(13==(e.keyCode||e.which))return jQuery(".arm_directory_search_btn").trigger("click"),!1}),jQuery(document).on("click",".arm_directory_clear_btn",function(){jQuery(this).closest("form").find("input[type=text].arm_directory_search_box").val(""),jQuery(this).closest("form").find("select.arm_directory_fieldlistby_select option:eq(0)").attr("selected","selected"),jQuery(this).closest("form").find("select.arm_directory_listby_select option:eq(1)").attr("selected","selected");var e=jQuery(this).parents(".arm_directory_form_container"),a=jQuery(this).parents(".arm_directory_search_wrapper_left");e.trigger("reset"),arm_get_directory_list(e,a)}),arm_current_membership_init(),arm_transaction_init(),arm_tooltip_init(),arm_set_plan_width(),arm_set_directory_template_style(),arm_do_bootstrap_angular()}),jQuery(window).on("load",function(){setTimeout(function(){arm_equal_hight_setup_plan()},500),armAdjustAccountTabs(),armAdjustDirectoryTemplateBox(),setTimeout(function(){jQuery(".arm_setup_form_container").show()},100)}),jQuery(window).on("resize",function(){arm_adjust_form_popup(),arm_equal_hight_setup_plan(),armAdjustAccountTabs(),armAdjustDirectoryTemplateBox()}),jQuery(document).on("click",".arm_transactions_container .arm_paging_wrapper_transaction .arm_page_numbers",function(){var e,a,r=jQuery(this).closest("form"),t=jQuery(this).attr("data-page");return jQuery(this).hasClass("current")||(e=r.serialize(),a=r.find('input[name="arm_wp_nonce"]').val(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_transaction_paging_action&current_page="+t+"&"+e+"&_wpnonce="+a,beforeSend:function(){r.find(".arm_transactions_wrapper").css("opacity","0.5")},success:function(e){return r.find(".arm_transactions_wrapper").css("opacity","1"),r.parents(".arm_transactions_container").replaceWith(e),arm_transaction_init(),!1}})),!1}),jQuery(document).on("click",".arm_paid_post_transactions_container .arm_paging_wrapper_transaction .arm_page_numbers",function(){var e,a,r=jQuery(this).closest("form"),t=jQuery(this).attr("data-page");return jQuery(this).hasClass("current")||(e=r.serialize(),a=r.find('input[name="arm_wp_nonce"]').val(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_transaction_paging_action&current_page="+t+"&"+e+"&_wpnonce="+a,beforeSend:function(){r.find(".arm_transactions_wrapper").css("opacity","0.5")},success:function(e){return r.find(".arm_transactions_wrapper").css("opacity","1"),r.parents(".arm_paid_post_transactions_container").replaceWith(e),arm_transaction_init(),!1}})),!1}),jQuery(document).on("click",".arm_membership_history_wrapper .arm_page_numbers",function(){var a=jQuery(this).parents(".arm_membership_history_wrapper"),e=a.attr("data-user_id"),r=jQuery(this).attr("data-page"),t=jQuery(this).attr("data-per_page"),n=jQuery('input[name="arm_wp_nonce"]').val(),_=a.attr("data-is_paid_post");return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_membership_history_paging_action&user_id="+e+"&page="+r+"&per_page="+t+"&_wpnonce="+n+"&is_paid_post="+_,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),!1}}),!1}),jQuery(document).on("click",".arm_user_transaction_wrapper .arm_page_numbers",function(){var a=jQuery(this).parents(".arm_user_transaction_wrapper"),e=a.attr("data-user_id"),r=jQuery(this).attr("data-page"),t=jQuery(this).attr("data-per_page"),n=jQuery('input[name="arm_wp_nonce"]').val(),_=a.attr("data-is_paid_post");return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_get_user_transactions_paging_action&user_id="+e+"&page="+r+"&per_page="+t+"&_wpnonce="+n+"&is_paid_post="+_,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),!1}}),!1}),jQuery(document).on("click",".arm_loginhistory_wrapper .arm_page_numbers",function(){var a=jQuery(this).parents(".arm_loginhistory_wrapper"),e=a.attr("data-user_id"),r=jQuery(this).attr("data-page"),t=jQuery(this).attr("data-per_page"),n=jQuery('input[name="arm_wp_nonce"]').val();return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_user_login_history_paging_action&user_id="+e+"&page="+r+"&per_page="+t+"&_wpnonce="+n,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),!1}}),!1}),jQuery(document).on("click",".arm_all_loginhistory_main_wrapper:not(#arm_all_loginhistory_page_main_wrapper) .arm_page_numbers",function(){var a=jQuery(this).parents(".arm_all_loginhistory_main_wrapper"),e=a.find("input#arm_log_history_search_user").val(),r=jQuery(this).attr("data-page"),t=jQuery(this).attr("data-per_page"),n=jQuery('input[name="arm_wp_nonce"]').val();return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_all_user_login_history_paging_action&page="+r+"&per_page="+t+"&arm_log_history_search_user="+e+"&_wpnonce="+n,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),!1}}),!1}),jQuery(document).on("click","#arm_all_loginhistory_page_main_wrapper .arm_page_numbers",function(){var e=jQuery(this).parents(".arm_all_loginhistory_main_wrapper"),a=jQuery(this).parents(".arm_all_loginhistory_main_wrapper").find(".arm_all_loginhistory_wrapper"),r=e.find("input#arm_log_history_search_user").val(),e=e.find("input#arm_login_history_type").val(),t=jQuery(this).attr("data-page"),n=jQuery(this).attr("data-per_page"),_=jQuery('input[name="arm_wp_nonce"]').val();return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_all_user_login_history_page_paging_action&page="+t+"&per_page="+n+"&arm_login_history_page_search_user="+r+"&arm_failed_login_filter="+e+"&_wpnonce="+_,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),!1}}),!1}),jQuery(document).on("change",".arm_directory_listof_input",function(){jQuery(this).parents(".arm_directory_list_of_filters").find("label").removeClass("arm_active"),jQuery(this).parent("label").addClass("arm_active"),arm_get_directory_list(jQuery(this).parents(".arm_directory_form_container"))}),jQuery(document).on("change",".arm_directory_listby_select",function(){jQuery(this);arm_get_directory_list(jQuery(this).parents(".arm_directory_form_container"),jQuery(this).parents(".arm_search_filter_fields_wrapper_top"))}),jQuery(document).on("click",".arm_directory_search_btn",function(){jQuery(".arm_directory_search_box").val();arm_get_directory_list(jQuery(this).parents(".arm_directory_form_container"),jQuery(this).parents(".arm_directory_search_wrapper_left"))}),jQuery(document).on("click",".arm_directory_form_container .arm_directory_load_more_btn",function(){var e=jQuery(this).parents(".arm_directory_form_container"),a=(jQuery(this).hide(),e.find(".arm_directory_paging_container .arm_load_more_loader").css("display","inline-block"),e.attr("data-temp")),r=jQuery(this).attr("data-page"),t=e.serialize(),e=e.find('input[name="arm_wp_nonce"]').val();jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_directory_paging_action&current_page="+r+"&"+t+"&_wpnonce="+e,beforeSend:function(){},success:function(e){return jQuery(".arm_template_wrapper_"+a+" .arm_directory_paging_container").replaceWith(e),arm_tooltip_init(),setTimeout(function(){armAdjustDirectoryTemplateBox()},100),!1}})}),jQuery(document).on("click",".arm_directory_form_container .arm_page_numbers",function(){var e,a,r=jQuery(this).parents(".arm_directory_form_container"),t=r.attr("data-temp"),n=jQuery(this).attr("data-page");return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||(e=r.serialize(),a=r.find('input[name="arm_wp_nonce"]').val(),r.find(".arm_template_loading").show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_directory_paging_action&current_page="+n+"&"+e+"&_wpnonce="+a,beforeSend:function(){r.find(".arm_template_container").css("opacity","0.5")},success:function(e){return r.find(".arm_template_container").css("opacity","1"),r.find(".arm_template_loading").hide(),jQuery(".arm_template_wrapper_"+t).find(".arm_user_block, .arm_directory_paging_container").remove(),jQuery(".arm_template_wrapper_"+t).find(".arm_template_container").prepend(e),0==jQuery(".arm_template_wrapper_"+t).parents(".arm_template_preview_popup").length&&jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(".arm_template_container").offset().top-80},1e3),arm_tooltip_init(),setTimeout(function(){armAdjustDirectoryTemplateBox()},100),!1}})),!1}),jQuery(document).on("click",".arm_switch_label:not(.disable_section)",function(e){var a=jQuery(this).attr("data-value");jQuery(this).parent(".arm_switch").find(".arm_switch_radio").val(a),jQuery(this).parent(".arm_switch").find(".arm_switch_label").removeClass("active"),jQuery(this).addClass("active")}),jQuery(document).on("change","input.arm_module_cycle_input",function(e){jQuery(this).parents(".arm_module_payment_cycle_ul").find(".arm_setup_column_item").removeClass("arm_active");var a,r,t=jQuery(this).parents("form:first"),n=(jQuery(t).find(".arm_module_payment_cycle_ul input.arm_module_cycle_input:checked").parents(".arm_setup_column_item").addClass("arm_active"),jQuery(this).attr("data-plan_amount")),_=jQuery(this).attr("data-tax"),i=jQuery(t).find('[data-id="arm_front_plan_skin_type"]').val(),o=0,m=0,s=(0<jQuery(".arm_global_currency_separators").length&&""!=jQuery(".arm_global_currency_separators").val()&&(o=1),"skin5"==i?(m=a=(r=jQuery(t).find('input[name="subscription_plan"]')).val(),r=r.parent("dl").find('li.arm__dc--item[data-value="'+a+'"]'),1==o?(s=arm_get_amount_currency_wise_separator(n),r.find(".arm_module_plan_cycle_price").html(s),jQuery(t).find('md-select[name="subscription_plan"]').find(".arm_module_plan_cycle_price").html(s)):(r.find(".arm_module_plan_cycle_price").html(n),jQuery(t).find('md-select[name="subscription_plan"]').find(".arm_module_plan_cycle_price").html(n))):(m=(r=jQuery(t).find("input.arm_module_plan_input:checked")).val(),1==o?(s=arm_get_amount_currency_wise_separator(n),r.parents(".arm_setup_column_item").find(".arm_module_plan_cycle_price").html(s)):r.parents(".arm_setup_column_item").find(".arm_module_plan_cycle_price").html(n)),r.attr("data-amt",n),r.attr("data-tax",_),"skin5"!=i&&(a=r.val(),o=jQuery('li.arm_active input[name="payment_cycle_'+a+'"]').val(),jQuery(t).find('[data-id="arm_payment_cycle_plan_'+a+'"]').val(o)),armResetCouponCode(t),0),n=0,_=jQuery(t).find(".arm_global_currency_decimal").val();""==_&&(_=2),1==jQuery(this).attr("data-pro_rata_enabled")&&"skin5"!=i?(s=jQuery(this).attr("data-pro_rata"),n=jQuery(this).attr("data-pro_rata_amount")):(m=jQuery(t).find('input[name="subscription_plan"]').val(),r=jQuery(this).val(),1==jQuery(".arm_payment_cycle_box_"+m).find('li.arm__dc--item[data-value="'+r+'"][data-plan_id="'+m+'"]').attr("data-pro_rata_enabled")&&(s=jQuery(".arm_payment_cycle_box_"+m).find('li.arm__dc--item[data-value="'+r+'"][data-plan_id="'+m+'"]').attr("data-pro_rata"),n=jQuery(".arm_payment_cycle_box_"+m).find('li.arm__dc--item[data-value="'+r+'"][data-plan_id="'+m+'"]').attr("data-pro_rata_amount"))),""==s&&(s=0),s=parseFloat(s).toFixed(_),n=parseFloat(n).toFixed(_),jQuery(".arm_pro_ration_amount_text").text(s),jQuery(t).find(".arm_payable_amount_text").text(n),armUpdateOrderAmount(t),e.stopPropagation()}),jQuery(document).on("change","input.arm_module_gateway_input",function(e){jQuery(this).parents(".arm_module_gateways_ul").find(".arm_setup_column_item").removeClass("arm_active"),jQuery(this).parents(".arm_setup_column_item").addClass("arm_active");var a,r,t,n,_,i,o,m,s=jQuery(this).val(),u=jQuery(this).parents("form:first"),c=u.attr("id"),l=jQuery(u).find('[data-id="arm_total_payable_amount"]').val(),d=jQuery(u).find('[data-id="arm_front_plan_skin_type"]').val(),p=jQuery(this).attr("data-payment_mode"),y=jQuery("#"+c).find('[data-id="arm_front_gateway_skin_type"]').val(),f=("radio"!=y&&(p=jQuery(this).parent("dl").find('li.arm__dc--item[data-value="'+s+'"]').attr("data-payment_mode")),"skin5"==d?(f=(n=jQuery("#"+c+" .arm_module_plan_input")).val(),r=n.parent("dl").find('li.arm__dc--item[data-value="'+f+'"]').attr("data-is_trial"),t=n.parent("dl").find('li.arm__dc--item[data-value="'+f+'"]').attr("data-trial_amt"),n=n.parent("dl").find('li.arm__dc--item[data-value="'+f+'"]').attr("data-pro_rata_enabled"),a=jQuery("#"+c+' input[name="subscription_plan"]').parent("dl").find('li.arm__dc--item[data-value="'+f+'"]'),_=jQuery("#"+c+' [data-id="arm_user_selected_payment_mode_'+f+'"]').val(),i=jQuery("#"+c+' [data-id="arm_user_old_plan_total_cycle_'+f+'"]').val(),o=jQuery("#"+c+' [data-id="arm_user_done_payment_'+f+'"]').val(),m=a.attr("data-type"),"radio"==y&&("recurring"!=m||-1==jQuery.inArray(f,h)||i==o&&"infinite"!=i||"manual_subscription"!=_?(jQuery("#"+c+" .arm_setup_couponbox_wrapper").show("slow"),"recurring"==m&&"both"==p?(jQuery("#"+c+' input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0).trigger("change"),jQuery("#"+c+" .arm_payment_mode_wrapper").show("slow")):(jQuery("#"+c+' input:radio[name="arm_selected_payment_mode"]').filter('[value="'+p+'"]').prop("checked",!0).trigger("change"),jQuery("#"+c+" .arm_payment_mode_wrapper").hide("slow"))):(jQuery("#"+c+" .arm_payment_mode_wrapper").hide("slow"),jQuery("#"+c+" .arm_setup_couponbox_wrapper").hide("slow")))):(r=jQuery(u).find('input[name="subscription_plan"]:checked').attr("data-is_trial"),t=jQuery(u).find('input[name="subscription_plan"]:checked').attr("data-trial_amt"),n=jQuery(u).find('input[name="subscription_plan"]:checked').attr("data-pro_rata_enabled"),y=jQuery("#"+c+" .arm_module_plan_input:checked"),y=jQuery("#"+c+" .arm_module_plan_input:checked"),_=jQuery("#"+c+' [data-id="arm_user_selected_payment_mode_'+y.attr("value")+'"]').val(),i=jQuery("#"+c+' [data-id="arm_user_old_plan_total_cycle_'+y.attr("value")+'"]').val(),o=jQuery("#"+c+' [data-id="arm_user_done_payment_'+y.attr("value")+'"]').val(),"recurring"!=(m=y.attr("data-type"))||-1==jQuery.inArray(y.attr("value"),h)||i==o&&"infinite"!=i||"manual_subscription"!=_?(jQuery("#"+c+" .arm_setup_couponbox_wrapper").show("slow"),"recurring"==m&&"both"==p?(jQuery("#"+c+' input:radio[name="arm_selected_payment_mode"]').filter('[value="auto_debit_subscription"]').prop("checked",!0).trigger("change"),jQuery("#"+c+" .arm_payment_mode_wrapper").show("slow")):(jQuery("#"+c+' input:radio[name="arm_selected_payment_mode"]').filter('[value="'+p+'"]').prop("checked",!0).trigger("change"),jQuery("#"+c+" .arm_payment_mode_wrapper").hide("slow"))):(jQuery("#"+c+" .arm_setup_couponbox_wrapper").hide("slow"),jQuery("#"+c+" .arm_payment_mode_wrapper").hide("slow"))),jQuery(u).find("[name=arm_selected_payment_mode]:checked").val()),y=("0.00"!=l&&"0"!=l&&"0.0"!=l&&"0.000"!=l||(1==r&&("0.00"==t||"0.000"==t||"0.0"==t||"0"==t)||void 0!==n&&1==n)&&"auto_debit_subscription"==f&&"bank_transfer"!=s?(jQuery("#"+c+" .arm_module_gateway_fields").not(".arm_module_gateway_fields_"+s).slideUp("slow").addClass("arm_hide"),jQuery("#"+c+" .arm_module_gateway_fields_"+s).slideDown("slow").removeClass("arm_hide")):jQuery("#"+c+" .arm_module_gateway_fields").slideUp("slow").addClass("arm_hide"),jQuery("#"+c+' [data-id="arm_user_old_plan"]').val()),h=[];null!=y&&(h=y.split(",")),"skin5"==d?armUpdateOrderAmount1(a,u,0):armUpdateOrderAmount(u,0),armResetCouponCode(u),e.stopPropagation()}),jQuery(document).on("click",'.arm-default-form [type="submit"], .arm-default-form [type="button"], .arm_membership_setup_form [type="submit"], .arm_membership_setup_form [type="button"]',function(){var e=jQuery.Event("keydown");e.which=9,jQuery("body").trigger(e)}),jQuery(document).on("click",".arm_cancel_membership_link",function(){var e=confirm(confirmCancelSubscription),r=jQuery(this).attr("data-plan_id"),t=jQuery("#arm_total_current_membership_columns").val(),a=jQuery("#arm_cancel_subscription_message").val(),n=jQuery('input[name="arm_wp_nonce"]').val();return 1==e&&(jQuery(".arm_form_message_container").html(""),jQuery(this).hide(),jQuery(this).parent().find("#arm_field_loader_img_"+r).show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_cancel_membership&type=front&plan_id="+r+"&cancel_message="+a+"&_wpnonce="+n,success:function(e){var a;jQuery(".arm_cancel_sub_error_msg").remove(),"success"==e.type?(a='<td colspan="'+t+'" class="arm_current_membership_cancelled_row">'+e.msg+"</td>",jQuery(".arm_current_membership_tr_"+r).html(a)):"error"==e.type?(0<jQuery(".arm_current_membership_heading_main").length?jQuery(".arm_current_membership_heading_main").after('<div class="arm_cancel_sub_error_msg">'+e.msg+"</div>"):jQuery(".arm_current_membership_container").before('<div class="arm_cancel_sub_error_msg">'+e.msg+"</div>"),jQuery(this).show(),jQuery(this).parent().find("#arm_field_loader_img_"+r).hide()):(alert(errorPerformingAction),jQuery(this).show())}})),!1}),jQuery(document).on("click",".arm_cancel_membership_btn",function(){var e=confirm(confirmCancelSubscription),t=jQuery(this).attr("data-plan_id"),a=jQuery('input[name="arm_wp_nonce"]').val();return 1==e&&(jQuery(".arm_form_message_container").html(""),jQuery(".arm_cancel_membership_plan_"+t).show(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,dataType:"json",data:"action=arm_cancel_membership&type=front&plan_id="+t+"&_wpnonce="+a,success:function(e){var a,r;"success"==e.type?(r='<div class="arm_success_msg">'+(a=""!=e.msg?e.msg:userSubscriptionCancel)+"</div>",jQuery(".arm_cancel_membership_form_container").find(".arm_cancel_membership_message_container_"+t).html(r).show().delay(1e4),jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(".arm_cancel_membership_message_container_"+t).offset().top-80},1e3),jQuery(".arm_cancel_membership_button_link_"+t).hide()):(a=""!=e.msg?e.msg:errorPerformingAction,r='<div class="arm_error_msg">'+(a+='<i class="armfa armfa-times"></i>')+"</div>",jQuery(".arm_cancel_membership_form_container").find(".arm_cancel_membership_message_container_"+t).html(r).show().delay(1e4).fadeOut(2e3),jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(".arm_cancel_membership_message_container_"+t).offset().top-80},1e3),jQuery(".arm_cancel_membership_form_container").find(".arm_cancel_membership_message_container_"+t+" i.armfa-times").on("click",function(){jQuery(".arm_cancel_membership_form_container").find(".arm_cancel_membership_message_container_"+t+" .arm_error_msg").delay(100)})),jQuery(".arm_cancel_membership_plan_"+t).hide()}})),!1}),jQuery(document).on("click",".arm_social_link_twitter",function(e){e.preventDefault(),twitter_auth=window.open(jQuery(this).attr("data-url"),"popupWindow","width=700,height=400,scrollbars=yes");var a=setInterval(function(){twitter_auth.closed&&clearInterval(a)},500)}),jQuery(document).on("click",".arm_social_link_tumblr",function(e){e.preventDefault(),tumblr_auth=window.open(jQuery(this).attr("data-url"),"popupWindow","width=700,height=400,scrollbars=yes");var a=setInterval(function(){tumblr_auth.closed&&clearInterval(a)},500)}),jQuery(document).on("click",".arm_profile_tab_link",function(){var e=jQuery(this).attr("data-tab"),a=jQuery(this).parents(".arm_profile_tabs_container");return a.find(".arm_profile_tab_link").removeClass("arm_profile_tab_link_active"),jQuery(this).addClass(" arm_profile_tab_link_active"),"following"===e&&jQuery(this).addClass("following_count"),a.find(".arm_profile_tab_detail").css("display","none"),jQuery(".arm_profile_tab_detail[data-tab="+e+"]").css("display","block"),!1}),jQuery(document).on("click",".arm_account_link_tab",function(e){var a=jQuery(this).attr("data-tab"),r=jQuery(this).parents(".arm_account_tabs_wrapper"),t=r.find(".arm_account_content_active").attr("data-tab");if(!jQuery(this).hasClass("arm_account_slider")&&a!=t)return r.find(".arm_account_link_tab").removeClass("arm_account_link_tab_active"),r.find(".arm_account_btn_tab").removeClass("arm_account_btn_tab_active"),r.find(".arm_account_detail_tab").removeClass("arm_account_content_active arm_account_content_left arm_account_content_right"),jQuery(this).addClass("arm_account_link_tab_active"),r.find(".arm_account_btn_tab[data-tab="+a+"]").addClass("arm_account_btn_tab_active"),r.find(".arm_account_detail_tab[data-tab="+a+"]").addClass("arm_account_content_active"),r.find(".arm_account_detail_tab[data-tab="+a+"]").nextAll(".arm_account_detail_tab").addClass("arm_account_content_right"),r.find(".arm_account_detail_tab[data-tab="+a+"]").prevAll(".arm_account_detail_tab").addClass("arm_account_content_left"),armAdjustAccountTabs(),!1}),jQuery(document).on("click",".arm_account_btn_tab",function(e){var a=jQuery(this).attr("data-tab"),r=jQuery(this).parents(".arm_account_tabs_wrapper"),t=r.find(".arm_account_content_active").attr("data-tab");if(!jQuery(this).hasClass("arm_account_slider")&&a!=t)return r.find(".arm_account_btn_tab").removeClass("arm_account_btn_tab_active"),jQuery(this).addClass("arm_account_btn_tab_active"),r.find(".arm_account_link_tab").removeClass("arm_account_link_tab_active"),r.find(".arm_account_link_tab[data-tab="+a+"]").addClass("arm_account_link_tab_active"),r.find(".arm_account_detail_tab").removeClass("arm_account_content_active arm_account_content_left arm_account_content_right"),r.find(".arm_account_btn_tab[data-tab="+a+"]").addClass("arm_account_btn_tab_active"),r.find(".arm_account_detail_tab[data-tab="+a+"]").addClass("arm_account_content_active"),r.find(".arm_account_detail_tab[data-tab="+a+"]").nextAll(".arm_account_detail_tab").addClass("arm_account_content_right"),r.find(".arm_account_detail_tab[data-tab="+a+"]").prevAll(".arm_account_detail_tab").addClass("arm_account_content_left"),jQuery(window.opera?"html":"html, body").animate({scrollTop:jQuery(this).offset().top-10},"slow"),!1}),jQuery.fn.armSortElements=function(){var r=[].sort;return function(e,t){t=t||function(){return this};var a=this.map(function(){var e=t.call(this),a=e.parentNode,r=a.insertBefore(document.createTextNode(""),e.nextSibling);return function(){if(a===this)throw new Error("You can't sort elements if any one is a descendant of another.");a.insertBefore(this,r),a.removeChild(r)}});return r.call(this,e).each(function(e){a[e].call(t.call(this))})}}(),jQuery(document).on("change",".arm_selected_payment_mode",function(){var e,a,r,t,n,_,i,o,m,s,u,c=jQuery(this).parents("form:first"),l=jQuery(c).find('[data-id="arm_front_plan_skin_type"]').val();armResetCouponCode(c),"skin5"===l?(e=(u=jQuery(c).find(".arm_module_plan_input")).val(),armUpdateOrderAmount1(a=u.parent("dl").find('li.arm__dc--item[data-value="'+e+'"]'),c,0)):armUpdateOrderAmount(c,0),jQuery(this).is(":checked")&&(r="#"+c.attr("id")+" ",t=jQuery(c).find('[data-id="arm_total_payable_amount"]').val(),o=jQuery(c).find('[data-id="arm_front_gateway_skin_type"]').val(),m="",n=jQuery(this).val(),"radio"==o?(s=jQuery(c).find("li.arm_active input[name=payment_gateway]:checked").attr("value"),"both"!=(m=jQuery(c).find("li.arm_active input[name=payment_gateway]:checked").attr("data-payment_mode"))&&(n=m),void 0===s&&void 0!==(s=jQuery(c).find("li.arm_active input[name=payment_gateway]").attr("value"))&&jQuery(c).find("li.arm_active input[name=payment_gateway]").prop("checked",!0),o="skin5"!=l?(_=jQuery(c).find('input[name="subscription_plan"]:checked').attr("data-is_trial"),i=jQuery(c).find('input[name="subscription_plan"]:checked').attr("data-trial_amt"),jQuery(c).find('input[name="subscription_plan"]:checked').attr("data-pro_rata_enabled")):(_=u.parent("dl").find('li.arm__dc--item[data-value="'+e+'"]').attr("data-is_trial"),i=u.parent("dl").find('li.arm__dc--item[data-value="'+e+'"]').attr("data-trial_amt"),u.parent("dl").find('li.arm__dc--item[data-value="'+e+'"]').attr("data-pro_rata_enabled")),"0.00"!=t&&"0"!=t&&"0.0"!=t&&"0.000"!=t||(1!=_||"0.000"!=i&&"0.00"!=i&&"0.0"!=i&&"0"!=i)&&1!=o||"auto_debit_subscription"!=n?1!=o||"auto_debit_subscription"==n||"0.000"!=t&&"0.00"!=t&&"0.0"!=t&&"0"!=t||jQuery(c).find(".arm_module_gateway_fields").slideUp("slow").addClass("arm_hide"):(jQuery(c).find(".arm_module_gateway_fields").not(".arm_module_gateway_fields_"+s).slideUp("slow").addClass("arm_hide"),jQuery(r+".arm_module_gateway_fields_"+s).slideDown("slow").removeClass("arm_hide"))):(s=(m=jQuery(c).find(".arm_module_gateway_input")).val(),m.find('li.arm__dc--item[data-value="'+s+'"]'),"both"!=(u=jQuery(c).find('li.arm__dc--item[data-value="'+s+'"]').attr("data-payment_mode"))&&(n=u),"0.000"!=t&&"0.00"!=t&&"0.0"!=t&&"0"!=t?(jQuery(r+" .arm_module_gateway_fields").not(".arm_module_gateway_fields_"+s).slideUp("slow").addClass("arm_hide"),jQuery(r+" .arm_module_gateway_fields_"+s).slideDown("slow").removeClass("arm_hide")):"auto_debit_subscription"!=n||"0.000"!=t&&"0.00"!=t&&"0.0"!=t&&"0"!=t?jQuery(c).find(".arm_module_gateway_fields").slideUp("slow").addClass("arm_hide"):(jQuery(r+".arm_module_gateway_fields").not(".arm_module_gateway_fields_"+s).slideUp("slow").addClass("arm_hide"),jQuery(r+".arm_module_gateway_fields_"+s).slideDown("slow").removeClass("arm_hide"))),armResetCouponCode(c),"skin5"===l?armUpdateOrderAmount1(a,c,0):armUpdateOrderAmount(c,0))}),jQuery(document).on("click",".arm_current_membership_container .arm_renew_subscription_button",function(){var s=jQuery(this).closest("form").attr("id"),u=jQuery(this).attr("data-plan_id"),e=jQuery(this).closest("form").find("#loader_img").val(),c=jQuery(this).closest("form").find("#setup_id").val(),l=jQuery(this).closest("form").find("#arm_form_style_css").val(),d=jQuery(this).closest("form").find("#arm_font_awsome").val(),p=jQuery(this).closest("form").find("#arm_stripe_js").val(),a=jQuery('input[name="arm_wp_nonce"]').val();jQuery(this).attr("data-is_paid_post");jQuery(".arm_current_membership_container").html(""),jQuery(".arm_current_membership_container_loader_img").html('<div class="arm_loading_grid"><img src="'+e+'" alt="'+ARM_Loding+'"></div>'),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_renew_plan_action&plan_id="+u+"&setup_id="+c+"&_wpnonce="+a,dataType:"html",success:function(a){for(var e=document.getElementsByTagName("script"),r=document.getElementsByTagName("link"),t=e.length,n=r.length,_=!1,i=!1,o=!1;t--;)void 0!==e[t].src&&e[t].src==p&&(o=!0);for(;n--;)void 0!==r[n].href&&r[n].href==l&&(_=!0),void 0!==r[n].href&&r[n].href==d&&(i=!0);o||arm_create_script_node(document,"script","stripe_js",p),_||arm_create_link_node(document,"link","arm_form_style_css",l),i||arm_create_link_node(document,"link","arm-font-awesome-css",d);var m=setInterval(function(){var e;"undefined"!=typeof Stripe&&(jQuery(".arm_current_membership_container").html(a),arm_do_bootstrap_angular(),e=setInterval(function(){armSetupHideShowSections(jQuery(".arm_membership_setup_form")),setTimeout(function(){jQuery(".arm_current_membership_container_loader_img").remove(),jQuery(".arm_current_membership_container .arm_setup_form_container").css("display","block")},1e3),clearInterval(e)},1e3),clearInterval(m)),ARMFormInitValidation(s),arm_df__dropdown_control_init(),jQuery(document).find(".arm_setup_form_"+c+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+u+' input[name="payment_cycle_'+u+'"]').attr("data-old_value","0"),jQuery(document).find(".arm_setup_form_"+c+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+u+' input[name="payment_cycle_'+u+'"]').trigger("change"),"function"==typeof arm_gm_change_form_child_counter&&arm_gm_change_form_child_counter(c,u)},1e3)}})}),jQuery(document).on("click",".arm_current_membership_container .arm_update_card_button",function(){var m=jQuery(this).closest("form").attr("id"),s=jQuery(this).attr("data-plan_id"),e=jQuery(this).closest("form").find("#loader_img").val(),u=jQuery(this).closest("form").find("#setup_id").val(),c=jQuery(this).closest("form").find("#arm_form_style_css").val(),a=jQuery(this).text(),l=jQuery(this).closest("form").find("#arm_font_awsome").val(),r=(jQuery(this).closest("form").find("#arm_stripe_js").val(),jQuery('input[name="arm_wp_nonce"]').val());jQuery(this).attr("data-is_paid_post");jQuery(".arm_current_membership_container").html(""),jQuery(".arm_current_membership_container_loader_img").html('<div class="arm_loading_grid"><img src="'+e+'" alt="'+ARM_Loding+'"></div>'),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_update_card_action&plan_id="+s+"&setup_id="+u+"&btn_text="+a+"&_wpnonce="+r,dataType:"html",success:function(a){for(var e=document.getElementsByTagName("script"),r=document.getElementsByTagName("link"),t=e.length,n=r.length,_=!1,i=!1;t--;)void 0!==e[t].src&&e[t].src;for(;n--;)void 0!==r[n].href&&r[n].href==c&&(_=!0),void 0!==r[n].href&&r[n].href==l&&(i=!0);_||arm_create_link_node(document,"link","arm_form_style_css",c),i||arm_create_link_node(document,"link","arm-font-awesome-css",l);var o=setInterval(function(){jQuery(".arm_current_membership_container").html(a),arm_do_bootstrap_angular();var e=setInterval(function(){armSetupHideShowSections(jQuery(".arm_membership_setup_form")),setTimeout(function(){jQuery(".arm_current_membership_container_loader_img").remove(),jQuery(".arm_current_membership_container .arm_update_card_form_container").css("display","block")},0),clearInterval(e)},0);clearInterval(o),ARMFormInitValidation(m),jQuery(document).find(".arm_setup_form_"+u+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+s+' input[name="payment_cycle_'+s+'"]').attr("data-old_value","0"),jQuery(document).find(".arm_setup_form_"+u+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+s+' input[name="payment_cycle_'+s+'"]').trigger("change")},1e3)}})}),jQuery(document).on("click",".arm_paid_post_current_membership_container .arm_renew_subscription_button",function(){var s=jQuery(this).closest("form").attr("id"),u=jQuery(this).attr("data-plan_id"),e=jQuery(this).closest("form").find("#loader_img").val(),c=jQuery(this).closest("form").find("#setup_id").val(),l=jQuery(this).closest("form").find("#arm_form_style_css").val(),d=jQuery(this).closest("form").find("#arm_font_awsome").val(),p=jQuery(this).closest("form").find("#arm_stripe_js").val(),a=jQuery('input[name="arm_wp_nonce"]').val();jQuery(this).attr("data-is_paid_post");jQuery(".arm_paid_post_current_membership_container").html(""),jQuery(".arm_paid_post_current_membership_container_loader_img").html('<div class="arm_loading_grid"><img src="'+e+'" alt="'+ARM_Loding+'"></div>'),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_renew_plan_action&plan_id="+u+"&setup_id="+c+"&_wpnonce="+a,dataType:"html",success:function(a){for(var e=document.getElementsByTagName("script"),r=document.getElementsByTagName("link"),t=e.length,n=r.length,_=!1,i=!1,o=!1;t--;)void 0!==e[t].src&&e[t].src==p&&(o=!0);for(;n--;)void 0!==r[n].href&&r[n].href==l&&(_=!0),void 0!==r[n].href&&r[n].href==d&&(i=!0);o||arm_create_script_node(document,"script","stripe_js",p),_||arm_create_link_node(document,"link","arm_form_style_css",l),i||arm_create_link_node(document,"link","arm-font-awesome-css",d);var m=setInterval(function(){var e;"undefined"!=typeof Stripe&&(jQuery(".arm_paid_post_current_membership_container").html(a),arm_do_bootstrap_angular(),e=setInterval(function(){armSetupHideShowSections(jQuery(".arm_membership_setup_form")),setTimeout(function(){jQuery(".arm_paid_post_current_membership_container_loader_img").remove(),jQuery(".arm_paid_post_current_membership_container .arm_setup_form_container").css("display","block")},1e3),clearInterval(e)},1e3),clearInterval(m)),ARMFormInitValidation(s),arm_df__dropdown_control_init(),"function"==typeof arm_gm_change_form_child_counter&&arm_gm_change_form_child_counter(c,u)},1e3);jQuery(document).find(".arm_setup_form_"+c+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+u+' input[name="payment_cycle_'+u+'"]').attr("data-old_value","0"),jQuery(document).find(".arm_setup_form_"+c+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+u+' input[name="payment_cycle_'+u+'"]').trigger("change")}})}),jQuery(document).on("click",".arm_paid_post_current_membership_container .arm_update_card_button",function(){var m=jQuery(this).closest("form").attr("id"),s=jQuery(this).attr("data-plan_id"),e=jQuery(this).closest("form").find("#loader_img").val(),u=jQuery(this).closest("form").find("#setup_id").val(),c=jQuery(this).closest("form").find("#arm_form_style_css").val(),a=jQuery(this).text(),l=jQuery(this).closest("form").find("#arm_font_awsome").val(),r=(jQuery(this).closest("form").find("#arm_stripe_js").val(),jQuery('input[name="arm_wp_nonce"]').val());jQuery(this).attr("data-is_paid_post");jQuery(".arm_paid_post_current_membership_container").html(""),jQuery(".arm_paid_post_current_membership_container_loader_img").html('<div class="arm_loading_grid"><img src="'+e+'" alt="'+ARM_Loding+'"></div>'),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_update_card_action&plan_id="+s+"&setup_id="+u+"&btn_text="+a+"&_wpnonce="+r,dataType:"html",success:function(a){for(var e=document.getElementsByTagName("script"),r=document.getElementsByTagName("link"),t=e.length,n=r.length,_=!1,i=!1;t--;)void 0!==e[t].src&&e[t].src;for(;n--;)void 0!==r[n].href&&r[n].href==c&&(_=!0),void 0!==r[n].href&&r[n].href==l&&(i=!0);_||arm_create_link_node(document,"link","arm_form_style_css",c),i||arm_create_link_node(document,"link","arm-font-awesome-css",l);var o=setInterval(function(){jQuery(".arm_paid_post_current_membership_container").html(a),arm_do_bootstrap_angular();var e=setInterval(function(){armSetupHideShowSections(jQuery(".arm_membership_setup_form")),setTimeout(function(){jQuery(".arm_paid_post_current_membership_container_loader_img").remove(),jQuery(".arm_paid_post_current_membership_container .arm_update_card_form_container").css("display","block")},0),clearInterval(e)},0);clearInterval(o),ARMFormInitValidation(m),jQuery(document).find(".arm_setup_form_"+u+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+s+' input[name="payment_cycle_'+s+'"]').attr("data-old_value","0"),jQuery(document).find(".arm_setup_form_"+u+" .arm_setup_paymentcyclebox_wrapper .arm_payment_cycle_box_"+s+' input[name="payment_cycle_'+s+'"]').trigger("change")},1e3)}})}),jQuery(document).on("click",".arm_front_invoice_detail",function(){var e=jQuery(this).attr("data-log_id"),a=jQuery(this).attr("data-log_type"),r=(jQuery(this).closest("form").find("#arm_form_style_css").val(),jQuery('input[name="arm_wp_nonce"]').val());return""!=e&&0!=e&&jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_invoice_detail&log_id="+e+"&log_type="+a+"&_wpnonce="+r,success:function(e){""!=e?(jQuery(".arm_invoice_detail_container").html(e),jQuery(".arm_invoice_detail_popup").bPopup({opacity:.5,follow:[!1,!1],closeClass:"arm_invoice_detail_close_btn",onClose:function(){jQuery(".arm_invoice_detail_popup").remove()}}).reposition(100)):alert(invoiceTransactionError)}}),!1}),jQuery(document).ready(function(){current_membership_manage_scroll(),paid_post_current_membership_manage_scroll()}),jQuery(document).on("click",'[data-id="arm_setup_two_step_next"]',function(){var e=jQuery(this).parents("form");e.find(".arm_module_forms_main_container").removeClass("arm_hide"),e.find(".arm_setup_gatewaybox_main_wrapper").removeClass("arm_hide"),e.find(".arm_payment_mode_main_wrapper").removeClass("arm_hide"),e.find(".arm_setup_couponbox_main_wrapper").removeClass("arm_hide"),e.find(".arm_setup_summary_text_main_container").removeClass("arm_hide"),e.find(".arm_setup_summary_text_container").removeClass("arm_hide"),e.find(".arm_setup_submit_btn_main_wrapper").removeClass("arm_hide"),jQuery(window.opera?"html":"html, body").animate({scrollTop:e.parents(".arm_setup_form_container").offset().top-100},0),e.find(".arm_setup_paymentcyclebox_main_wrapper").slideUp(),e.find(".arm_module_plans_main_container").slideUp(),e.find(".arm_setup_two_step_next_wrapper").slideUp(),e.find(".arm_setup_two_step_previous_wrapper").slideDown(),arm_set_plan_height(e)}),jQuery(document).on("click",'[data-id="arm_setup_two_step_previous"]',function(){var e=jQuery(this).parents("form");e.find(".arm_module_forms_main_container").addClass("arm_hide"),e.find(".arm_setup_gatewaybox_main_wrapper").addClass("arm_hide"),e.find(".arm_payment_mode_main_wrapper").addClass("arm_hide"),e.find(".arm_setup_couponbox_main_wrapper").addClass("arm_hide"),e.find(".arm_setup_summary_text_main_container").removeClass("arm_hide"),e.find(".arm_setup_summary_text_container").addClass("arm_hide"),e.find(".arm_setup_submit_btn_main_wrapper").addClass("arm_hide"),e.find(".arm_setup_paymentcyclebox_main_wrapper").slideDown(),e.find(".arm_module_plans_main_container").slideDown(),e.find(".arm_setup_two_step_next_wrapper").slideDown(),e.find(".arm_setup_two_step_previous_wrapper").slideUp()}),jQuery.fn.hasScrollBar=function(){return this.get(0).scrollHeight>this.height()},jQuery(document).on("mouseup",function(e){var a,r;0<jQuery(".arm_popup_wrapper:visible").length&&(a=(e=jQuery(e.target)).parents(".md-select-menu-container").length,r=e.parents(".popup_content_text").length,(0==a&&1==r||e.hasClass("popup_content_text"))&&jQuery(".md-select-backdrop").trigger("click"))}),jQuery(document).on("click","svg.arm_card_print_btn",function(){var e=jQuery(this).attr("data-id");document.getElementById(e).contentWindow.print()}),jQuery(document).on("click",".arm_cancel_update_card_btn",function(){location.reload()}),function(){var e,t,n=jQuery(".arm_settings_recaptcha_site_key").val(),_=jQuery(".arm_settings_recaptcha_theme").val();void 0!==window.arm_recaptcha_v3&&"undefined"!=typeof grecaptcha?grecaptcha.ready(function(){grecaptcha.execute(n).then(function(e){for(var a in window.arm_recaptcha_v3){var r=window.arm_recaptcha_v3[a].size;grecaptcha.render(a,{sitekey:n,theme:_,size:r});jQuery("#"+a).val(e).trigger("change")}})}):(e=0,t=setInterval(function(){void 0!==window.arm_recaptcha_v3?grecaptcha.ready(function(){grecaptcha.execute(n).then(function(e){for(var a in window.arm_recaptcha_v3){var r=window.arm_recaptcha_v3[a].size;grecaptcha.render(a,{sitekey:n,theme:_,size:r});jQuery("#"+a).val(e).trigger("change"),clearInterval(t)}})}):10==++e&&clearInterval(t)},1500))}),arm_reload_captcha=function(){var e=jQuery(".arm_settings_recaptcha_site_key").val();void 0!==window.arm_recaptcha_v3&&"undefined"!=typeof grecaptcha&&grecaptcha.ready(function(){grecaptcha.execute(e).then(function(e){for(var a in window.arm_recaptcha_v3)jQuery("#"+a).val(e).trigger("change")})})},arm_form_payment_gateway=(jQuery(document).on("click",".arm_update_stripe_card",function(){var e=jQuery(this).attr("data-plan_id"),a=jQuery('input[name="arm_wp_nonce"]').val(),r=(jQuery(this).closest("form").find("#loader_img").val(),jQuery(this).closest("form").find("#setup_id").val()),_=jQuery(this).closest("form").find("#arm_form_style_css").val(),i=jQuery(this).closest("form").find("#arm_font_awsome").val(),t=(jQuery(this).closest("form").find("#arm_stripe_js").val(),jQuery(this).text()),o=jQuery(this),n=o.parents(".arm_current_membership_form_container");o.attr("disabled","disabled"),o[0].style.cursor="not-allowed",n.addClass("active"),jQuery.ajax({url:__ARMAJAXURL,type:"POST",data:"action=arm_update_stripe_card&plan_id="+e+"&setup_id="+r+"&btn_text="+t+"&_wpnonce="+a,success:function(e){o.removeAttr("disabled"),o[0].style.cursor="";for(var a=document.getElementsByTagName("link"),r=a.length,t=!1,n=!1;r--;)void 0!==a[r].href&&a[r].href==_&&(t=!0),void 0!==a[r].href&&a[r].href==i&&(n=!0);t||arm_create_link_node(document,"link","arm_form_style_css-css",_),n||arm_create_link_node(document,"link","arm-font-awesome-css",i),jQuery("body").append(e)}})}),jQuery(document).on("click",".arm_visible_password, .arm_visible_password_material",function(){var e=jQuery(this).parents(".arm-df__form-field-wrap").find(".arm-df__form-control").attr("type");"text"==e?(jQuery(this).parents(".arm-df__form-field-wrap").find(".arm-df__form-control").attr("type","password"),jQuery(this).find("i").removeClass("armfa-eye-slash"),jQuery(this).find("i").addClass("armfa-eye")):"password"==e&&(jQuery(this).parents(".arm-df__form-field-wrap").find(".arm-df__form-control").attr("type","text"),jQuery(this).find("i").removeClass("armfa-eye"),jQuery(this).find("i").addClass("armfa-eye-slash"))}),jQuery(document).on("click",".arm_visible_password_admin",function(){var e=jQuery(this).parents("td").find(".arm_member_form_input").attr("type");"text"==e?(jQuery(this).parents("td").find(".arm_member_form_input").attr("type","password"),jQuery(this).find("i").removeClass("armfa-eye-slash"),jQuery(this).find("i").addClass("armfa-eye")):"password"==e&&(jQuery(this).parents("td").find(".arm_member_form_input").attr("type","text"),jQuery(this).find("i").removeClass("armfa-eye"),jQuery(this).find("i").addClass("armfa-eye-slash"))}),jQuery(document).on("heartbeat-send",function(e,a){a.arm_update_user_logout_status="1"}),jQuery(document).on("click",".arm_current_membership_container .arm_paging_wrapper_current_membership .arm_page_numbers",function(){var e,a=jQuery(this).closest("form"),r=jQuery(this).attr("data-page");return jQuery(this).hasClass("current")||(e=a.serialize(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_membership_paging_action&current_page="+r+"&"+e,beforeSend:function(){a.find(".arm_current_membership_wrapper").css("opacity","0.5")},success:function(e){return a.find(".arm_current_membership_wrapper").css("opacity","1"),jQuery(".arm_current_membership_container_loader_img").remove(),a.parents(".arm_current_membership_container").replaceWith(e),current_membership_manage_scroll(),arm_current_membership_init(),!1}})),!1}),jQuery(document).on("click",".arm_paid_post_current_membership_container .arm_page_numbers",function(){var e,a=jQuery(this).closest("form"),r=jQuery(this).attr("data-page");return jQuery(this).hasClass("current")||(e=a.serialize(),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_membership_paging_action&current_page="+r+"&"+e,beforeSend:function(){a.find(".arm_current_membership_wrapper").css("opacity","0.5")},success:function(e){return a.find(".arm_current_membership_wrapper").css("opacity","1"),jQuery(".arm_paid_post_current_membership_container_loader_img").remove(),a.parents(".arm_paid_post_current_membership_container").replaceWith(e),paid_post_current_membership_manage_scroll(),arm_current_membership_init(),!1}})),!1}),jQuery(document).on("click",".arm_member_paid_post_plans_paging_container .arm_page_numbers",function(){var a=jQuery(this).parents(".arm_paid_post_plans_wrapper"),e=a.attr("data-user_id"),r=jQuery(this).attr("data-page"),t=jQuery(this).attr("data-per_page"),n=jQuery('input[name="arm_wp_nonce"]').val();return jQuery(this).hasClass("current")||jQuery(this).hasClass("dots")||jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_paid_post_plan_paging_action&user_id="+e+"&page="+r+"&per_page="+t+"&_wpnonce="+n,beforeSend:function(){a.css("opacity","0.4")},success:function(e){return a.css("opacity","1"),a.replaceWith(e),arm_tooltip_init(),jQuery.isFunction(jQuery().datetimepicker)&&jQuery(".arm_user_plan_expiry_date_picker").each(function(){var e=jQuery(this).data("date_format");jQuery(".arm_user_plan_expiry_date_picker").datetimepicker({useCurrent:!1,format:armChangeDateFormat(e),locale:""})}),!1}}),!1}),jQuery(document).on("change",".arm_hidden_checkbox",function(){var e=jQuery(this).val(),a=jQuery(this).attr("data-id");1==jQuery(this).prop("checked")?jQuery("#"+a).val(e):jQuery("#"+a).val("")}),""),ARMGetParents=(document.addEventListener("keydown",function(e){var a=document.querySelector(".arm-df__dropdown-control.arm-is-active");null!==a?(armdisableScroll(),arm_init_selectpicker_keyscroll(e,e.key,a)):armenableScroll()}),jQuery(document).on("keyup",".arm-selectpicker-input-control",function(e){9!=e.keyCode&&"Tab"!=e.key||(jQuery(this).parent().parent().find("dd ul").show(),jQuery(this).parent().find(".arm-df__dropdown-control").addClass("arm-is-active"))}),jQuery(document).on("keydown",".arm-selectpicker-input-control",function(e){9!=e.keyCode&&"Tab"!=e.key||(jQuery(this).parent().parent().find("dd ul").hide(),jQuery(this).parent().find(".arm-df__dropdown-control").removeClass("arm-is-active"))}),function(e,a){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||function(e){for(var a=(this.document||this.ownerDocument).querySelectorAll(e),r=a.length;0<=--r&&a.item(r)!==this;);return-1<r});for(var r=[];e&&e!==document;e=e.parentNode)(!a||e.matches(a))&&r.push(e);return r});jQuery(document).on("click",".arm_other_files",function(){var e=jQuery(this).parent().find("input.arm_file_url").val();window.open(e,"_blank")});