/* Genre Sections Module Styles */

.genre-sections-module {
    margin: 40px 0;
    padding: 0;
}

.genre-sections-header {
    text-align: center;
    margin-bottom: 30px;
}

.genre-sections-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--primary-color, #fff);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.genre-sections-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted, #999);
    margin: 0;
    font-weight: 400;
}

/* Grid Layout */
.genre-sections-content.layout-grid .genre-section {
    margin-bottom: 50px;
    background: var(--section-bg, rgba(255,255,255,0.05));
    border-radius: 12px;
    padding: 25px;
    border: 1px solid var(--border-color, rgba(255,255,255,0.1));
}

.genre-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--accent-color, #e74c3c);
}

.genre-section-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.genre-section-title a {
    color: var(--primary-color, #fff);
    text-decoration: none;
    transition: color 0.3s ease;
}

.genre-section-title a:hover {
    color: var(--accent-color, #e74c3c);
}

.genre-count {
    font-size: 0.9rem;
    color: var(--text-muted, #999);
    font-weight: 400;
    margin-left: 8px;
}

.genre-see-all {
    color: var(--accent-color, #e74c3c);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.genre-see-all:hover {
    color: var(--accent-hover, #c0392b);
    transform: translateX(3px);
}

.genre-content-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.genre-content-grid.columns-2 { grid-template-columns: repeat(2, 1fr); }
.genre-content-grid.columns-3 { grid-template-columns: repeat(3, 1fr); }
.genre-content-grid.columns-4 { grid-template-columns: repeat(4, 1fr); }
.genre-content-grid.columns-5 { grid-template-columns: repeat(5, 1fr); }
.genre-content-grid.columns-6 { grid-template-columns: repeat(6, 1fr); }
.genre-content-grid.columns-7 { grid-template-columns: repeat(7, 1fr); }
.genre-content-grid.columns-8 { grid-template-columns: repeat(8, 1fr); }

/* Carousel Layout */
.genre-sections-content.layout-carousel .genre-section {
    margin-bottom: 40px;
}

.genre-carousel {
    position: relative;
    overflow: hidden;
}

.carousel-item {
    display: inline-block;
    width: 200px;
    margin-right: 15px;
    vertical-align: top;
}

/* Tabs Layout */
.genre-tabs-wrapper {
    background: var(--section-bg, rgba(255,255,255,0.05));
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color, rgba(255,255,255,0.1));
}

.genre-tabs-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    background: var(--tabs-bg, rgba(0,0,0,0.3));
    overflow-x: auto;
    scrollbar-width: thin;
}

.genre-tabs-nav::-webkit-scrollbar {
    height: 4px;
}

.genre-tabs-nav::-webkit-scrollbar-track {
    background: var(--scrollbar-track, rgba(255,255,255,0.1));
}

.genre-tabs-nav::-webkit-scrollbar-thumb {
    background: var(--accent-color, #e74c3c);
    border-radius: 2px;
}

.genre-tab {
    flex-shrink: 0;
}

.genre-tab a {
    display: block;
    padding: 15px 25px;
    color: var(--text-muted, #999);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.genre-tab.active a,
.genre-tab a:hover {
    color: var(--primary-color, #fff);
    background: var(--tab-active-bg, rgba(255,255,255,0.1));
    border-bottom-color: var(--accent-color, #e74c3c);
}

.genre-tabs-content {
    padding: 30px;
}

.genre-tab-content {
    display: none;
}

.genre-tab-content.active {
    display: block;
}

.genre-tab-content .genre-see-all {
    justify-content: center;
    margin-top: 25px;
    padding: 12px 25px;
    background: var(--accent-color, #e74c3c);
    color: white;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
}

.genre-tab-content .genre-see-all:hover {
    background: var(--accent-hover, #c0392b);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .genre-content-grid.columns-8 { grid-template-columns: repeat(6, 1fr); }
    .genre-content-grid.columns-7 { grid-template-columns: repeat(5, 1fr); }
    .genre-content-grid.columns-6 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 992px) {
    .genre-content-grid.columns-8,
    .genre-content-grid.columns-7,
    .genre-content-grid.columns-6,
    .genre-content-grid.columns-5 { 
        grid-template-columns: repeat(4, 1fr); 
    }
    
    .genre-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .genre-sections-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .genre-content-grid.columns-8,
    .genre-content-grid.columns-7,
    .genre-content-grid.columns-6,
    .genre-content-grid.columns-5,
    .genre-content-grid.columns-4 { 
        grid-template-columns: repeat(3, 1fr); 
    }
    
    .genre-sections-title {
        font-size: 1.8rem;
    }
    
    .genre-section-title {
        font-size: 1.5rem;
    }
    
    .genre-tab a {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .genre-content-grid { 
        grid-template-columns: repeat(2, 1fr); 
        gap: 15px;
    }
    
    .genre-sections-module {
        margin: 20px 0;
    }
    
    .genre-sections-content.layout-grid .genre-section {
        padding: 20px 15px;
        margin-bottom: 30px;
    }
    
    .genre-sections-title {
        font-size: 1.6rem;
    }
    
    .genre-section-title {
        font-size: 1.3rem;
    }
    
    .genre-tabs-content {
        padding: 20px 15px;
    }
}

/* Animation Effects */
.genre-section {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.genre-section:nth-child(2) { animation-delay: 0.1s; }
.genre-section:nth-child(3) { animation-delay: 0.2s; }
.genre-section:nth-child(4) { animation-delay: 0.3s; }
.genre-section:nth-child(5) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading State */
.genre-section.loading {
    opacity: 0.6;
    pointer-events: none;
}

.genre-section.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-color, #e74c3c);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
