@charset "utf-8";
.arm_template_wrapper.arm_template_wrapper_directorytemplate5{
    float:none;
    width:100%;
    max-width:1100px;
    border: 1px solid #e0e0e0;
    padding-left: 25px;
    padding-right: 25px;
    padding-top:60px;
    padding-bottom:60px;
    border-radius: 6px;
    -webkit-border-radius:6px;
    -o-border-radius:6px;
    -moz-border-radius:6px;
    margin:0 auto;
    display:block;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper{
    float:left;
    width: 100%;
    margin-bottom: 50px;
    padding: 0 0px 0 50px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper{
    float:left;
    min-width: 35%;
    width: auto;
    margin-right: 8px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_0 .arm_directory_search_wrapper{ margin-right: 0px; }
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box, .arm_search_filter_field_item_top input[type="text"], .arm_search_filter_field_item_top input[type="email"]{
    float:left;
    max-width: 100%;
    width: 100%;
    height: 32px;
    border: 1px #e0e0e0 solid;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_btn{
    float:left;
    width: 38px;
    height:38px;
    background:#ececec;
    border:1px solid #e0e0e0;
    border-left:none;
    color:#000000;
    padding: 0px 7px 4px 9px;
    font-size:12px;
    position: relative;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_clear_wrapper
{
    float: left;
    padding: 3px 0;
}

.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_template_container{
    display: flex;
    flex-wrap: wrap;
}
.arm_template_wrapper_directorytemplate5 .arm_user_block{
    width: 216px;
    min-width: 195px;
    display: inline-block;
    margin: 10px;
    padding: 17px;
    vertical-align: top;
    position: relative;
    text-align: center;
}
.arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator + .arm_user_block{
    border-left: 0;
}
.arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator{
    float:none;
    display: block;
    margin:0 auto !important;
    width: 98% !important;
    height:1px !important;
    padding:0 !important;
    border-top:1px solid #e2e9ed;
}

.arm_template_wrapper_directorytemplate5 .arm_user_block.arm_user_block_with_follow{
    padding: 15px 5px 30px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_avatar{
    display: block;
    max-width: 100%;
    width: 92px;
    margin: 40px auto 18px auto;
    vertical-align: middle;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    border: none;
    overflow: hidden;
    -moz-border-top-left-radius: 20px;
    -webkit-border-top-left-radius: 20px;
    -o-border-top-left-radius: 20px;
    border-top-left-radius: 20px;

    -moz-border-top-right-radius: 4px;
    -webkit-border-top-right-radius: 4px;
    -o-border-top-right-radius: 4px;
    border-top-right-radius: 4px;

    -moz-border-bottom-left-radius: 4px; 
    -webkit-border-bottom-left-radius: 4px; 
    -o-border-bottom-left-radius: 4px; 
    border-bottom-left-radius: 4px; 

    -moz-border-bottom-right-radius: 20px; 
    -webkit-border-bottom-right-radius: 20px; 
    -o-border-bottom-right-radius: 20px; 
    border-bottom-right-radius: 20px; 

}
.arm_template_wrapper_directorytemplate5 .arm_user_avatar:before{
    content: '';
    position: absolute; 
    top: -4px;
    right: -4px;
    bottom: -4px;
    left: -4px;
    border: 4px solid #e4e6e7;
    -moz-border-top-left-radius: 25px;
    -webkit-border-top-left-radius: 25px;
    -o-border-top-left-radius: 25px;
    border-top-left-radius: 25px;

    -moz-border-top-right-radius: 0px;
    -webkit-border-top-right-radius: 0px;
    -o-border-top-right-radius: 0px;
    border-top-right-radius: 0px;

    -moz-border-bottom-left-radius: 0px; 
    -webkit-border-bottom-left-radius: 0px; 
    -o-border-bottom-left-radius: 0px; 
    border-bottom-left-radius: 0px; 

    -moz-border-bottom-right-radius: 25px; 
    -webkit-border-bottom-right-radius: 25px; 
    -o-border-bottom-right-radius: 25px; 
    border-bottom-right-radius: 25px; 

}
.arm_template_wrapper_directorytemplate5 .arm_user_block:hover .arm_user_avatar:before{
    -webkit-animation-name: hvr-ripple-out;
    animation-name: hvr-ripple-out;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-iteration-count: 1;
}
.arm_template_wrapper_directorytemplate5 .arm_dp_user_link{display: block;border: 0;}
.arm_template_wrapper_directorytemplate5 .arm_dp_user_link:hover,
.arm_template_wrapper_directorytemplate5 .arm_user_link:hover,
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_user_social_fields a:hover
{
    box-shadow: none
}
.arm_template_wrapper_directorytemplate5 .arm_user_avatar img {
    width: 100%;
    height: 100%;
    -moz-border-top-left-radius: 20px;
    -webkit-border-top-left-radius: 20px;
    -o-border-top-left-radius: 20px;
    border-top-left-radius: 20px;

    -moz-border-top-right-radius: 0px;
    -webkit-border-top-right-radius: 0px;
    -o-border-top-right-radius: 0px;
    border-top-right-radius: 0px;

    -moz-border-bottom-left-radius: 0px; 
    -webkit-border-bottom-left-radius: 0px; 
    -o-border-bottom-left-radius: 0px; 
    border-bottom-left-radius: 0px; 

    -moz-border-bottom-right-radius: 20px; 
    -webkit-border-bottom-right-radius: 20px; 
    -o-border-bottom-right-radius: 20px; 
    border-bottom-right-radius: 20px; 
    border: 0 !important;
}

.arm_template_wrapper_directorytemplate5 .arm_user_avatar:after{
    content: '';
    bottom: 0;
    left: 0;
    height: 100%;
    width: 100%;
    position: absolute;
    transition: all 0.3s ease-out;
}

.arm_template_wrapper_directorytemplate5 .arm_user_avatar:hover:after{
    content: '';
    transition: all 0.3s ease-out;
}

.arm_template_wrapper_directorytemplate5 .arm_user_avatar:hover .arm_badges_detail{
    transition: all 0.8s ease-out;
    top: 50%;
    transform: translate(-50%,-50%);
    opacity: 1;
}
.arm_template_wrapper_directorytemplate5 .arm_user_link{
    display: block;
    font-size: 17px;
    font-weight: bold;
    text-align: center;
    color: #565765;
    margin: 0px 0 8px 0;
    text-transform: capitalize;
    float:left;
    width:100%;
}
.arm_template_wrapper_directorytemplate5 .arm_badges_detail{
    float:left;
    width:100%;
    text-align: center;
    position: absolute;
    top: 100%;
    transform: translateX(-50%);
    display: inline-block;
    left: 50%;
    z-index: 1;
    transition: all 0.8s ease-out;
    opacity: 0;
}
.arm_template_wrapper_directorytemplate5 .arm_badges_detail .arm-user-badge{
    float:none;
    display: inline-block;
    width:23px !important;
    height:23px !important;
    margin: 6px 9px 6px 3px;
}
.arm_template_wrapper_directorytemplate5 .arm_badges_detail .arm-user-badge img{
    min-width: 30px !important;
    min-height: 30px !important;
    width:100% !important;
    height:100% !important;
    border-radius: 11px;
}

.arm_template_wrapper_directorytemplate5 .arm_last_active_text{
    float:left;
    width:100%;
    text-align:center;
    margin-bottom: 20px;   
}

.arm_template_wrapper_directorytemplate5 .arm_view_profile_btn_wrapper{
    float:left;
    width:100%;
    text-align: center;
}

.arm_template_wrapper_directorytemplate5 .arm_view_profile_btn_wrapper .arm_view_profile_user_link,
.arm_template_wrapper_directorytemplate5 .arm_directory_paging_container .arm_directory_load_more_link{
    float:none;
    display:inline-block;
    font-size: 14px;
    border: 1px solid #CED4DE;
    border-radius: 6px;
    height: 40px;
    padding-left: 32px;
    padding-right:32px;
    margin:0 auto 15px;
    border-radius: 8px;
    -webkit-border-radius:8px;
    -o-border-radius:8px;
    -moz-border-radius:8px;
    width:auto;
    cursor: pointer;
    line-height:40px;
}
.arm_template_wrapper_directorytemplate5 .arm_view_profile_btn_wrapper .arm_view_profile_user_link
{
    height: 34px;
    padding: 0px 24px 0px 24px;
    line-height: 34px;
    margin-bottom: 5px;
}

.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_user_social_fields{
    float:none;
    width:26px;
    height:26px;
    background: #cccccc;
    margin-right:5px;
    border-radius:50%;
    -webkit-border-radius:50%;
    -o-border-radius:50%;
    -moz-border-radius:50%;
    text-align: center;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks{
    width: 100%;
    margin-bottom: 10px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_user_social_fields{
    float:none;
    width:22px;
    height:22px;
    background: #cccccc;
    margin-right: 10px;
    margin-bottom: 10px;
    -webkit-border-radius:50%;
    -o-border-radius:50%;
    -moz-border-radius:50%;
    text-align: center;
    display: inline-block;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_prof_div > a {
    background-position: 15px center;
    border-radius: 30px;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    height: 25px;
    line-height: normal;
    margin: 5px 9px 5px 0;
    min-height: 25px;
    min-width: 25px;
    padding: 0px;
    position: relative;
    text-align: center;
    text-transform: lowercase !important;
    vertical-align: middle;
    width: 25px;
    text-align: center;
}

.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_user_social_fields > a{
    color:#ffffff;
    padding-right:2px;
    display: inline-block;
    border-radius:50%;
    padding-top: 0px !important;
    width:100%;
    height:100%;
    margin-top: -1px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_user_social_fields > a::before {
    position:relative;
    top:0px;
    left: 0px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_facebook > a{
    background-color: #3b5998;
    border: 2px solid #3b5998;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_facebook > a:hover{
    background-color: #ffffff;
    border: 2px solid #3b5998;
    color: #3b5998;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_twitter > a{
     background-color: #00abf0;
    border: 2px solid #00abf0;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_twitter > a:hover{
    background-color: #ffffff;
    border: 2px solid #00abf0;
    color: #00abf0;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_linkedin > a{
    background-color: #0177b5;
    border: 2px solid #0177b5;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_linkedin > a:hover{
     background-color: #ffffff;
    border: 2px solid #0177b5;
    color: #0177b5;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_googleplush > a{
    background-color: #e94738;
    border: 2px solid #e94738;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_googleplush > a:hover{
     background-color: #ffffff;
    border: 2px solid #e94738;
    color: #e94738;

}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_pinterest > a{
    background-color: #ca2026;
    border: 2px solid #ca2026;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_pinterest > a:hover{
     background-color: #ffffff;
    border: 2px solid #ca2026;
    color: #ca2026;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_youtube > a{
    background-color: #E32C28;
    border: 2px solid #E32C28;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_youtube > a:hover{
    background-color: #ffffff;
    border: 2px solid #E32C28;
    color: #E32C28;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_reddit > a{
    background-color: #ff4500;
    border: 2px solid #ff4500;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_reddit > a:hover{
    background-color: #ffffff;
    border: 2px solid #ff4500;
    color: #ff4500;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_delicious > a{
    background-color: #2a96ff;
    border: 2px solid #2a96ff;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_delicious > a:hover{
     background-color: #ffffff;
    border: 2px solid #2a96ff;
    color: #2a96ff;

}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_tumblr > a {
     background-color: #36465d;
    border: 2px solid #36465d;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_tumblr > a:hover{
     background-color: #ffffff;
    border: 2px solid #36465d;
    color: #36465d;

}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_vk > a{
    background-color: #324f77;
    border: 2px solid #324f77;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_vk > a:hover{
    background-color: #ffffff;
    border: 2px solid #324f77;
    color: #324f77;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_instagram > a{
    background-color: #2a5b83;
    border: 2px solid #2a5b83;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_instagram > a:hover{
    background-color: #ffffff;
    border: 2px solid #2a5b83;
    color: #2a5b83;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_dribbble > a{
    background-color: #ea4c89;
    border: 2px solid #ea4c89;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_dribbble > a:hover{
    background-color: #ffffff;
    border: 2px solid #ea4c89;
    color: #ea4c89;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_vine > a{
    background-color: #1cce94;
    border: 2px solid #1cce94;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_vine > a:hover{
    background-color: #ffffff;
    border: 2px solid #1cce94;
    color: #1cce94;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_skype > a{
     background-color: #00aff0;
    border: 2px solid #00aff0;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_skype > a:hover{
    background-color: #ffffff;
    border: 2px solid #00aff0;
    color: #00aff0;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_whatsapp > a{
     background-color: #00e676;
    border: 2px solid #00e676;

}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_whatsapp > a:hover{
    background-color: #ffffff;
    border: 2px solid #00e676;
    color: #00e676;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_tiktok > a{
    background-color: #010101;
    border: 2px solid #010101;
}
.arm_template_wrapper_directorytemplate5 .arm_social_prof_div.arm_social_field_tiktok > a:hover{
    background-color: #ffffff;
    border: 2px solid #010101;
    color: #010101;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks .arm_social_field_tiktok > a:before{
    margin-top: 3px;
    margin-left: 4px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_btns{
    text-align: center;
    margin: 10px auto;
    display: block;
    width: 100%;
    min-height: 35px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_block.arm_user_block_with_follow .arm_user_btns{
    margin: 0 auto 10px;
    position: absolute;
    left: 0;
    bottom: 0;
}

.arm_template_wrapper_directorytemplate5 a.disabled{cursor: not-allowed;}

@-webkit-keyframes hvr-ripple-out {
    100% {
        top: -20px;
        right: -20px;
        bottom: -20px;
        left: -20px;
        opacity: 0;
        border: 4px #00aff0 solid;
    }
}
@keyframes hvr-ripple-out {
    100% {
        top: -20px;
        right: -20px;
        bottom: -20px;
        left: -20px;
        opacity: 0;
        border: 4px #00aff0 solid;
    }
}
.arm_template_wrapper_directorytemplate5 .arm_user_badges_detail {
    text-align: center;
    display: inline-block;
    margin: 0px;
}

.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_search_wrapper{float: right;right: 0;direction:rtl;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_list_of_filters{right: 0;direction:rtl;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_list_by_filters {direction: ltr;float: left;left: 0;}

.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_user_block_right {direction: rtl;right: 0; text-align:right; float:right;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_user_block_left {float: right;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_empty_list {text-align: right;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_listby_select {direction: rtl;right: 0;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_search_wrapper .arm_directory_search_btn{ border-radius: 3px 0 0 3px; float: right !important;}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_directory_search_wrapper .arm_directory_search_box{float: right !important;border-radius: 0px 3px 3px 0px; }
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper
{
    width: 100%;   
}
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper .arm_display_member_profile 
{
    width: 100%;
}
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper li
{
    padding: 9px 0 16px 0px;
}
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper .arm_member_field_label
{
    width: 100%;
    text-align: left;
    display: inline-block;
    vertical-align: middle;
    word-break: break-all;
    word-break: break-word;
    line-height: 22px;
}
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper .arm_member_field_value
{
    width: 100%;
    text-align: left;
    vertical-align: middle;
    display: inline-block;
    word-break: break-all;
    word-break: break-word;
    line-height: 22px;
    font-weight: 700 !important;
}
.arm_template_wrapper_directorytemplate5 .arm_display_members_field_wrapper .arm_display_member_profile .arm_memeber_field_wrapper li:last-child
{
    border: none;
}
.arm_template_preview_popup.arm_mobile_wrapper .arm_template_wrapper_directorytemplate5 .remove_bottom_border_preview{
    border-bottom:none !important;
}
.arm_template_preview_popup.arm_mobile_wrapper .arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator{
    display:none !important;
}
.arm_template_wrapper_directorytemplate5 .arm_directory_form_rtl .arm_badges_detail .arm-user-badge{
    margin: 6px 3px 6px 9px;
}

.arm_template_wrapper_directorytemplate5 .arm_user_block {
    box-shadow: 0px 2.01845px 20.1845px rgba(136, 150, 200, 0.22);
    border-radius: 12px;
}

.arm_template_wrapper_directorytemplate5 .arm_user_block .arm_member_since_detail_wrapper{
    color: #757982 !important;
    font-size: 12px !important;
}
.arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top input {
  width: 100%;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper {
    width: 100%;
    display: flex;
    max-width: 100%;
    flex-wrap: wrap;
    flex-direction: row;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper_top {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding: 8px;
    margin-top: 25px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_button_search_filter_btn_div_top
{
    display: flex;
    margin-left: 10px;
    margin-right: -7px;

}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_container, .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_profile_container {
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    margin-top: 20px;
    text-align: center;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters select {
    width: auto;
}
.arm_template_wrapper_directorytemplate5 .arm_user_badges_detail {
    margin-top: 10px;
    width: 100%;
}
.arm_template_wrapper_directorytemplate5 .arm_user_block{
    margin: 10px;
}
.arm_template_wrapper_directorytemplate5 .arm_user_social_blocks{
    margin-top: 10px;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
    width: 54%;
    margin-right: 2%;
}

.arm_template_wrapper_directorytemplate5, .arm_template_wrapper_directorytemplate5 * {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    line-height: normal;
    padding-top: 0px !important;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_button_search_filter_btn_div .arm_directory_search_btn{
    margin-left: 0;
    border-radius: 5px;
    line-height: initial;
    padding: 0px 30px !important;
    height: 38px;
    margin-right: 15px;
    text-transform: none;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_button_search_filter_btn_div .arm_directory_clear_btn{
    padding: 0px 30px !important;
}
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_radio .arm_search_filter_field_radio_item_label,
.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_dir_filter_input .arm_search_filter_field_radio_item_label{
    margin-left: 5px;
    vertical-align: top;
    line-height: 24px;
}
.arm_template_wrapper .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
    width: 80%;
}
@media (max-width: 980px){
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper{
        padding: 0 0 0 10px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper
    {
        float: left;
        width: 45% ;
        min-width: 40%;
        margin-right: 0px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box
    {
        max-width: 100%;
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter
    {
        width: 40%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter select
    {
        width: 100%;
        float: left;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters
    {
        width: 50%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters select
    {
        width: 90%; 
        max-width: 100%;
        float: left;
    }
}
@media (max-width:768px){
	.arm_template_wrapper_directorytemplate5 .arm_user_block{
        width:46% !important;
        margin:0 auto !important;
        float:none !important;
        display: inline-block !important;
    }
    .arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator{
    	margin-bottom:0 !important;
    	width:100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper
    {
        width: 50%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box
    {
        max-width: 100%;
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters select
    {
        width: 90%; 
        max-width: 100%;
    }
}
@media (max-width: 600px) and (min-width: 501px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter select,
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters select{
        max-width:100%;
        float: left;
        width: 90%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper
    {
        width:100%;
        float: left;
        max-width: 60%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter,
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters{
        width:100%;
        float:left !important;
        text-align: center;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box
    {
        max-width: 100%;
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_1 .arm_directory_clear_wrapper{ float: none; }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_0 .arm_directory_clear_wrapper{ float: left; }
}
@media (min-width:501px) and (max-width:600px ){
	.arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper{
		padding:0 !important;
		border-bottom:none !important;
	}
	.arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator{
		display:none !important;
	}
	.arm_template_wrapper_directorytemplate5 .arm_user_block{
        border:none !important;
        border-bottom:1px solid #DBE1E8 !important;
        margin-bottom:15px !important;
        float:none !important;
        margin:0 auto !important;
        display:block !important;
        width:70% !important;
    }
    .arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters,
	.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper{
		float:left;
		width: 100% !important;
		text-align: center;
	}
	.arm_template_wrapper_directorytemplate5 .arm_directory_search_box,
	.arm_template_wrapper_directorytemplate5 .arm_directory_search_btn{
		display:inline-block;
	}
}
@media (max-width:500px){
    .arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper{
        padding:0 !important;
        border-bottom:none !important;
    }
    .arm_template_wrapper_directorytemplate5 .arm_directorytemplate1_seperator{
        display: none !important;
    }
    .arm_template_wrapper_directorytemplate5 .arm_user_block{
        border:none !important;
        border-bottom:1px solid #DBE1E8 !important;
        margin-bottom:15px !important;
        float:none !important;
        margin:0 auto !important;
        display:block !important;
    }
    .arm_template_wrapper_directorytemplate5 .remove_bottom_border{
        border-bottom:none !important;
    }
    .arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter,
    .arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters,
	.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper{
		width: 100% !important;
		text-align: center;
        max-width: 100% !important;
	}

	.arm_template_wrapper_directorytemplate5 .arm_user_block{
		width:70% !important;
        min-width: 165px;
	}
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box
    {
        max-width: 100% !important;
        width: 100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_field_list_filter select,
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_list_by_filters select{
        width: 90% ;
        max-width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_1 .arm_directory_search_wrapper
    {
        width:90% !important;
        margin-right: 8px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_0 .arm_directory_search_wrapper
    {
        width: 60% !important;
        margin-right: 8px;
    }
}
@media (max-width:480px){
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper
    {
        width:90% !important;
    }
}

@media(min-width: 769px) and (max-width: 1024px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper {
        float: left;
        width: 100%;
        margin-bottom: 50px;
        padding: 0 15px 0 50px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
        width: 31%;
        margin-right: 2%;
    }
    
}
@media (max-width: 768px) and (min-width: 480px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_container,.arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_profile_container {
        width: 100%;
        display: inline-block;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        -o-box-sizing: border-box;
        margin-top: 20px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper_top {
        width: 100%;
        display: flex;
        padding: 8px;
        flex-wrap: wrap;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_directory_search_box, .arm_search_filter_field_item_top input[type="text"],.arm_search_filter_field_item_top input[type="email"] {
        float: left;
        max-width: 100%;
        width: 100%;
        height: 32px;
        border: 1px #e0e0e0 solid;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper {
        width: 100%;
        display: flex;
        max-width: 768px;
        flex-direction: row;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters select {
        width: 100%;
        margin-left: 0px;
        max-width: 100%;
        margin-top: 0px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper .arm_directory_search_box {
        width: 48%;
        margin-right: 2%;
        max-width: 100% !important;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters {
        width: 100%;
        margin-right: 0;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper .arm_button_search_filter_btn_div_top {
        place-self: center;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top {
        max-width:48%;
        width: 100%;
        margin-right: 2%;
        top: auto;
        margin-top: -15px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_filters_wrapper{
        margin-top: -10px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top input {
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper_top .arm_search_filter_field_item_top select {
        width: 100%;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_container_type_1 .arm_directory_search_wrapper {
        float: left;
        width: 100%;
        min-width: 100%;
        margin-right: 0px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 {
        padding-left: 20px !important; 
        padding-right: 20px !important; 
    }
    
}
@media (max-width: 475px)
{
    .arm_template_wrapper_directorytemplate5 .arm_user_block {
        width: 90% !important;
    }
}

@media (min-width: 480px) and (max-width: 600px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper .arm_directory_filters_wrapper .arm_directory_list_by_filters select {
        width: 100%;
        margin-left: 0px;
        max-width: 100%;
        margin-top: 0px;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper input.arm_directory_search_box {
        max-width: 100% !important;
    }
}
@media (max-width: 1024px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_template_container {
        display: flex;
        flex-wrap: wrap;
        padding-right: 25px;
    }
    .arm_template_wrapper_directorytemplate5 .arm_user_block {
        width: 206px !important;
        min-width: 195px;
        display: inline-block;
        margin: 8px;
        padding: 6px;
        vertical-align: top;
        position: relative;
        text-align: center;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_1 .arm_directory_filters_wrapper .arm_directory_search_wrapper {
        width: 100% !important;
        display: flex;
        max-width: 100%;
        flex-direction: row;
    }
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_search_filter_fields_wrapper.arm_search_filter_container_type_0 .arm_directory_filters_wrapper .arm_directory_list_by_filters select {
      width: 100%;
      margin-left: 0;
      margin-top: 10px;
      margin-bottom: 10px;
    }
}
@media (max-width: 480px)
{
    .arm_template_wrapper.arm_template_wrapper_directorytemplate5 .arm_directory_search_wrapper #arm_loader_img {
        position: inherit !important;
    }
}
