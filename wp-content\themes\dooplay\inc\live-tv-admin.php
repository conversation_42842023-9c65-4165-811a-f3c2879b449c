<?php
/*
* Live TV Admin Settings
* @since 2.5.0
*/

// Add Live TV settings page
function dooplay_live_tv_settings_page() {
    add_options_page(
        __('Live TV Settings', 'dooplay'),
        __('Live TV Settings', 'dooplay'),
        'manage_options',
        'live-tv-settings',
        'dooplay_live_tv_settings_callback'
    );
}
add_action('admin_menu', 'dooplay_live_tv_settings_page');

// Settings page callback
function dooplay_live_tv_settings_callback() {
    if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'live_tv_settings')) {
        // Save settings
        update_option('dooplay_live_tv_auto_import', isset($_POST['auto_import']) ? '1' : '0');
        update_option('dooplay_live_tv_auto_test', isset($_POST['auto_test']) ? '1' : '0');
        update_option('dooplay_live_tv_default_m3u', sanitize_url($_POST['default_m3u']));
        update_option('dooplay_live_tv_player_autoplay', isset($_POST['player_autoplay']) ? '1' : '0');
        update_option('dooplay_live_tv_show_offline', isset($_POST['show_offline']) ? '1' : '0');
        
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'dooplay') . '</p></div>';
    }
    
    // Get current settings
    $auto_import = get_option('dooplay_live_tv_auto_import', '1');
    $auto_test = get_option('dooplay_live_tv_auto_test', '1');
    $default_m3u = get_option('dooplay_live_tv_default_m3u', 'https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u');
    $player_autoplay = get_option('dooplay_live_tv_player_autoplay', '1');
    $show_offline = get_option('dooplay_live_tv_show_offline', '0');
    
    // Get statistics
    $total_channels = wp_count_posts('live_tv_channels')->publish;
    $active_channels = get_posts(array(
        'post_type' => 'live_tv_channels',
        'meta_query' => array(
            array(
                'key' => '_live_tv_is_active',
                'value' => '1',
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    ?>
    <div class="wrap">
        <h1><?php _e('Live TV Settings', 'dooplay'); ?></h1>
        
        <div class="live-tv-admin-container">
            <div class="live-tv-admin-main">
                <form method="post" action="">
                    <?php wp_nonce_field('live_tv_settings'); ?>
                    
                    <div class="card">
                        <h2><?php _e('General Settings', 'dooplay'); ?></h2>
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Default M3U Playlist URL', 'dooplay'); ?></th>
                                <td>
                                    <input type="url" name="default_m3u" value="<?php echo esc_attr($default_m3u); ?>" class="regular-text" />
                                    <p class="description"><?php _e('Default M3U playlist URL for automatic imports.', 'dooplay'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Auto Import Channels', 'dooplay'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="auto_import" value="1" <?php checked($auto_import, '1'); ?> />
                                        <?php _e('Automatically import new channels weekly from default M3U playlist', 'dooplay'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Auto Test Channels', 'dooplay'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="auto_test" value="1" <?php checked($auto_test, '1'); ?> />
                                        <?php _e('Automatically test channel availability daily', 'dooplay'); ?>
                                    </label>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="card">
                        <h2><?php _e('Player Settings', 'dooplay'); ?></h2>
                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Auto Play', 'dooplay'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="player_autoplay" value="1" <?php checked($player_autoplay, '1'); ?> />
                                        <?php _e('Enable auto play for live streams', 'dooplay'); ?>
                                    </label>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row"><?php _e('Show Offline Channels', 'dooplay'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="show_offline" value="1" <?php checked($show_offline, '1'); ?> />
                                        <?php _e('Show offline channels in channel list', 'dooplay'); ?>
                                    </label>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <p class="submit">
                        <input type="submit" name="save_settings" class="button-primary" value="<?php _e('Save Settings', 'dooplay'); ?>" />
                    </p>
                </form>
            </div>
            
            <div class="live-tv-admin-sidebar">
                <div class="card">
                    <h2><?php _e('Channel Statistics', 'dooplay'); ?></h2>
                    <div class="live-tv-stats">
                        <div class="stat-box">
                            <div class="stat-number"><?php echo $total_channels; ?></div>
                            <div class="stat-label"><?php _e('Total Channels', 'dooplay'); ?></div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number"><?php echo count($active_channels); ?></div>
                            <div class="stat-label"><?php _e('Active Channels', 'dooplay'); ?></div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number"><?php echo $total_channels - count($active_channels); ?></div>
                            <div class="stat-label"><?php _e('Offline Channels', 'dooplay'); ?></div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h2><?php _e('Quick Actions', 'dooplay'); ?></h2>
                    <div class="quick-actions">
                        <a href="<?php echo admin_url('edit.php?post_type=live_tv_channels&page=live-tv-management'); ?>" class="button button-secondary">
                            <i class="dashicons dashicons-admin-tools"></i>
                            <?php _e('Channel Management', 'dooplay'); ?>
                        </a>
                        <a href="<?php echo admin_url('post-new.php?post_type=live_tv_channels'); ?>" class="button button-secondary">
                            <i class="dashicons dashicons-plus-alt"></i>
                            <?php _e('Add New Channel', 'dooplay'); ?>
                        </a>
                        <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="button button-secondary" target="_blank">
                            <i class="dashicons dashicons-visibility"></i>
                            <?php _e('View Live TV Page', 'dooplay'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="card">
                    <h2><?php _e('Shortcode Usage', 'dooplay'); ?></h2>
                    <p><?php _e('Use these shortcodes to display live TV channels:', 'dooplay'); ?></p>
                    <div class="shortcode-examples">
                        <div class="shortcode-example">
                            <code>[live_tv]</code>
                            <p><?php _e('Display all active channels', 'dooplay'); ?></p>
                        </div>
                        <div class="shortcode-example">
                            <code>[live_tv category="news" limit="6"]</code>
                            <p><?php _e('Display 6 news channels', 'dooplay'); ?></p>
                        </div>
                        <div class="shortcode-example">
                            <code>[live_tv columns="3" show_category="false"]</code>
                            <p><?php _e('Display in 3 columns without category', 'dooplay'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <style>
    .live-tv-admin-container {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 20px;
        margin-top: 20px;
    }
    
    .live-tv-admin-main .card,
    .live-tv-admin-sidebar .card {
        background: white;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    }
    
    .live-tv-admin-main .card h2,
    .live-tv-admin-sidebar .card h2 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1.3em;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    
    .live-tv-stats {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-box {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #e74c3c;
    }
    
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #e74c3c;
        line-height: 1;
    }
    
    .stat-label {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }
    
    .quick-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .quick-actions .button {
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: flex-start;
        text-decoration: none;
    }
    
    .shortcode-examples {
        margin-top: 15px;
    }
    
    .shortcode-example {
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
    }
    
    .shortcode-example code {
        display: block;
        background: #2c3e50;
        color: #ecf0f1;
        padding: 8px 12px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        margin-bottom: 5px;
    }
    
    .shortcode-example p {
        margin: 0;
        font-size: 0.9em;
        color: #666;
    }
    
    @media (max-width: 1200px) {
        .live-tv-admin-container {
            grid-template-columns: 1fr;
        }
        
        .live-tv-admin-sidebar {
            order: -1;
        }
        
        .live-tv-stats {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .live-tv-stats {
            grid-template-columns: 1fr;
        }
    }
    </style>
    <?php
}

// Include this file in functions.php
if (!function_exists('dooplay_include_live_tv_admin')) {
    function dooplay_include_live_tv_admin() {
        if (is_admin()) {
            require_once get_template_directory() . '/inc/live-tv-admin.php';
        }
    }
    add_action('init', 'dooplay_include_live_tv_admin');
}
