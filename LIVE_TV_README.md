# Live TV Feature for DeshiFlix

## Overview
This Live TV feature adds comprehensive live television streaming capabilities to your DeshiFlix website. It includes channel management, M3U playlist scraping, a modern player, and admin controls.

## Features

### 🎯 Core Features
- **Live TV Channels**: Custom post type for managing TV channels
- **M3U Playlist Import**: Automatic scraping and import from M3U playlists
- **HLS Player**: Modern HTML5 video player with HLS.js support
- **Channel Categories**: Organized by News, Entertainment, Sports, Music, Kids, Religious, Other
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Admin Management**: Complete backend management system

### 📺 Player Features
- **HLS Streaming**: Support for .m3u8 live streams
- **Auto-play**: Configurable auto-play functionality
- **Fullscreen Mode**: Native fullscreen support
- **Volume Control**: Mute/unmute functionality
- **Error Handling**: Graceful error handling with retry options
- **Loading States**: Professional loading indicators

### 🛠 Admin Features
- **Channel Management**: Add, edit, delete channels manually
- **M3U Import**: Bulk import channels from M3U playlists
- **Channel Testing**: Automatic testing of stream availability
- **Statistics**: View channel counts and status
- **Settings Page**: Configure auto-import, testing, and player options
- **Scheduled Tasks**: Daily channel testing and weekly auto-import

## Installation

### Files Added
```
wp-content/themes/dooplay/
├── archive-live_tv_channels.php     # Channel archive page
├── single-live_tv_channels.php      # Single channel page
├── page-live-tv.php                 # Custom live TV page template
├── assets/js/live-tv.js             # Player JavaScript
├── inc/live-tv-admin.php            # Admin settings page
└── functions.php                    # Updated with Live TV functions
```

### Database Changes
- New custom post type: `live_tv_channels`
- New meta fields:
  - `_live_tv_stream_url`: Channel stream URL
  - `_live_tv_channel_logo`: Channel logo URL
  - `_live_tv_category`: Channel category
  - `_live_tv_is_active`: Channel status

## Usage

### 1. Admin Setup
1. Go to **WordPress Admin > Live TV > Channel Management**
2. Enter M3U playlist URL: `https://raw.githubusercontent.com/FunctionError/PiratesTv/main/combined_playlist.m3u`
3. Click "Import Channels" to automatically import channels
4. Configure settings in **Settings > Live TV Settings**

### 2. Menu Integration
The Live TV menu item is automatically added to:
- Main header navigation
- Responsive mobile menu
- Styled with red gradient background and TV icon

### 3. Pages Available
- **Live TV Archive**: `/live-tv/` - Shows all channels with category filters
- **Single Channel**: `/live-tv/channel-name/` - Individual channel page
- **Custom Page**: Create a page with "Live TV Page" template

### 4. Shortcodes
Use these shortcodes anywhere on your site:

```php
// Display all channels (default 8 channels, 4 columns)
[live_tv]

// Display specific category with custom limit
[live_tv category="news" limit="6"]

// Custom layout options
[live_tv columns="3" show_category="false"]

// Sports channels only
[live_tv category="sports" limit="8" columns="4"]
```

### 5. Widget
Add the "Live TV Channels" widget to any sidebar:
- Configurable number of channels
- Category filtering
- Compact display with logos

## Channel Management

### Manual Channel Addition
1. Go to **Live TV > Add New**
2. Enter channel name and description
3. Add stream URL (must be .m3u8 format)
4. Upload channel logo
5. Select category
6. Set as active/inactive

### M3U Import Process
1. **Automatic Scraping**: Parses M3U playlist files
2. **Stream Testing**: Tests each URL for availability
3. **Duplicate Prevention**: Skips existing channels
4. **Metadata Extraction**: Extracts logos and categories from M3U
5. **Bulk Import**: Imports working channels only

### Channel Testing
- **Daily Testing**: Automatically tests all channels daily
- **Manual Testing**: Test all channels via admin panel
- **Status Updates**: Automatically disables broken channels
- **Retry Logic**: Multiple retry attempts for temporary failures

## Technical Details

### Stream Support
- **HLS Streams**: Primary support for .m3u8 files
- **Browser Compatibility**: Works with Chrome, Firefox, Safari, Edge
- **Fallback Support**: Native HLS for Safari, HLS.js for others
- **Error Recovery**: Automatic retry on stream failures

### Performance
- **Lazy Loading**: Channels load on demand
- **Caching**: WordPress object caching for channel data
- **Optimized Queries**: Efficient database queries
- **CDN Ready**: All assets can be served via CDN

### Security
- **Nonce Verification**: All admin actions protected
- **Capability Checks**: Proper permission checking
- **Input Sanitization**: All inputs sanitized and validated
- **XSS Prevention**: Output properly escaped

## Customization

### Styling
All styles are included inline for easy customization. Key CSS classes:
- `.live-tv-archive`: Main archive page
- `.live-tv-channel-card`: Individual channel cards
- `.live-tv-modal`: Player modal
- `.live-tv-shortcode`: Shortcode container

### Player Customization
Modify `assets/js/live-tv.js` to:
- Change player settings
- Add custom controls
- Modify error handling
- Customize loading states

### Admin Customization
Edit `inc/live-tv-admin.php` to:
- Add new settings
- Modify import logic
- Change admin interface
- Add custom statistics

## Troubleshooting

### Common Issues

**Channels not playing:**
- Check if stream URL is valid .m3u8 format
- Verify channel is marked as active
- Test stream URL manually
- Check browser console for errors

**Import not working:**
- Verify M3U URL is accessible
- Check server can make external HTTP requests
- Ensure proper permissions for admin user
- Check error logs for detailed messages

**Player not loading:**
- Verify HLS.js library is loading
- Check browser compatibility
- Ensure JavaScript is enabled
- Check for conflicting plugins

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Scheduled Tasks

### Daily Channel Testing
- **Hook**: `dooplay_daily_channel_test`
- **Frequency**: Daily
- **Function**: Tests all channels and updates status

### Weekly Auto Import
- **Hook**: `dooplay_weekly_auto_import`
- **Frequency**: Weekly
- **Function**: Imports new channels from default M3U

### Manual Scheduling
```php
// Force run daily test
do_action('dooplay_daily_channel_test');

// Force run weekly import
do_action('dooplay_weekly_auto_import');
```

## API Endpoints

### AJAX Endpoints
- `wp_ajax_dooplay_import_m3u`: Import M3U playlist
- `wp_ajax_dooplay_test_channels`: Test all channels

### Usage Example
```javascript
// Import M3U via AJAX
jQuery.post(ajaxurl, {
    action: 'dooplay_import_m3u',
    playlist_url: 'https://example.com/playlist.m3u',
    nonce: 'nonce_value'
}, function(response) {
    console.log(response);
});
```

## Support

For support and customization:
1. Check WordPress error logs
2. Verify all files are uploaded correctly
3. Ensure proper file permissions
4. Test with default WordPress theme
5. Check for plugin conflicts

## License

This Live TV feature is part of the DooPlay theme and follows the same licensing terms.
