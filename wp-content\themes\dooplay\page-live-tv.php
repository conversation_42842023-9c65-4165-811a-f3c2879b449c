<?php
/*
Template Name: Live TV Page
*/

get_header(); ?>

<div id="archive" class="live-tv-page">
    <div class="container">
        <!-- Hero Section -->
        <div class="live-tv-hero">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-tv"></i>
                    <?php _e('Live TV Channels', 'dooplay'); ?>
                </h1>
                <p class="hero-description">
                    <?php _e('Watch your favorite TV channels live online for free. Enjoy news, entertainment, sports, and more!', 'dooplay'); ?>
                </p>
                <div class="hero-stats">
                    <?php
                    $total_channels = wp_count_posts('live_tv_channels')->publish;
                    $active_channels = get_posts(array(
                        'post_type' => 'live_tv_channels',
                        'meta_query' => array(
                            array(
                                'key' => '_live_tv_is_active',
                                'value' => '1',
                                'compare' => '='
                            )
                        ),
                        'posts_per_page' => -1,
                        'fields' => 'ids'
                    ));
                    ?>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo count($active_channels); ?></span>
                        <span class="stat-label"><?php _e('Live Channels', 'dooplay'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label"><?php _e('Available', 'dooplay'); ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php _e('Free', 'dooplay'); ?></span>
                        <span class="stat-label"><?php _e('No Cost', 'dooplay'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Channels Section -->
        <div class="featured-channels-section">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                <?php _e('Featured Channels', 'dooplay'); ?>
            </h2>
            <p class="section-description"><?php _e('Most popular and trending live TV channels', 'dooplay'); ?></p>
            
            <?php echo do_shortcode('[live_tv limit="8" columns="4"]'); ?>
        </div>

        <!-- Categories Section -->
        <div class="categories-section">
            <h2 class="section-title">
                <i class="fas fa-th-large"></i>
                <?php _e('Browse by Category', 'dooplay'); ?>
            </h2>
            
            <div class="category-tabs">
                <div class="category-grid">
                    <div class="category-card" data-category="news">
                        <div class="category-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h3><?php _e('News', 'dooplay'); ?></h3>
                        <p><?php _e('Latest news and current affairs', 'dooplay'); ?></p>
                    </div>
                    
                    <div class="category-card" data-category="entertainment">
                        <div class="category-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <h3><?php _e('Entertainment', 'dooplay'); ?></h3>
                        <p><?php _e('Movies, shows and entertainment', 'dooplay'); ?></p>
                    </div>
                    
                    <div class="category-card" data-category="sports">
                        <div class="category-icon">
                            <i class="fas fa-futbol"></i>
                        </div>
                        <h3><?php _e('Sports', 'dooplay'); ?></h3>
                        <p><?php _e('Live sports and matches', 'dooplay'); ?></p>
                    </div>
                    
                    <div class="category-card" data-category="music">
                        <div class="category-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <h3><?php _e('Music', 'dooplay'); ?></h3>
                        <p><?php _e('Music videos and concerts', 'dooplay'); ?></p>
                    </div>
                    
                    <div class="category-card" data-category="kids">
                        <div class="category-icon">
                            <i class="fas fa-child"></i>
                        </div>
                        <h3><?php _e('Kids', 'dooplay'); ?></h3>
                        <p><?php _e('Children\'s programs and cartoons', 'dooplay'); ?></p>
                    </div>
                    
                    <div class="category-card" data-category="religious">
                        <div class="category-icon">
                            <i class="fas fa-pray"></i>
                        </div>
                        <h3><?php _e('Religious', 'dooplay'); ?></h3>
                        <p><?php _e('Religious and spiritual content', 'dooplay'); ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Category Content -->
            <div class="category-content">
                <div class="category-channels" id="news-channels">
                    <h3><?php _e('News Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="news" limit="6" columns="3"]'); ?>
                </div>
                
                <div class="category-channels" id="entertainment-channels" style="display: none;">
                    <h3><?php _e('Entertainment Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="entertainment" limit="6" columns="3"]'); ?>
                </div>
                
                <div class="category-channels" id="sports-channels" style="display: none;">
                    <h3><?php _e('Sports Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="sports" limit="6" columns="3"]'); ?>
                </div>
                
                <div class="category-channels" id="music-channels" style="display: none;">
                    <h3><?php _e('Music Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="music" limit="6" columns="3"]'); ?>
                </div>
                
                <div class="category-channels" id="kids-channels" style="display: none;">
                    <h3><?php _e('Kids Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="kids" limit="6" columns="3"]'); ?>
                </div>
                
                <div class="category-channels" id="religious-channels" style="display: none;">
                    <h3><?php _e('Religious Channels', 'dooplay'); ?></h3>
                    <?php echo do_shortcode('[live_tv category="religious" limit="6" columns="3"]'); ?>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <div class="cta-content">
                <h2><?php _e('Want to see all channels?', 'dooplay'); ?></h2>
                <p><?php _e('Browse our complete collection of live TV channels from around the world.', 'dooplay'); ?></p>
                <a href="<?php echo get_post_type_archive_link('live_tv_channels'); ?>" class="cta-button">
                    <i class="fas fa-tv"></i>
                    <?php _e('View All Channels', 'dooplay'); ?>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Live TV Player Modal -->
<div id="live-tv-player-modal" class="live-tv-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="player-channel-title"><?php _e('Live TV Player', 'dooplay'); ?></h3>
            <button class="modal-close" id="close-player">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div id="live-tv-player-container">
                <video id="live-tv-player" controls autoplay>
                    <source src="" type="application/x-mpegURL">
                    <?php _e('Your browser does not support the video tag.', 'dooplay'); ?>
                </video>
            </div>
            <div class="player-controls">
                <button id="player-fullscreen" class="control-btn">
                    <i class="fas fa-expand"></i>
                    <?php _e('Fullscreen', 'dooplay'); ?>
                </button>
                <button id="player-volume" class="control-btn">
                    <i class="fas fa-volume-up"></i>
                    <?php _e('Volume', 'dooplay'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.live-tv-page {
    padding: 0;
}

/* Live TV Player Modal Styles */
.live-tv-modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    overflow: auto;
}

.modal-content {
    position: relative;
    margin: 3% auto;
    width: 85%;
    max-width: 1000px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.modal-header {
    background: #333;
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 500;
    padding-right: 50px;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.8em;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.3s ease;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255,255,255,0.1);
}

.modal-body {
    padding: 0;
    position: relative;
}

#live-tv-player-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    background: #000;
}

#live-tv-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
}

.player-controls {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background 0.3s ease;
}

.control-btn:hover {
    background: #c0392b;
}

.live-tv-hero {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 60px 0;
    text-align: center;
    margin-bottom: 40px;
}

.hero-title {
    font-size: 3em;
    margin-bottom: 15px;
    font-weight: 700;
}

.hero-title i {
    margin-right: 15px;
    color: #e74c3c;
}

.hero-description {
    font-size: 1.2em;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-number {
    display: block;
    font-size: 2.5em;
    font-weight: bold;
    color: #e74c3c;
    line-height: 1;
}

.hero-stats .stat-label {
    font-size: 0.9em;
    opacity: 0.8;
    margin-top: 5px;
}

.featured-channels-section,
.categories-section {
    margin-bottom: 50px;
}

.section-title {
    font-size: 2.2em;
    margin-bottom: 10px;
    color: #2c3e50;
    text-align: center;
}

.section-title i {
    margin-right: 10px;
    color: #e74c3c;
}

.section-description {
    text-align: center;
    font-size: 1.1em;
    color: #666;
    margin-bottom: 30px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.category-card {
    background: white;
    padding: 30px 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover,
.category-card.active {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
    border-color: #e74c3c;
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2em;
}

.category-card h3 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 1.3em;
}

.category-card p {
    margin: 0;
    color: #666;
    font-size: 0.95em;
    line-height: 1.5;
}

.category-content {
    margin-top: 40px;
}

.category-channels h3 {
    font-size: 1.8em;
    margin-bottom: 20px;
    color: #2c3e50;
    text-align: center;
}

.cta-section {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 50px 0;
    text-align: center;
    border-radius: 10px;
    margin: 40px 0;
}

.cta-content h2 {
    font-size: 2.5em;
    margin-bottom: 15px;
    font-weight: 600;
}

.cta-content p {
    font-size: 1.2em;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: white;
    color: #e74c3c;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1em;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    color: #c0392b;
    text-decoration: none;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.2em;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    .hero-stats .stat-number {
        font-size: 2em;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .category-card {
        padding: 20px 15px;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5em;
    }

    .cta-content h2 {
        font-size: 2em;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-width: none;
    }

    .modal-header {
        padding: 10px 15px;
    }

    .modal-header h3 {
        font-size: 1.1em;
        padding-right: 45px;
    }

    .modal-close {
        font-size: 1.5em;
        width: 35px;
        height: 35px;
    }

    .player-controls {
        padding: 10px 15px;
        flex-wrap: wrap;
        gap: 8px;
    }

    .control-btn {
        padding: 6px 12px;
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 1% auto;
        border-radius: 0;
    }

    .modal-header {
        padding: 8px 12px;
    }

    .modal-header h3 {
        font-size: 1em;
    }

    .modal-close {
        font-size: 1.3em;
        width: 30px;
        height: 30px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Category switching
    const categoryCards = document.querySelectorAll('.category-card');
    const categoryChannels = document.querySelectorAll('.category-channels');
    
    categoryCards.forEach(function(card) {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Remove active class from all cards
            categoryCards.forEach(function(c) {
                c.classList.remove('active');
            });
            
            // Add active class to clicked card
            this.classList.add('active');
            
            // Hide all category channels
            categoryChannels.forEach(function(channels) {
                channels.style.display = 'none';
            });
            
            // Show selected category channels
            const targetChannels = document.getElementById(category + '-channels');
            if (targetChannels) {
                targetChannels.style.display = 'block';
            }
        });
    });
    
    // Set first category as active by default
    if (categoryCards.length > 0) {
        categoryCards[0].classList.add('active');
    }
});
</script>

<?php get_footer(); ?>
