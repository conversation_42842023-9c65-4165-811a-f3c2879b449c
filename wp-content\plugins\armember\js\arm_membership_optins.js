function showConfirmBoxCallback(r){var n=jQuery("#arm_confirm_box_arm_"+r+"_optins_sync");1==n.is(":visible")?hideConfirmBoxCallback():(n.addClass("#arm_confirm_box_arm_"+r+"_optins_sync"),n.addClass("armopen").toggle("slide"),n.parents(".arm_sync_"+r+"_optins").toggleClass("armopen"),n.parents(".arm_sync_"+r+"_optins").append('<div class="arm_confirm_back_wrapper" onclick="hideConfirmBoxCallback();"></div>'))}function hideConfirmBoxCallback_sync(r){return jQuery(".arm_confirm_box.armopen").removeClass("armopen").toggle("slide",function(){jQuery(".arm_sync_"+r+"_optins").removeClass("armopen"),jQuery(".arm_confirm_back_wrapper").remove(),jQuery(".arm_confirm_box.arm_confirm_box_arm_"+r+"_optins_sync").removeClass("arm_confirm_box_arm_"+r+"_optins_sync")}),!1}function arm_start_add_optins_data(n){var r=jQuery('input[name="arm_wp_nonce"]').val(),_=(hideConfirmBoxCallback_sync(n),jQuery(".arm_"+n+"_optins_sync_btn").attr("disabled","disabled"),jQuery(".arm_opt_ins_options_btn").attr("disabled","disabled"),arm_optins_sync_progressbar(n),jQuery(".arm_optins_sync_progressbar").show(),new sack(__ARMAJAXURL));_.execute=0,_.method="POST",_.async=!0,_.setVar("action","arm_optins_sync"),_.setVar("optins_action",n),_.setVar("_wpnonce",r),0!=arm_membership_optins_continue_flag&&(_.setVar("arm_membership_optins_continue_flag",arm_membership_optins_continue_flag),_.setVar("arm_optins_continue_flag",arm_optins_continue_flag)),_.onError=function(){0==arm_optins_continue_flag&&(arm_membership_optins_continue_flag=Number(arm_membership_optins_continue_flag)+Number(1))<=7e4&&arm_start_add_optins_data(n)},_.onCompletion=function(){var r=_.response,r=jQuery.parseJSON(r);arm_optins_continue_flag=r.arm_optins_continue_flag,r.error?(armToast(saveSettingsError,"error"),jQuery("#arm_loader_img_sync").hide(),jQuery(".arm_optins_sync_progressbar").hide(),jQuery(".arm_"+n+"_optins_sync_btn").removeAttr("disabled"),jQuery(".arm_opt_ins_options_btn").removeAttr("disabled")):(r=""!=r.msg?r.msg:saveSettingsSuccess,armToast(r,"success"),jQuery("#arm_loader_img_sync").hide())},_.runAJAX()}function arm_optins_sync_progressbar(_,s){var r=jQuery('input[name="arm_wp_nonce"]').val(),r={url:__ARMAJAXURL,type:"POST",dataType:"json",data:"action=arm_optins_sync_progress&_wpnonce="+r,success:function(r){var n=r.percentage;jQuery(".arm_optins_sync_progressbar_inner").css("width",n+"%"),jQuery(".arm_optins_sync_progressbar_inner").html(n+"%"),!0===r.continue?arm_optins_sync_progressbar(_,s):(jQuery(".arm_optins_sync_progressbar_inner").html("100%"),setTimeout(function(){jQuery(".arm_optins_sync_progressbar").hide(0),jQuery(".arm_optins_sync_progressbar_inner").css("width","0"),jQuery(".arm_optins_sync_progressbar_inner").html("0")},1500),jQuery(".arm_"+_+"_optins_sync_btn").removeAttr("disabled"),jQuery(".arm_opt_ins_options_btn").removeAttr("disabled"))}};jQuery.ajax(r)}