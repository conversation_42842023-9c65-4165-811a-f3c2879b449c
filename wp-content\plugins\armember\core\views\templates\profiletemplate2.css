@charset "utf-8";
.arm_template_wrapper.arm_template_wrapper_profiletemplate2{
    width:100%;
    text-align:center;
}
.arm_template_wrapper.arm_template_wrapper_profiletemplate2 .arm_template_container{
    display: inline-block;
    margin:0 auto;
    float: none;
    max-width:1120px;
}
.arm_template_wrapper.arm_template_wrapper_profiletemplate2 .arm_template_container{
    display: inline-block;
}
.arm_template_wrapper_profiletemplate2{
    font-size: 14px;
    line-height: normal;
    color: #565765;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_wrapper{
    border:none;
    border-radius: 4px;
    display: block;
    width: 100%;
    box-sizing: border-box;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block{
     border-radius: 16px;
    -webkit-border-radius: 16px;
    -moz-border-radius: 16px;
    -o-border-radius: 16px;
    margin: 30px auto auto auto;
    height:220px;
    display: block;
    box-sizing: border-box;
    background-repeat: no-repeat;
    position: relative;
    text-align:left;
    width: calc(100% - 60px);
    background-size: cover !important;
    background-position: center center;
    padding-bottom: 0;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block .arm_template_loading{
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9998;
    width: 100%;
    height: 100%;
    text-align: center;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block .arm_template_loading img{margin-top: 80px;}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block:hover .arm_cover_upload_container:not(.arm_profile){display: block;}
.arm_template_wrapper_profiletemplate2 .arm_cover_upload_container{
    display: none;
    position: absolute;
    right: 40px;
    bottom: -1px;
    z-index: 99;
}
.arm_template_wrapper_profiletemplate2 .arm_cover_upload_container .armCoverUploadBtnContainer{position: relative;float: left;}
.arm_template_wrapper_profiletemplate2 i{vertical-align: middle;cursor: pointer;}
.arm_template_wrapper_profiletemplate2 input{}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block_inner .arm_user_avatar{
    display: block !important;
    text-align: center;
    vertical-align: top;
    max-width: 142px;
    width: 142px;
    max-height: 142px;
    height:142px;
    background-color: #f1b136;
    border: 6px #e0a432 solid;
    border-radius: 100%;
    margin: 5px 0 30px 0;
    z-index: 999;
    position: relative;
    flex: 1;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block_inner .arm_user_avatar img{
    width: 132px;
    height: 130px;
    border-radius: 100%;
    -webkit-border-radius:100%;
    -moz-border-radius: 100%;
    -o-border-radius: 100%;
    background: transparent;
}

.arm_template_wrapper_profiletemplate2 .arm_profile_header_bottom_box{
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    vertical-align: top;
    padding: 0;
    margin: 5px 0 6px;
    text-align: center;
    position: relative;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_header_top_box{
    width:100%;
    display: flex;
    position:absolute;
    left:0;
    bottom:-100px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_header_top_box{
    justify-content: center;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_name_link{
    display: inline-block;
    text-decoration: none;
    font-size: 24px;
    font-weight: bold;
    color: #33334c;
    padding-top: 5px;
    margin-bottom: 15px;
    float:left;
    width:100%;
    margin-top:70px;
    margin-bottom:10px;
}
.arm_template_wrapper_profiletemplate2 .arm_user_last_active_text{
    float:left;
    width:100%;
    text-align: center;
    color:#ffffff !important;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_name_link a{
    color: #33334c;
}
.arm_template_wrapper_profiletemplate2 .arm_user_last_login_time{
    font-size: 16px;
    color: #33334c;
}
.arm_template_wrapper_profiletemplate2 .arm_user_last_login_time span {
    font-size: 16px;
    color: #33334c;
    margin-left: 40px;
}
.arm_template_wrapper_profiletemplate2 .arm_user_about_me{
    display: block;
    font-size: 16px;
    color: #33334c;
    width: 60%;
    margin: 5px auto;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_name_link a{
    text-decoration: none;
    border: 0;
    outline: 0;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_name_link a:hover{box-shadow: none;}
.arm_template_wrapper_profiletemplate2 .arm_profile_tabs{
    display: inline-block;
    width: 100%;
    background-color: #1e1e28;
    padding: 10px;
    text-align: center;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_tabs .arm_profile_tab_link{
    padding: 6px 8px;
    display: inline-block;
    margin: 0 5px 0 0;
    font-size: 16px;
    color: #908c84;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_tabs .arm_profile_tab_count{
    font-style: italic;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_tabs .arm_profile_tab_link:hover,
.arm_template_wrapper_profiletemplate2 .arm_profile_tabs .arm_profile_tab_link.arm_profile_tab_link_active{
    font-size: 16px;
    color: #e89900;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_tab_detail{
    box-sizing: border-box;
    margin-top:0px;
}
.arm_template_wrapper_profiletemplate2 .arm_general_info_container,
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container,
.arm_template_wrapper_profiletemplate2 .arm_activities_container{
    border: 0;
    width: 100%;
}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_listing_wrapper{
    border: 0;
}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_block{
    padding: 15px 0 10px;
}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_left{
    max-width: 80px;
    width: 80px;
}
.arm_template_wrapper_profiletemplate2 .arm_activity_item .arm_activity_avatar,
.arm_template_wrapper_profiletemplate2 .arm_member_info_block .arm_user_avatar{
    display: inline-block;
    width: 60px;
    height: 60px;
    vertical-align: middle;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    border: 1px solid #ededed;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.arm_template_wrapper_profiletemplate2 .arm_activities_container img.avatar,
.arm_template_wrapper_profiletemplate2 .arm_member_info_block img.avatar{
    display: block;
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    height: 100%;
    min-height: 100%;
    max-height: 100%;  
    margin: 0 auto;
}
.arm_template_wrapper_profiletemplate2 .arm_activities_container img.arm_avatar_small_width,
.arm_template_wrapper_profiletemplate2 .arm_member_info_block img.arm_avatar_small_width{
    width: auto;
    min-width: auto;
}
.arm_template_wrapper_profiletemplate2 .arm_activities_container img.arm_avatar_small_height,
.arm_template_wrapper_profiletemplate2 .arm_member_info_block img.arm_avatar_small_height{
    height: auto;
    min-height: auto;
}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_right,
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activity_item_right{padding-top: 8px;}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_right,
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_right *,
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activity_item_right,
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activity_item_right *{
    font-size: 14px;
    color: #7a7d84;
}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_right a,
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activity_item_right a{color: #e89900;}
.arm_template_wrapper_profiletemplate2 .arm_member_listing_container .arm_member_info_right a:hover,
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activity_item_right a:hover{color: #333;}
.arm_template_wrapper_profiletemplate2 .arm_activities_container .arm_activities_pagination_block{text-align: right;}

.arm_template_wrapper_profiletemplate2 .arm_transactions_container{padding: 10px;}

.arm_template_wrapper_profiletemplate2 .arm_profile_form_rtl .arm_cover_upload_container{direction: ltr;left:40px;}
.arm_template_wrapper_profiletemplate2 .social_profile_fields {
    display: block;
    float: none;
}
.arm_template_wrapper_profiletemplate2 .social_profile_fields.arm_mobile_wrapper{
    display:none;
}


.arm_template_wrapper_profiletemplate2 .social_profile_fields .arm_social_prof_div{
    display: inline-block;
}
.arm_template_wrapper_profiletemplate2 .social_profile_fields .arm_social_prof_div > a {
    border-radius: 30px;
    color: #ffffff;
    cursor: pointer;
    display: inline-block;
    font-size: 12px;
    height: 30px;
    line-height: normal;
    margin: 5px 9px 5px 0;
    min-height: 30px;
    min-width: 30px;
    padding: 2px;
    position: relative;
    text-align: center;
    text-transform: lowercase !important;
    vertical-align: middle;
    width: 30px;
}

.arm_template_wrapper_profiletemplate2 .social_profile_fields.arm_mobile .arm_social_prof_div{
    margin:0 5px 5px 0;
}
.arm_template_wrapper_profiletemplate2 .social_profile_fields .arm_social_prof_div > a::before {
    position:relative;
    top:4px !important;
}
.arm_template_wrapper_profiletemplate2 .arm_user_badge_icons_left {
    justify-content: flex-end;
    text-align: right;
}
.arm_template_wrapper_profiletemplate2 .arm_user_badge_icons_left,
.arm_template_wrapper_profiletemplate2 .arm_user_social_icons_right{flex: 1;}

.arm_template_wrapper_profiletemplate2 .arm_user_social_icons_left .social_profile_fields{
    float: right;
    margin: 65px 0 0;
    text-align: right;
    width:100%;
}
.arm_template_wrapper_profiletemplate2 .arm_user_social_icons_right .social_profile_fields{
    float:left;
    text-align: left;
    width:100%;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_field_before_content_wrapper, .arm_template_wrapper_profiletemplate2 .arm_profile_field_after_content_wrapper {padding-left: 40px;}

.arm_template_wrapper_profiletemplate2 .arm_mobile{
    display:none;
    float:left;
    width:100%;
    text-align: center;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_text {
    width: 100%;
    padding: 15px 30px 15px 40px;
    text-align: left;
    float: left;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data{
    display: table-cell;
    padding: 20px 10px 22px 52px;
    border-bottom: 2px solid #CED4DE;
    column-count: 2;
    text-align: left;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl {
    display: table;
    width: 100%;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_row {
    display: table-row;
    column-count: 2;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_body {
    display: table-row-group;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data{
    display: table-cell;
    padding: 20px 10px 22px 40px;
    border-bottom: 2px solid #CED4DE;
    column-count: 2;
    text-align: left;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data.arm_data_value{
    font-weight: 500 !important;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_row:last-child .arm_profile_detail_data{
    border-bottom: none;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_picture_block{
    height:250px;
    width: 100%;
    margin: auto;
    background-size: cover !important;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_field_before_content_wrapper, 
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_field_after_content_wrapper
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_detail_text{
    padding-left: 32px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_detail_tbl .arm_profile_detail_data {
    display: block;
    padding: 8px 10px 8px 30px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_detail_tbl .arm_profile_detail_data:nth-child(odd) {
    border: none;
}
@media (max-width:768px){
    .arm_template_wrapper_profiletemplate2 .arm_user_avatar{
        width:140px !important;
        height:140px !important;
    }
    .arm_template_wrapper_profiletemplate2 .arm_user_avatar img{
        width:130px !important;
        height:130px !important;
    }
}
.arm_template_wrapper_profiletemplate2.mobile .arm_desktop{
    display:none;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_mobile{
    display:block;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_header_top_box{
    position:absolute;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_header_info{
    margin-top:150px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_user_badges_detail{
    margin: 10px 0 0 0;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_tab_detail{
    margin-top:10px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_general_info_container{
    padding:0;
    width:100%;
    max-width: 100%;
}
.arm_template_wrapper_profiletemplate2:not(.mobile) .arm_user_social_icons_all{
    display:none;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_user_social_icons_all{
    display:block;
}
.arm_template_wrapper_profiletemplate2:not(.mobile) .arm_user_badge_icons_all{
    display:none;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_user_badge_icons_all{
    display:block;
}
.arm_template_wrapper_profiletemplate2.arm_desktop .arm_user_badge_icons_all{
    display:none;
}
.arm_template_wrapper_profiletemplate2.mobile .social_profile_fields{
    width: 100%;
}
.arm_template_wrapper_profiletemplate2.mobile .social_profile_fields .arm_social_prof_div{
    width:30px;
    margin-right:5px;
}


@media (max-width:550px){
    .arm_template_wrapper_profiletemplate2 .arm_desktop{
        display:none;
    }
    .arm_template_wrapper_profiletemplate2 .arm_mobile{
        display:block;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_header_top_box{
        position:absolute;
        justify-content: center;
    }
    .arm_template_wrapper_profiletemplate2 .arm_user_badges_detail{
        margin:0;
    }
    .arm_template_wrapper_profiletemplate2 .arm_general_info_container{
        padding:0;
        width:100%;
        max-width: 100%;
    }
    .arm_template_wrapper_profiletemplate2 .arm_user_social_icons_all.arm_mobile{
        display:inline-block;
    }
    .arm_template_wrapper_profiletemplate2 .arm_user_badge_icons_all.arm_mobile {
        display:inline-block; margin: 5px 0 5px 0; 
    }
    .arm_template_wrapper_profiletemplate2 .arm_user_badge_icons_left.arm_desktop {
        display: none;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_detail_wrapper{
        border-radius: 16px;
        -webkit-border-radius: 16px;
        -moz-border-radius: 16px;
        -o-border-radius: 16px;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_picture_block {
        margin: auto;
        width: 100%;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_detail_text {
        padding-left: 32px;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data {
        display: block;
        padding: 8px 10px 8px 32px;
    }
    .arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data:nth-child(odd) {
          border: none;
    }
}


.arm_template_preview_popup.popup_wrapper.arm_tablet_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_avatar{
    width:140px !important;
    height:140px !important;
}
.arm_template_preview_popup.popup_wrapper.arm_tablet_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_avatar img{
    width:130px !important;
    height:130px !important;
}

.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_desktop{
    display:none;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_profile_picture_block{
    height:250px;
}
.arm_template_wrapper_profiletemplate2.mobile .arm_profile_picture_block {
    width: 100%;
    margin: 0;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_mobile{
    display:block;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_profile_header_top_box{
    justify-content: center;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_avatar{
    width:140px !important;
    max-width:140px !important;
    min-width: 140px !important;
    height:140px !important;
    max-height:140px !important;
    min-height: 140px !important;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_avatar img{
    width:130px !important;
    max-width:130px !important;
    min-width: 130px !important;
    height:130px !important;
    max-height:130px !important;
    min-height: 130px !important;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_badges_detail{
    margin:0 0 10px;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .social_profile_fields{
    margin:10px auto;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_profile_tab_detail{
    margin-top:10px;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_general_info_container{
    padding:0;
    width:100%;
    max-width: 100%;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .social_profile_fields{
    min-width: 100% !important;
    max-width: 100% !important;
    width:100% !important;
}
.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .arm_profile_detail_data {
    display: block;padding: 8px 10px 8px 32px;
}

.arm_template_preview_popup.popup_wrapper.arm_mobile_wrapper .arm_template_wrapper_profiletemplate2 .arm_user_last_active_text {margin-bottom: 10px;}
.arm_template_wrapper_profiletemplate2 .arm_user_avatar .arm_cover_upload_container{
    top : 70px;
    left: 30px;
    right: 0;
    top: 55px;
}

.arm_template_wrapper_profiletemplate2 .arm_profile_picture_block .arm_user_avatar:hover .arm_profile{display: block;}

.arm_template_wrapper_profiletemplate2 .arm_delete_profile_popup.arm_confirm_box .arm_confirm_box_arrow{
    margin-right: 15px;
}
.arm_template_wrapper_profiletemplate2 .arm_profile_detail_tbl .hidden_section{
    display: none; 
}
::i-block-chrome,.armCoverUploadBtn{
    height : 29px !important;
}
