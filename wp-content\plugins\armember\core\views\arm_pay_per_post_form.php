<?php
$filter_search = (!empty($_POST['search'])) ? sanitize_text_field($_POST['search']) : '';//phpcs:ignore
if (isset($_REQUEST['arm_default_paid_post_save'])) {
	do_action('arm_save_default_paid_post', $_REQUEST);
}

wp_enqueue_style('arm_post_metaboxes_css', MEMBERSHIP_URL . '/css/arm_post_metaboxes.css', array(), MEMBERSHIP_VERSION);
wp_enqueue_script('arm_tinymce', MEMBERSHIP_URL . '/js/arm_tinymce_member.js', array(), MEMBERSHIP_VERSION);

global $arm_members_activity;
$setact = 0;
global $check_sorting;
$setact = $arm_members_activity->$check_sorting();

global $wpdb, $ARMember, $arm_global_settings;
$user_table = $ARMember->tbl_arm_members;
$user_meta_table = $wpdb->usermeta;

$PaidPostContentTypes = array('page' => esc_html__('Page', 'ARMember'), 'post' => esc_html__('Post', 'ARMember'));
$custom_post_types = get_post_types(array('public' => true, '_builtin' => false, 'show_ui' => true), 'objects');
if (!empty($custom_post_types)) {
	foreach ($custom_post_types as $cpt) {
		$PaidPostContentTypes[$cpt->name] = $cpt->label;
	}
}

$action = isset( $_GET['action'] ) ? sanitize_text_field($_GET['action']) : 'add_paid_post';
$get_page = !empty($_GET['page']) ? sanitize_text_field( $_GET['page'] ) : '';

$post_id = '';
$post_type = '';
$edit_paid_post = false;
if( 'edit_paid_post' == $action ){
	$post_id = isset( $_GET['post_id'] ) ? intval($_GET['post_id']) : '';
	$post_type = get_post_type( $post_id );
}
$get_msg = !empty($_GET['msg']) ? esc_html( sanitize_text_field($_GET['msg'] )) : '';
if( isset( $_GET['status'] ) && 'success' == $_GET['status'] ){
	echo "<script type='text/javascript'>";
		echo "jQuery(document).ready(function(){";
			echo "armToast('" . $get_msg . "','success');"; //phpcs:ignore
			echo "var pageurl = ArmRemoveVariableFromURL( document.URL, 'status' );";  
			echo "pageurl = ArmRemoveVariableFromURL( pageurl, 'msg' );";  
			echo "window.history.pushState( { path: pageurl }, '', pageurl );";
		echo "});";
	echo "</script>";
}

?>

<div class="wrap arm_page arm_paid_posts_main_wrapper">
	<?php
    if ($setact != 1) {
        $admin_css_url = admin_url('admin.php?page=arm_manage_license');
        ?>
        <div style="margin-top:20px;margin-bottom:20px;border-left: 4px solid #ffba00;box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);height:20px;width:99%;padding:10px 0px 10px 10px;background-color:#ffffff;color:#000000;font-size:16px;display:block;visibility:visible;text-align:left;" >ARMember License is not activated. Please activate license from <a href="<?php echo esc_url($admin_css_url); ?>">here</a></div>
    <?php } ?>
	<div class="content_wrapper arm_paid_posts_wrapper arm_position_relative" id="content_wrapper" >
		<div class="page_title">
			<?php
				if( 'edit_paid_post' == $action ){
					esc_html_e('Edit Paid Posts','ARMember');
				} else {
					esc_html_e('Add Paid Posts','ARMember');
				}

				$after_title = "";
				$after_title = apply_filters('arm_filter_after_paid_post_page_title',$after_title);
				echo $after_title; //phpcs:ignore
			?>
			<div class="armclear"></div>
		</div>
		<div class="armclear"></div>
		<?php
			global $arm_pay_per_post_feature;
			$total_paid_post_setups = $arm_pay_per_post_feature->arm_get_paid_post_setup();
			
			if( $total_paid_post_setups < 1 ){

				$arm_setup_link = admin_url( 'admin.php?page=arm_membership_setup&action=new_setup' );
		?>
			<div class="arm_admin_notices_container">
				<p><?php echo sprintf( esc_html__( 'You don\'t have created paid post type membership setup. Please create at least one membership setup for paid post from %s and then reload this page.', 'ARMember' ), '<a href="'.esc_url($arm_setup_link).'">here</a>' ); //phpcs:ignore?> </p>
			</div>
		<?php
			} else {
		?>
			<form method="post" id="arm_add_edit_paid_post_form" class="arm_add_edit_paid_post_form arm_admin_form" novalidate="novalidate">
				<?php
					if( 'edit_paid_post' == $action ){
						echo '<input type="hidden" name="edit_paid_post_id" value="' . esc_attr($post_id) . '" />';
						echo '<input type="hidden" name="edit_paid_post_type" value="' . esc_attr($post_type) . '" />';
					}
					echo '<input type="hidden" name="arm_action" value="arm_add_update_paid_post_plan" />';
					echo '<input type="hidden" name="action" value="' . esc_attr($action) . '" />';
				?>
				<div class="arm_admin_form_content postbox" id="arm_paid_post_metabox_wrapper">
					<table class="form-table">
						<tbody>
							<tr class="form-field form-required">
								<?php
									global $arm_pay_per_post_feature;
									if( 'edit_paid_post' == $_GET['action'] ){
										$postBlankObj = get_post( $post_id );
									} else {
										$postBlankObj = new stdClass();
									}
									$metabox_obj = array();
									$arm_pay_per_post_feature->arm_add_paid_post_metabox_html( $postBlankObj, $metabox_obj, true );
								?>
							</tr>
						</tbody>
					</table>
					<div class="arm_submit_btn_container">
						<button class="arm_save_btn" type="submit"><?php esc_html_e('Save','ARMember'); ?></button>
					</div>
				</div>
				<?php
				
				$after_content = "";
				$after_content = apply_filters('arm_content_filter_after_paid_post_form_content',$after_content);
				echo $after_content; //phpcs:ignore
				
				?>
			</form>
		<?php
			}
		?>
		<?php $wpnonce = wp_create_nonce( 'arm_wp_nonce' );?>
		<input type="hidden" name="arm_wp_nonce" value="<?php echo esc_attr($wpnonce);?>"/>
		<div class="armclear"></div>
	</div>
</div>

<script type="text/javascript">

	jQuery(document).ready(function(){
		if( jQuery("#arm_paid_post_items_input").length > 0 ){
			arm_init_paid_post_autocomplete();
		}
	});

	function arm_init_paid_post_autocomplete(){
		if (jQuery.isFunction(jQuery().autocomplete)){
			jQuery('#arm_paid_post_items_input').autocomplete({
				minLength: 0,
				delay: 500,
				appendTo: "#arm_paid_post_items_list_container",
				source: function (request, response) {
					var post_type = jQuery('#arm_add_paid_post_item_type').val();
					var _wpnonce = jQuery('input[name="arm_wp_nonce"]').val();
					jQuery.ajax({
						type: "POST",
						url: ajaxurl,
						dataType: 'json',
						data: "action=arm_get_paid_post_item_options&arm_post_type=" + post_type + "&search_key="+request.term + "&_wpnonce=" + _wpnonce,
						beforeSend: function () {},
						success: function (res) {
							response(res.data);
						}
					});
				},
				focus: function() {return false;},
				select: function(event, ui) {
					var itemData = ui.item;
					jQuery("#arm_paid_post_items_input").val('');
					if(jQuery('#arm_paid_post_items .arm_paid_post_itembox_'+itemData.id).length > 0) {
					} else {
						var itemHtml = '<div class="arm_paid_post_itembox arm_paid_post_itembox_'+itemData.id+'">';
						itemHtml += '<input type="hidden" name="arm_paid_post_item_id['+itemData.id+']" value="'+itemData.id+'"/>';
						itemHtml += '<label>'+itemData.label+'<span class="arm_remove_selected_itembox">x</span></label>';
						itemHtml += '</div>';
						jQuery("#arm_paid_post_items").append(itemHtml);
						jQuery('#arm_paid_post_items_input').removeAttr('required');
					}
					jQuery('#arm_paid_post_items').show();
					return false;
				},
			}).data('uiAutocomplete')._renderItem = function (ul, item) {
				var itemClass = 'ui-menu-item';
				if(jQuery('#arm_paid_post_items .arm_paid_post_itembox_'+item.id).length > 0) {
					itemClass += ' ui-menu-item-selected';
				}
				var itemHtml = '<li class="'+itemClass+'" data-value="'+item.value+'" data-id="'+item.id+'" ><a>' + item.label + '</a></li>';
				return jQuery(itemHtml).appendTo(ul);
			};
		}
	}

	jQuery(document).on('click', '.arm_remove_selected_itembox', function () {
		jQuery(this).parents('.arm_paid_post_itembox').remove();
		if(jQuery('#arm_paid_post_items .arm_paid_post_itembox').length == 0) {
			jQuery('#arm_paid_post_items_input').attr('required', 'required');
			jQuery('#arm_paid_post_items').hide();
		}
		return false;
	});	
</script>
<?php
	echo $ARMember->arm_get_need_help_html_content('paid-posts-list-add'); //phpcs:ignore
?>