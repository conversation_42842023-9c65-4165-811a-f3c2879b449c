<div class="box_links">
    <?php if(doo_here_links($post->ID)){ ?>
    <div class="linktabs">
        <h2><?php _d('Links'); ?></h2>
        <ul class="idTabs">
            <?php // Menu Link types
            if(doo_here_type_links($post->ID, __d('Download'))) echo '<li><a href="#download">'. __d('Download'). '</a></li>';
            if(doo_here_type_links($post->ID, __d('Torrent'))) echo '<li><a href="#torrent">'. __d('Torrent'). '</a></li>';
            if(doo_here_type_links($post->ID, __d('Watch online'))) echo '<li><a href="#videos">'. __d('Watch online'). '</a></li>';
            if(doo_here_type_links($post->ID, __d('Rent or Buy'))) echo '<li><a href="#buy">'. __d('Rent or Buy'). '</a></li>';
            ?>
        </ul>
    </div>
    
    <style>
    .download-links-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        background: #fff;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        border: 1px solid rgba(255,255,255,0.2);
        table-layout: fixed;
    }
    .download-links-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 18px 12px;
        text-align: center;
        font-weight: 700;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }
    .download-links-table td {
        padding: 20px 12px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        vertical-align: middle;
        font-size: 14px;
        text-align: center;
        background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
    }
    .download-links-table tr:hover td {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .download-btn, .play-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px 12px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        font-size: 11px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        margin: 2px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }
    .download-btn::before, .play-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }
    .download-btn:hover::before, .play-btn:hover::before {
        left: 100%;
    }
    .download-btn {
        background: linear-gradient(135deg, #4CAF50, #45a049, #2e7d32);
        color: white;
        border: 2px solid transparent;
    }
    .download-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        border-color: #4CAF50;
    }
    .play-btn {
        background: linear-gradient(135deg, #2196F3, #1976D2, #0D47A1);
        color: white;
        border: 2px solid transparent;
    }
    .play-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
        border-color: #2196F3;
    }
    .quality-badge {
        background: linear-gradient(135deg, #FF5722, #E64A19, #BF360C);
        color: white;
        padding: 8px 12px;
        border-radius: 25px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
        box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
        letter-spacing: 0.5px;
    }
    .language-flag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 12px;
        background: linear-gradient(135deg, #9C27B0, #7B1FA2, #4A148C);
        color: white;
        padding: 8px 12px;
        border-radius: 25px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        letter-spacing: 0.5px;
    }
    .size-info {
        background: linear-gradient(135deg, #FF9800, #F57C00, #E65100);
        color: white;
        padding: 8px 14px;
        border-radius: 25px;
        font-weight: 700;
        font-size: 13px;
        box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        letter-spacing: 0.5px;
        min-width: 60px;
        word-wrap: break-word;
    }
    
    /* Column widths - Dynamic based on episode links presence */
    .download-links-table.has-episodes th:nth-child(1) { width: 15%; } /* Episode */
    .download-links-table.has-episodes th:nth-child(2) { width: 15%; } /* Quality */
    .download-links-table.has-episodes th:nth-child(3) { width: 15%; } /* Language */
    .download-links-table.has-episodes th:nth-child(4) { width: 20%; } /* Size */
    .download-links-table.has-episodes th:nth-child(5) { width: 35%; } /* Actions */

    /* Column widths - No episode links */
    .download-links-table:not(.has-episodes) th:nth-child(1) { width: 20%; } /* Quality */
    .download-links-table:not(.has-episodes) th:nth-child(2) { width: 20%; } /* Language */
    .download-links-table:not(.has-episodes) th:nth-child(3) { width: 25%; } /* Size */
    .download-links-table:not(.has-episodes) th:nth-child(4) { width: 35%; } /* Actions */
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .download-links-table {
            font-size: 12px;
            margin: 10px 0;
            border-radius: 10px;
        }
        .download-links-table th,
        .download-links-table td {
            padding: 8px 4px;
        }
        .download-btn, .play-btn {
            padding: 6px 8px;
            font-size: 10px;
            gap: 4px;
            margin: 1px;
        }
        .quality-badge, .language-flag {
            font-size: 9px;
            padding: 5px 8px;
        }
        .size-info {
            font-size: 11px;
            padding: 6px 10px;
        }
    }
    
    @media (max-width: 480px) {
        .box_links {
            margin: 0;
            padding: 0;
        }
        .download-links-table {
            margin: 5px 0;
            border-radius: 8px;
        }
        .download-links-table th:nth-child(1) { width: 15%; } /* Episode */
        .download-links-table th:nth-child(2) { width: 15%; } /* Quality */
        .download-links-table th:nth-child(3) { width: 15%; } /* Language */
        .download-links-table th:nth-child(4) { width: 20%; } /* Size */
        .download-links-table th:nth-child(5) { width: 35%; } /* Actions */
        
        .download-btn, .play-btn {
            display: block;
            text-align: center;
            margin: 2px 0;
            width: 100%;
            padding: 6px 2px;
            font-size: 9px;
        }
        .download-links-table th,
        .download-links-table td {
            padding: 6px 2px;
            font-size: 10px;
        }
        .quality-badge, .language-flag {
            font-size: 8px;
            padding: 4px 6px;
        }
        .size-info {
            font-size: 9px;
            padding: 4px 6px;
        }
    }

    /* Premium Badge and Button Styles */
    .premium-badge {
        animation: glow 2s ease-in-out infinite alternate;
    }
    @keyframes glow {
        from { box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4); }
        to { box-shadow: 0 4px 16px rgba(255, 215, 0, 0.8); }
    }
    .premium-badge-locked {
        animation: pulse 2s ease-in-out infinite alternate;
    }
    @keyframes pulse {
        from { box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4); }
        to { box-shadow: 0 4px 16px rgba(220, 53, 69, 0.8); }
    }
    .premium-locked:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
        border-color: #6c757d !important;
    }
    </style>
    
    <?php
    // Function to check if user has premium membership
    function is_user_premium() {
        if (!is_user_logged_in()) {
            return false;
        }

        $current_user_id = get_current_user_id();

        // Check if ARMember plugin is active
        if (function_exists('arm_is_member_active') && function_exists('get_user_meta')) {
            // Get user's plan IDs
            $arm_user_plan_ids = get_user_meta($current_user_id, 'arm_user_plan_ids', true);

            if (!empty($arm_user_plan_ids) && is_array($arm_user_plan_ids)) {
                // Check if user has active membership
                if (arm_is_member_active($current_user_id)) {
                    // Check for suspended plans
                    $suspended_plan_ids = get_user_meta($current_user_id, 'arm_user_suspended_plan_ids', true);
                    $suspended_plan_ids = !empty($suspended_plan_ids) ? $suspended_plan_ids : array();

                    // Check if user has any active non-suspended plan
                    foreach ($arm_user_plan_ids as $plan_id) {
                        if (!in_array($plan_id, $suspended_plan_ids)) {
                            // Check if plan is not expired
                            $planData = get_user_meta($current_user_id, 'arm_user_plan_' . $plan_id, true);
                            if (!empty($planData)) {
                                $expire_time = isset($planData['arm_expire_plan']) ? $planData['arm_expire_plan'] : 0;
                                $now_time = strtotime(current_time('mysql'));

                                // If plan has no expiry or not expired yet
                                if (empty($expire_time) || $now_time < $expire_time) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    // Custom Download Links Table - Replace default
    if(doo_here_type_links($post->ID, __d('Download'))) {
        echo '<div id="download" class="tabcontent">';

        // Check if user is premium and show notification
        $is_premium = is_user_premium();
        if ($is_premium) {
            echo '<div class="premium-notification" style="background: linear-gradient(135deg, #FFD700, #FFA500); color: #000; padding: 15px; border-radius: 10px; margin-bottom: 20px; text-align: center; font-weight: bold; box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);">';
            echo '<i class="fas fa-crown" style="margin-right: 8px; color: #FF8C00;"></i>';
            echo 'Premium Member: You have access to direct download links without any delay + Ad-Free Experience!';
            echo '<i class="fas fa-star" style="margin-left: 8px; color: #FF8C00;"></i>';
            echo '</div>';
        }

        // First, get download links
        $links = get_posts(array(
            'post_type' => 'dt_links',
            'post_parent' => $post->ID,
            'meta_query' => array(
                array(
                    'key' => '_dool_type',
                    'value' => __d('Download')
                )
            ),
            'posts_per_page' => -1
        ));

        // Check if any links are episode links
        $has_episode_links = false;
        if($links && is_array($links)) {
            foreach($links as $link) {
                $is_episode_link = get_post_meta($link->ID, '_is_episode_link', true);
                if($is_episode_link) {
                    $has_episode_links = true;
                    break;
                }
            }
        }

        $table_class = $has_episode_links ? 'download-links-table has-episodes' : 'download-links-table';
        echo '<table class="' . $table_class . '">';
        echo '<thead><tr>';
        if($has_episode_links) {
            echo '<th>Episode</th>';
        }
        echo '<th>Quality</th>';
        echo '<th>Language</th>';
        echo '<th>Size</th>';
        echo '<th>Actions</th>';
        echo '</tr></thead><tbody>';

        if($links && is_array($links)) {
            foreach($links as $link) {
            $link_url = get_post_meta($link->ID, '_dool_url', true);
            $quality = get_post_meta($link->ID, '_dool_quality', true);
            $language = get_post_meta($link->ID, '_dool_lang', true);
            $size = get_post_meta($link->ID, '_dool_size', true);
            $episode_title = $link->post_title;
            $is_episode_link = get_post_meta($link->ID, '_is_episode_link', true);

            echo '<tr>';

            // Only show episode column if there are episode links in this post
            if($has_episode_links) {
                if($is_episode_link && $episode_title && strpos($episode_title, 'EP-') === 0) {
                    echo '<td><span class="episode-badge" style="background: linear-gradient(135deg, #673AB7, #512DA8, #311B92); color: white; padding: 8px 12px; border-radius: 25px; font-size: 12px; font-weight: bold; text-transform: uppercase; box-shadow: 0 4px 15px rgba(103, 58, 183, 0.3); letter-spacing: 0.5px;">' . $episode_title . '</span></td>';
                } else {
                    echo '<td><span class="link-type-badge" style="background: linear-gradient(135deg, #2196F3, #1976D2, #0D47A1); color: white; padding: 8px 12px; border-radius: 25px; font-size: 12px; font-weight: bold; text-transform: uppercase; box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3); letter-spacing: 0.5px;">DOWNLOAD</span></td>';
                }
            }

            // Check if this specific link is marked as premium
            $is_link_premium = get_post_meta($link->ID, '_is_premium_link', true);

            // Debug: For testing, let's mark some links as premium based on quality
            // Remove this after testing
            if (!$is_link_premium && in_array($quality, ['4K', '1080p', 'HD'])) {
                $is_link_premium = '1'; // Temporarily mark high quality as premium for testing
            }

            // Only show premium badge if this link is specifically marked as premium
            $premium_badge = '';
            if ($is_link_premium == '1') {
                if ($is_premium) {
                    // User is premium - show unlocked badge
                    $premium_badge = '<span class="premium-badge" style="background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00); color: #000; padding: 4px 8px; border-radius: 15px; font-size: 10px; font-weight: bold; text-transform: uppercase; margin-left: 8px; box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4); letter-spacing: 0.5px; animation: glow 2s ease-in-out infinite alternate;">PREMIUM</span>';
                } else {
                    // User is not premium - show locked badge
                    $premium_badge = '<span class="premium-badge-locked" style="background: linear-gradient(135deg, #dc3545, #c82333, #bd2130); color: white; padding: 4px 8px; border-radius: 15px; font-size: 10px; font-weight: bold; text-transform: uppercase; margin-left: 8px; box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4); letter-spacing: 0.5px; position: relative;"><i class="fas fa-lock" style="margin-right: 4px; font-size: 8px;"></i>PREMIUM</span>';
                }
            }

            echo '<td><span class="quality-badge">' . ($quality ?: 'HD') . '</span>' . $premium_badge . '</td>';
            echo '<td><span class="language-flag">' . ($language ?: 'EN') . '</span></td>';
            echo '<td><span class="size-info">' . ($size ?: '----') . '</span></td>';
            echo '<td>';

            // Download button logic based on link type and user status
            if ($is_link_premium == '1') {
                // This is a premium link
                if ($is_premium) {
                    // Premium user accessing premium link - direct download
                    echo '<a href="' . esc_url($link_url) . '" class="download-btn premium-download" target="_blank" rel="nofollow">';
                    echo '<i class="fas fa-download"></i> Direct Download';
                    echo '</a>';
                } else {
                    // Regular user accessing premium link - show locked message
                    echo '<a href="#" class="download-btn premium-locked" onclick="alert(\'This is a Premium link! Upgrade your membership to access direct downloads without delay.\'); return false;" style="background: linear-gradient(135deg, #6c757d, #5a6268, #495057) !important; position: relative;">';
                    echo '<i class="fas fa-lock" style="margin-right: 4px; font-size: 10px;"></i> Premium Only';
                    echo '</a>';
                }
            } else {
                // This is a regular link - all users get same treatment
                if ($is_premium) {
                    // Premium user accessing regular link - still gets direct download benefit
                    echo '<a href="' . esc_url($link_url) . '" class="download-btn" target="_blank" rel="nofollow">';
                    echo '<i class="fas fa-download"></i> Direct Download';
                    echo '</a>';
                } else {
                    // Regular user accessing regular link - normal short link process
                    echo '<a href="' . get_permalink($link->ID) . '" class="download-btn" target="_blank">';
                    echo '<i class="fas fa-download"></i> Download';
                    echo '</a>';
                }
            }

            echo '<a href="#" class="play-btn" onclick="playInBuiltPlayer(\'' . $link_url . '\', \'' . get_the_title($post->ID) . '\'); return false;">';
            echo '<i class="fas fa-play"></i> Play Now';
            echo '</a>';
            echo '</td>';
            echo '</tr>';
            }
        }

        echo '</tbody></table>';
        echo '</div>';
    } else {
        // Use default design for other types
        DooLinks::tablelist_front($post->ID, __d('Torrent'), 'torrent');
        DooLinks::tablelist_front($post->ID, __d('Watch online'), 'videos');
        DooLinks::tablelist_front($post->ID, __d('Rent or Buy'), 'buy');
    }
    ?>
    
    <script>
    function playInBuiltPlayer(url, title) {
        // Check for saved progress
        const savedTime = localStorage.getItem('video_progress_' + btoa(url));
        
        // Create player modal
        const modal = document.createElement('div');
        modal.id = 'video-player-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.95);
            z-index: 99999;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        `;
        
        modal.innerHTML = `
            <div style="width: 100%; max-width: 90%; height: 85%; background: #000; border-radius: 12px; overflow: hidden; position: relative;">
                <!-- Header -->
                <div id="player-header" style="position: absolute; top: 0; left: 0; right: 0; background: linear-gradient(to bottom, rgba(0,0,0,0.8), transparent); padding: 15px 10px; z-index: 100; display: flex; justify-content: space-between; align-items: center; transition: opacity 0.3s;">
                    <h3 style="color: white; margin: 0; font-size: 16px; max-width: 70%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${title}</h3>
                    <button onclick="closeVideoPlayer()" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 35px; height: 35px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- Resume Notification -->
                ${savedTime ? `
                <div id="resume-popup" style="position: absolute; top: 60px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.9); color: white; padding: 15px; border-radius: 8px; z-index: 101; text-align: center; max-width: 90%; box-sizing: border-box;">
                    <p style="margin: 0 0 10px 0; font-size: 14px;">Resume from ${formatVideoTime(savedTime)}?</p>
                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                        <button onclick="resumeFromSaved(${savedTime})" style="background: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;">Resume</button>
                        <button onclick="startFromBeginning()" style="background: #f44336; color: white; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;">Start Over</button>
                    </div>
                </div>
                ` : ''}
                
                <!-- Video Element -->
                <video id="main-video" 
                       style="width: 100%; height: 100%; object-fit: contain;" 
                       autoplay 
                       preload="metadata"
                       playsinline
                       webkit-playsinline>
                    <source src="${url}" type="video/mp4">
                    <source src="${url}" type="video/webm">
                    <source src="${url}" type="video/ogg">
                    Your browser does not support the video tag.
                </video>
                
                <!-- Center Play Controls -->
                <div id="center-controls" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); display: flex; align-items: center; gap: 20px; opacity: 0; transition: opacity 0.3s; pointer-events: none;">
                    <button onclick="skipTime(-10)" style="background: rgba(0,0,0,0.7); border: none; color: white; width: 50px; height: 50px; border-radius: 50%; cursor: pointer; display: flex; flex-direction: column; align-items: center; justify-content: center; pointer-events: all; font-size: 12px;">
                        <i class="fas fa-backward"></i>
                        <small style="font-size: 8px; margin-top: 2px;">10s</small>
                    </button>
                    
                    <button id="main-play-btn" onclick="toggleVideoPlay()" style="background: rgba(0,0,0,0.7); border: none; color: white; width: 60px; height: 60px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 20px; pointer-events: all;">
                        <i class="fas fa-pause"></i>
                    </button>
                    
                    <button onclick="skipTime(10)" style="background: rgba(0,0,0,0.7); border: none; color: white; width: 50px; height: 50px; border-radius: 50%; cursor: pointer; display: flex; flex-direction: column; align-items: center; justify-content: center; pointer-events: all; font-size: 12px;">
                        <i class="fas fa-forward"></i>
                        <small style="font-size: 8px; margin-top: 2px;">10s</small>
                    </button>
                </div>
                
                <!-- Bottom Controls -->
                <div id="bottom-controls" style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(to top, rgba(0,0,0,0.8), transparent); padding: 15px 10px 10px; z-index: 100; transition: opacity 0.3s;">
                    <!-- Progress Bar -->
                    <div style="margin-bottom: 10px;">
                        <div onclick="seekToPosition(event)" style="width: 100%; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; cursor: pointer; position: relative;">
                            <div id="progress-bar" style="height: 100%; background: #ff6b6b; border-radius: 2px; width: 0%; position: relative;">
                                <div id="progress-handle" style="position: absolute; right: -6px; top: -4px; width: 12px; height: 12px; background: #ff6b6b; border-radius: 50%; cursor: pointer;"></div>
                            </div>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-top: 5px; font-size: 11px; color: white;">
                            <span id="current-time">0:00</span>
                            <span id="total-time">0:00</span>
                        </div>
                    </div>
                    
                    <!-- Control Buttons -->
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <!-- Left Controls -->
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button onclick="toggleVideoPlay()" style="background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 6px;">
                                <i id="play-pause-icon" class="fas fa-pause"></i>
                            </button>
                            <button onclick="skipTime(-10)" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px;">
                                <i class="fas fa-backward"></i>
                            </button>
                            <button onclick="skipTime(10)" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px;">
                                <i class="fas fa-forward"></i>
                            </button>
                        </div>
                        
                        <!-- Right Controls -->
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button onclick="toggleMute()" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px;">
                                <i id="volume-icon" class="fas fa-volume-up"></i>
                            </button>
                            <div style="display: flex; align-items: center; gap: 5px;">
                                <input type="range" id="volume-slider" min="0" max="100" value="100" style="width: 60px; height: 4px;" onchange="changeVolume(this.value)">
                            </div>
                            <button onclick="openInExternalPlayer('${url}')" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px;">
                                <i class="fas fa-external-link-alt"></i>
                            </button>
                            <button onclick="toggleVideoFullscreen()" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px;">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button onclick="requestLandscape()" style="background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 6px; display: ${/Mobi|Android/i.test(navigator.userAgent) ? 'block' : 'none'};">
                                <i class="fas fa-mobile-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Initialize player
        const video = document.getElementById('main-video');
        const playBtn = document.getElementById('main-play-btn');
        const playPauseIcon = document.getElementById('play-pause-icon');
        const header = document.getElementById('player-header');
        const centerControls = document.getElementById('center-controls');
        const bottomControls = document.getElementById('bottom-controls');
        const progressBar = document.getElementById('progress-bar');
        const progressHandle = document.getElementById('progress-handle');
        const currentTimeSpan = document.getElementById('current-time');
        const totalTimeSpan = document.getElementById('total-time');
        const volumeIcon = document.getElementById('volume-icon');
        const volumeSlider = document.getElementById('volume-slider');
        
        let hideTimeout;
        let isDragging = false;
        
        // Mobile-specific adjustments
        const isMobile = /Mobi|Android/i.test(navigator.userAgent);
        if (isMobile) {
            // Adjust modal for mobile - make it fullscreen
            modal.style.padding = '0';
            modal.style.borderRadius = '0';
            
            // Make video container fullscreen on mobile
            const videoContainer = modal.querySelector('div');
            videoContainer.style.width = '100%';
            videoContainer.style.height = '100%';
            videoContainer.style.maxWidth = '100%';
            videoContainer.style.borderRadius = '0';
            
            // Make controls larger for touch
            centerControls.style.gap = '30px';
            
            // Adjust header for mobile
            header.style.padding = '10px 15px';
            
            // Auto-hide controls faster on mobile
            hideTimeout = setTimeout(() => {
                if (!video.paused && !isDragging) {
                    header.style.opacity = '0';
                    centerControls.style.opacity = '0';
                    bottomControls.style.opacity = '0';
                }
            }, 2000);
        }
        
        // Touch events for mobile
        let touchStartY = 0;
        let touchStartX = 0;
        
        modal.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
            touchStartX = e.touches[0].clientX;
            showControls();
        });
        
        modal.addEventListener('touchmove', (e) => {
            e.preventDefault(); // Prevent scrolling
        });
        
        modal.addEventListener('touchend', (e) => {
            const touchEndY = e.changedTouches[0].clientY;
            const touchEndX = e.changedTouches[0].clientX;
            const diffY = touchStartY - touchEndY;
            const diffX = touchStartX - touchEndX;
            
            // Swipe gestures
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        skipTime(10); // Swipe left = forward
                    } else {
                        skipTime(-10); // Swipe right = backward
                    }
                }
            }
        });
        
        // Show/Hide controls
        function showControls() {
            header.style.opacity = '1';
            centerControls.style.opacity = '1';
            bottomControls.style.opacity = '1';
            clearTimeout(hideTimeout);
            hideTimeout = setTimeout(() => {
                if (!video.paused && !isDragging) {
                    header.style.opacity = '0';
                    centerControls.style.opacity = '0';
                    bottomControls.style.opacity = '0';
                }
            }, 3000);
        }
        
        // Update progress bar
        function updateProgress() {
            if (!isDragging && video.duration) {
                const progress = (video.currentTime / video.duration) * 100;
                progressBar.style.width = progress + '%';
                progressHandle.style.left = progress + '%';
                
                currentTimeSpan.textContent = formatVideoTime(video.currentTime);
                totalTimeSpan.textContent = formatVideoTime(video.duration);
            }
        }
        
        // Seek to position
        window.seekToPosition = function(event) {
            const rect = event.currentTarget.getBoundingClientRect();
            const pos = (event.clientX - rect.left) / rect.width;
            video.currentTime = pos * video.duration;
        }
        
        // Change volume
        window.changeVolume = function(value) {
            video.volume = value / 100;
            if (value == 0) {
                volumeIcon.className = 'fas fa-volume-mute';
            } else if (value < 50) {
                volumeIcon.className = 'fas fa-volume-down';
            } else {
                volumeIcon.className = 'fas fa-volume-up';
            }
        }
        
        // Toggle mute
        window.toggleMute = function() {
            if (video.muted) {
                video.muted = false;
                volumeSlider.value = video.volume * 100;
                volumeIcon.className = 'fas fa-volume-up';
            } else {
                video.muted = true;
                volumeIcon.className = 'fas fa-volume-mute';
            }
        }
        
        // Request landscape mode
        window.requestLandscape = function() {
            if (screen.orientation && screen.orientation.lock) {
                screen.orientation.lock('landscape').catch(() => {
                    alert('Please rotate your device to landscape mode for better viewing');
                });
            } else {
                alert('Please rotate your device to landscape mode for better viewing');
            }
        }
        
        // Event listeners
        modal.addEventListener('mousemove', showControls);
        modal.addEventListener('click', showControls);
        
        video.addEventListener('loadedmetadata', () => {
            totalTimeSpan.textContent = formatVideoTime(video.duration);
        });
        
        video.addEventListener('timeupdate', updateProgress);
        
        video.addEventListener('play', () => {
            playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            playPauseIcon.className = 'fas fa-pause';
        });
        
        video.addEventListener('pause', () => {
            playBtn.innerHTML = '<i class="fas fa-play"></i>';
            playPauseIcon.className = 'fas fa-play';
            showControls();
        });
        
        // Save progress every 5 seconds after 30 seconds
        video.addEventListener('timeupdate', () => {
            if (video.currentTime > 30 && Math.floor(video.currentTime) % 5 === 0) {
                localStorage.setItem('video_progress_' + btoa(url), video.currentTime);
            }
        });
        
        video.addEventListener('ended', () => {
            localStorage.removeItem('video_progress_' + btoa(url));
            // Unlock orientation when video ends
            if (screen.orientation && screen.orientation.unlock) {
                screen.orientation.unlock();
            }
        });
        
        // Keyboard controls
        document.addEventListener('keydown', handleVideoKeys);
        
        // Show controls initially
        showControls();
        
        return false;
    }
    
    // Helper functions
    function formatVideoTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return mins + ':' + (secs < 10 ? '0' : '') + secs;
    }
    
    function resumeFromSaved(time) {
        document.getElementById('main-video').currentTime = time;
        document.getElementById('resume-popup').style.display = 'none';
    }
    
    function startFromBeginning() {
        document.getElementById('main-video').currentTime = 0;
        document.getElementById('resume-popup').style.display = 'none';
    }
    
    function toggleVideoPlay() {
        const video = document.getElementById('main-video');
        if (video.paused) {
            video.play();
        } else {
            video.pause();
        }
    }
    
    function skipTime(seconds) {
        const video = document.getElementById('main-video');
        video.currentTime += seconds;
    }
    
    function toggleVideoFullscreen() {
        const video = document.getElementById('main-video');
        const modal = document.getElementById('video-player-modal');
        
        if (/Mobi|Android/i.test(navigator.userAgent)) {
            // Mobile: Request fullscreen and landscape
            if (!document.fullscreenElement) {
                modal.requestFullscreen().then(() => {
                    // After entering fullscreen, try to lock to landscape
                    if (screen.orientation && screen.orientation.lock) {
                        screen.orientation.lock('landscape').catch(() => {
                            console.log('Landscape lock not supported');
                        });
                    }
                }).catch(() => {
                    // Fallback: just try landscape without fullscreen
                    if (screen.orientation && screen.orientation.lock) {
                        screen.orientation.lock('landscape').catch(() => {
                            console.log('Landscape lock failed');
                        });
                    }
                });
            } else {
                document.exitFullscreen().then(() => {
                    // Unlock orientation when exiting fullscreen
                    if (screen.orientation && screen.orientation.unlock) {
                        screen.orientation.unlock();
                    }
                });
            }
        } else {
            // Desktop fullscreen
            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.mozRequestFullScreen) {
                video.mozRequestFullScreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }
    }
    
    // Add separate landscape function
    function requestLandscape() {
        if (/Mobi|Android/i.test(navigator.userAgent)) {
            if (screen.orientation && screen.orientation.lock) {
                screen.orientation.lock('landscape-primary').catch(() => {
                    screen.orientation.lock('landscape').catch(() => {
                        // Try alternative method
                        if (screen.lockOrientation) {
                            screen.lockOrientation('landscape');
                        } else if (screen.mozLockOrientation) {
                            screen.mozLockOrientation('landscape');
                        } else if (screen.msLockOrientation) {
                            screen.msLockOrientation('landscape');
                        }
                    });
                });
            }
        }
    }
    
    function handleVideoKeys(e) {
        if (!document.getElementById('video-player-modal')) return;
        
        const video = document.getElementById('main-video');
        
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                toggleVideoPlay();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                skipTime(-10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                skipTime(10);
                break;
            case 'KeyF':
                e.preventDefault();
                toggleVideoFullscreen();
                break;
            case 'Escape':
                closeVideoPlayer();
                break;
            case 'ArrowUp':
                e.preventDefault();
                video.volume = Math.min(1, video.volume + 0.1);
                document.getElementById('volume-slider').value = video.volume * 100;
                break;
            case 'ArrowDown':
                e.preventDefault();
                video.volume = Math.max(0, video.volume - 0.1);
                document.getElementById('volume-slider').value = video.volume * 100;
                break;
        }
    }
    
    function closeVideoPlayer() {
        const modal = document.getElementById('video-player-modal');
        if (modal) {
            document.removeEventListener('keydown', handleVideoKeys);
            // Unlock orientation when closing
            if (screen.orientation && screen.orientation.unlock) {
                screen.orientation.unlock();
            }
            modal.remove();
        }
    }
    
    function openInExternalPlayer(url) {
        const popup = document.createElement('div');
        popup.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        popup.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 400px; width: 90%;">
                <h3 style="margin-top: 0; text-align: center;">Open in External Player</h3>
                <div style="display: flex; flex-direction: column; gap: 12px;">
                    <button onclick="window.location.href='vlc://${url}'" style="padding: 15px; background: #ff8c00; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-play"></i> VLC Player
                    </button>
                    <button onclick="window.location.href='mpc-hc://${url}'" style="padding: 15px; background: #4CAF50; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-play"></i> MPC-HC
                    </button>
                    <button onclick="window.location.href='potplayer://${url}'" style="padding: 15px; background: #2196F3; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-play"></i> PotPlayer
                    </button>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="margin-top: 20px; padding: 12px; background: #f44336; color: white; border: none; border-radius: 6px; cursor: pointer; width: 100%;">
                    Close
                </button>
            </div>
        `;
        
        document.body.appendChild(popup);
    }
    </script>
    
    <?php } ?>
</div>
