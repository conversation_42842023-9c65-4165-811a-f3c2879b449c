"use strict";var ARMemberElementor=window.ARMemberElementor||function(e,t){var n={},o={init:function(){if("undefined"==typeof elementor)return!1;t(e).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/arm-element-shortcode.default",o.WidgetARMHandler),elementor.hooks.addAction("panel/open_editor/widget/arm-element-shortcode",o.widgetPanelOpen)})},WidgetARMHandler:function(e){},disableEvents:function(e){return e.preventDefault(),e.stopImmediatePropagation(),!1},widgetPanelOpen:function(e,t){n.widgetId=t.attributes.id,n.formId=t.attributes.settings.attributes.arm_shortcode_select,o.widgetPanelInit(e),o.widgetPanelObserver.init(e)},widgetPanelInit:function(e){e.$el.find(".elementor-control.elementor-control-arm_shortcode_select").find("select");e.$el.find(".elementor-control.elementor-control-arm_shortcode_select").on("change","select",function(){let e=this.value;var t=e=e.replace(/(id=)/,"");console.log(t)})},widgetPanelObserver:{init:function(e){var t;n.observerWidgetId!==n.widgetId&&(void 0!==n.observer&&"function"==typeof n.observer.disconnect&&n.observer.disconnect(),t={targetNode:e.$el.find("#elementor-panel-content-wrapper")[0],config:{childList:!0,subtree:!0,attributes:!0}},o.widgetPanelObserver.panel=e,t.observer=new MutationObserver(o.widgetPanelObserver.callback),t.observer.observe(t.targetNode,t.config),n.observerWidgetId=n.widgetId,n.observer=t.observer)},callback:function(e){var t,n,r=!1;for(n in e)if("childList"===(t=e[n]).type&&0<t.addedNodes.length&&(r=o.widgetPanelObserver.callbackMutationChildList(t)),r="attributes"===t.type?o.widgetPanelObserver.callbackMutationAttributes(t):r)return},callbackMutationChildList:function(e){var t,n,r=e.addedNodes||[];for(n in r)if((t=r[n])&&t.classList&&t.classList.contains("elementor-control-section_content"))return o.widgetPanelInit(o.widgetPanelObserver.panel),!0;return!1},callbackMutationAttributes:function(e){return!!(e.target&&e.target.classList&&e.target.classList.contains("elementor-tab-control-content"))&&(o.widgetPanelInit(o.widgetPanelObserver.panel),!0)}}};return o}((document,window),jQuery);ARMemberElementor.init();