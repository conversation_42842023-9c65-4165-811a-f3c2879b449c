function popupResetOptions(){jQuery(".arm_shortcode_list_container").removeClass("arm_hide_block"),jQuery(".arm_shortcode_opt_form_container").addClass("arm_hide_block"),jQuery(".arm_shortcode_opt_form_container").find("#arm_form_id_input").val("")}function arm_open_form_shortcode_popup(){popupResetOptions(),jQuery.isFunction(jQuery().bPopup)&&jQuery("#arm_form_shortcode_options_popup_wrapper").bPopup({opacity:.5,closeClass:"popup_close_btn",follow:[!1,!1]});var e=jQuery(window).height()-265;jQuery("#arm_form_shortcode_options_popup_wrapper").find(".arm_tabgroup_content_wrapper").css("max-height",e),arm_selectbox_init()}function arm_open_restriction_shortcode_popup(){popupResetOptions(),jQuery.isFunction(jQuery().bPopup)&&jQuery("#arm_restriction_shortcode_options_popup_wrapper").bPopup({opacity:.5,closeClass:"popup_close_btn",follow:[!1,!1]}),arm_selectbox_init()}function armICheckInit(){jQuery.isFunction(jQuery().iCheck)&&(jQuery(".arm_icheckbox").iCheck({checkboxClass:"icheckbox_minimal-red",radioClass:"iradio_minimal-red",increaseArea:"20%",disabledClass:""}),jQuery(".arm_icheckbox").on("ifChanged",function(e){jQuery(this).trigger("change")}),jQuery(".arm_icheckbox").on("ifClicked",function(e){jQuery(this).trigger("click")}),jQuery(".arm_iradio").iCheck({checkboxClass:"icheckbox_minimal-red",radioClass:"iradio_minimal-red",increaseArea:"20%"}),jQuery(".arm_iradio").on("ifChanged",function(e){jQuery(this).trigger("change")}),jQuery(".arm_iradio").on("ifClicked",function(e){setTimeout(function(){jQuery(this).trigger("click")},1)}))}function arm_selectbox_init(){jQuery(".arm_selectbox").each(function(){var _=jQuery(this),e=_.find("dd ul"),i=e.attr("data-id"),t=jQuery("#"+i).val();e.find("li").each(function(){var e=jQuery(this).text(),r=jQuery(this).attr("data-value"),a=jQuery(this).attr("data-type");r==t&&(_.find("dt span").text(e),_.find("dt input").val(e),jQuery("#"+i).attr("data-type",a))})})}function armPaidPostPlanCycleSortableInit(){jQuery.isFunction(jQuery().sortable)&&jQuery(".arm_plan_payment_cycle_ul").sortable({containment:".arm_plan_payment_cycle_ul",tolerance:"pointer",cursor:"move",scroll:!1,item:"li.arm_plan_payment_cycle_li",placeholder:"arm_plan_payment_cycle_li_placeholder",handle:".arm_plan_cycle_sortable_icon",stop:function(e,r){jQuery(r.item).parent().find(".arm_plan_payment_cycle_li").each(function(e,r){jQuery(this).find("input.arm_plan_payment_cycle_order").val(e+1)})},update:function(e,r){var a=r.item.index(),r=r.item.find(".arm_plan_cycle_no").attr("data-index");if(jQuery(".arm_plan_payment_cycle_ul li:nth-child("+(a+1)+")").find(".arm_plan_cycle_no").html(a+1),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+(a+1)+")").find(".arm_plan_cycle_no").attr("data-index",a),r<a)for(var _=a;0<_;_--)jQuery(".arm_plan_payment_cycle_ul li:nth-child("+_+")").find(".arm_plan_cycle_no").html(_),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+_+")").find(".arm_plan_cycle_no").attr("data-index",_-1);else for(_=a+2;_<=jQuery(".arm_plan_payment_cycle_ul li").length;_++)jQuery(".arm_plan_payment_cycle_ul li:nth-child("+_+")").find(".arm_plan_cycle_no").html(_),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+_+")").find(".arm_plan_cycle_no").attr("data-index",_-1)}})}function rand(e,r){return r=r||"",e?rand(--e,"0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz".charAt(Math.floor(60*Math.random()))+r):r}function arm_tooltip_init(){jQuery.isFunction(jQuery().tipso)&&(jQuery(".armhelptip").each(function(){jQuery(this).tipso({position:"top",size:"small",background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".arm_helptip_icon").each(function(){jQuery(this).tipso({position:"top",size:"small",tooltipHover:!0,background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".arm_helptip_icon_ui").each(function(){jQuery.isFunction(jQuery().tooltip)&&jQuery(this).tooltip({tooltipClass:"arm_helptip_ui_content",position:{my:"center bottom-20",at:"center top",using:function(e,r){jQuery(this).css(e),jQuery("<div>").addClass("arm_arrow").addClass(r.vertical).addClass(r.horizontal).appendTo(this)}},content:function(){return jQuery(this).prop("title")},show:{duration:0},hide:{duration:0}})}),jQuery(".arm_email_helptip_icon").each(function(){jQuery(this).tipso({position:"left",size:"small",tooltipHover:!0,background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}),jQuery(".armhelptip_front").each(function(){jQuery(this).tipso({position:"top",size:"small",background:"#939393",color:"#ffffff",width:!1,maxWidth:400,useTitle:!0})}))}function ArmNumberValidation(e,r){e=e.which||e.keyCode,r=jQuery(r).val();return!(46!=e&&31<e&&(e<48||57<e)&&37!=e&&39!=e)&&!(""!=r&&1<r.split(".").length&&46==e)}function isAdminEnqueued(){var r=!1;return jQuery("script[src]").each(function(){var e=jQuery(this).attr("src");/(plugins\/armember\/js\/arm_admin\.js)/g.test(e)&&(r=!0)}),r}window.arm_add_subscription_plan_clicked=!1,function(){function o(e){var r;void 0!==wp.blocks?(0<jQuery('.arm_tinymce_editor_id[value="true"]').length&&""!=(r=jQuery('.arm_tinymce_editor_id[value="true"]').attr("id").replace("arm_tinymce_editor_id-",""))&&(jQuery("div#wp-"+r+"-wrap").length&&jQuery("div#wp-"+r+"-wrap").hasClass("html-active")?a(r,e):"object"==typeof tinyMCE&&(tinyMCE.activeEditor.execCommand("mceInsertContent",!1,e),tinyMCE.activeEditor.execCommand("mceRepaint"))),void 0!==window.arm_props&&"1"==window.arm_props_selected?(window.arm_props.setAttributes({ArmShortcode:e}),0<jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").length&&jQuery("#block-"+window.arm_props.clientId).find(".wp-block-armember-armember-shortcode").val(e)):void 0!==window.arm_restrict_content_props&&"2"==window.arm_props_selected&&(window.arm_restrict_content_props.setAttributes({ArmRestrictContent:e}),0<jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").length&&jQuery("#block-"+window.arm_restrict_content_props.clientId).find(".wp-block-armember-armember-restrict-content-textarea").val(e))):(r="content",0<jQuery('.arm_tinymce_editor_id[value="true"]').length&&(r=jQuery('.arm_tinymce_editor_id[value="true"]').attr("id").replace("arm_tinymce_editor_id-","")),jQuery("div#wp-"+r+"-wrap").length&&jQuery("div#wp-"+r+"-wrap").hasClass("html-active")?a(r,e):"object"==typeof tinyMCE&&(tinyMCE.activeEditor.execCommand("mceInsertContent",!1,e),tinyMCE.activeEditor.execCommand("mceRepaint")))}function a(e,r){var a,e=document.getElementById(e),_=e.scrollTop,i=0,t=e.selectionStart||"0"==e.selectionStart?"ff":!!document.selection&&"ie",n=("ie"==t?(e.focus(),(a=document.selection.createRange()).moveStart("character",-e.value.length),i=a.text.length):"ff"==t&&(i=e.selectionStart),e.value.substring(0,i)),o=e.value.substring(i,e.value.length);e.value=n+r+o,i+=r.length,"ie"==t?(e.focus(),(a=document.selection.createRange()).moveStart("character",-e.value.length),a.moveStart("character",i),a.moveEnd("character",0),a.select()):"ff"==t&&(e.selectionStart=i,e.selectionEnd=i,e.focus()),e.scrollTop=_}jQuery(document).on("click",".arm_form_shortcode_cancel_btn",function(){return popupResetOptions(),!1}),jQuery(document).on("click",".arm_shortcode_insert_btn",function(e){var r=jQuery(this).attr("data-code");if(""!=r){if("arm_membership"==r||"arm_purchased_paid_post_list"==r){if("true"==jQuery(".renew_subscription_radio .arm_shortcode_subscription_opt:checked").val()&&""==jQuery('input[name="renew_text"]').val())return jQuery(".arm_renew_text_error").show(),jQuery('input[name="renew_text"]').focus(),jQuery('input[name="renew_text"]').css("border-color","#ff0000"),!1;if("true"==jQuery(".renew_subscription_radio .arm_shortcode_subscription_opt:checked").val()&&""==jQuery('input[name="make_payment_text"]').val())return jQuery(".arm_make_payment_text_error").show(),jQuery('input[name="make_payment_text"]').focus(),jQuery('input[name="make_payment_text"]').css("border-color","#ff0000"),!1;if("true"==jQuery(".cancel_subscription_radio .arm_shortcode_subscription_opt:checked").val()&&""==jQuery('input[name="cancel_text"]').val())return jQuery(".arm_cancel_text_error").show(),jQuery('input[name="cancel_text"]').focus(),jQuery('input[name="cancel_text"]').css("border-color","#ff0000"),!1;if("true"==jQuery(".update_card_subscription_radio .arm_shortcode_subscription_opt:checked").val()&&""==jQuery('input[name="update_card_text"]').val())return jQuery(".arm_update_card_text_error").show(),jQuery('input[name="update_card_text"]').focus(),jQuery('input[name="update_card_text"]').css("border-color","#ff0000"),!1}o(function(e,r){var a="";switch(e){case"armif":var _=jQuery(".arm_generator_"+e+" input[type=radio]:checked").val();""!=_&&void 0!==_&&(a="["+e+" "+_+"]Content Goes Here[/"+e+"]");break;case"arm_social_login":var e="arm_social_login",i=jQuery(r).find(":input").serializeArray(),t="";""!=i&&jQuery(i).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),a="["+e+t+"]";break;case"arm_close_account":e="arm_close_account",i=jQuery(r).find(":input").serializeArray(),t="";""!=i&&jQuery(i).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),a="["+e+t+"]";break;case"arm_account_detail":var _=jQuery(r).find(".arm_account_detail_options :input").serializeArray(),n=(""!=_&&jQuery(_).each(function(e,r){r.value}),""),_=jQuery(r).find(".arm_social_profile_field_item :input").serializeArray(),t=(""!=_&&jQuery(_).each(function(e,r){n+=r.value+","}),""),o=(jQuery(".arm_icheckbox.arm_member_account_detail_fields").serializeArray()," label='"),d=" value='";jQuery(".arm_icheckbox.arm_member_account_detail_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_account_detail_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+' social_fields="'+n+'" '+(t+=" "+(o+="'")+" "+(d+="'"))+"]";break;case"arm_member_transaction":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_member_transaction_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),jQuery(".arm_icheckbox.arm_member_transaction_fields").serializeArray()," label='"),d=" value='";jQuery(".arm_icheckbox.arm_member_transaction_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_transaction_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+="'")+" "+(d+="'"))+"]";break;case"arm_login_history":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_member_login_history_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),jQuery(".arm_icheckbox.arm_member_login_history_fields").serializeArray()," label='"),d=" value='";jQuery(".arm_icheckbox.arm_member_login_history_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_login_history_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+="'")+" "+(d+="'"))+"]";break;case"arm_conditional_redirection":var t="",_=jQuery("[name=arm_conditional_redirection_plans").val();null==_&&(_=""),""!==(c=jQuery(".arm_shortcode_other_opts_arm_conditional_redirection").find(":input:not(.arm_conditional_redirection_plans_select)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),a="["+e+(t+=' plans="'+_+'"')+"]";break;case"arm_conditional_redirection_role":var t="",_=jQuery("[name=arm_conditional_redirection_by_user_role_roles").val();null==_&&(_=""),""!==(c=jQuery(".arm_shortcode_other_opts_arm_conditional_redirection_by_user_role").find(":input:not(.arm_conditional_redirection_by_user_role_roles_select)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),a="["+e+(t+=' roles="'+_+'"')+"]";break;case"arm_edit_profile":var i=jQuery(r).find("input:not(.arm_spf_active_checkbox)").serializeArray(),t="",s=(""!=i&&jQuery(i).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),"");jQuery(".arm_spf_active_checkbox").each(function(){1==jQuery(this).is(":checked")&&(s+=jQuery(this).val()+",")}),a="[arm_profile_detail "+t+"]";break;case"arm_greeting_message":_=jQuery("#arm_shortcode_username_type").val();a="["+(_=""!==_&&void 0!==_?_:"arm_username")+"]","arm_usermeta"===_&&(a="["+_+' meta="'+jQuery("#arm_custom_user_meta").val()+'"]');break;case"arm_if_user_in_trial_or_not":_=jQuery("#arm_shortcode_if_user_in_trial_or_not").val();""===_||void 0===_||"arm_if_user_in_trial"==_?_="arm_if_user_in_trial":"arm_not_if_user_in_trial"==_&&(_="arm_not_if_user_in_trial"),a="["+_+"] Content Goes Here [/"+_+"]";break;case"arm_membership":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_member_current_membership_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),' membership_label="'),d=' membership_value="';jQuery(".arm_icheckbox.arm_member_current_membership_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_current_membership_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+='"')+" "+(d+='"'))+"]";break;case"arm_membershiplist":i=jQuery(r).find(":input:not(.arm_plan_order_inputs)").serializeArray(),t="",s=(""!=i&&jQuery(i).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),"");jQuery(".arm_plan_order_inputs").each(function(){jQuery(this).attr("data-plan_id")&&(s+=jQuery(this).attr("data-plan_id")+",")}),a="["+e+(t+=" plans_order='"+s+"' ")+"]";break;case"arm_purchased_paid_post_list":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_member_paid_post_current_membership_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),' membership_label="'),d=' membership_value="';jQuery(".arm_icheckbox.arm_member_paid_post_current_membership_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_paid_post_current_membership_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+='"')+" "+(d+='"'))+"]";break;case"arm_paid_post_member_transaction":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_paid_post_member_transaction_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),jQuery(".arm_icheckbox.arm_paid_post_member_transaction_fields").serializeArray()," label='"),d=" value='";jQuery(".arm_icheckbox.arm_paid_post_member_transaction_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_paid_post_transaction_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+="'")+" "+(d+="'"))+"]";break;case"arm_purchased_gift_list":t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_member_gift_current_membership_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),' membership_label="'),d=' membership_value="';jQuery(".arm_icheckbox.arm_member_gift_current_membership_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_gift_current_membership_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+='"')+" "+(d+='"'))+"]";break;case"arm_gift_member_transaction":var c,t="",o=(""!==(c=jQuery(r).find(":input:not(.arm_gift_member_transaction_fields)").serializeArray())&&jQuery(c).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),jQuery(".arm_icheckbox.arm_gift_member_transaction_fields").serializeArray()," label='"),d=" value='";jQuery(".arm_icheckbox.arm_gift_member_transaction_fields").each(function(){var e,r;jQuery(this).is(":checked")&&(jQuery(this).attr("name"),e=jQuery(this).val(),r=jQuery('input[name="arm_gift_transaction_field_label_'+e+'"]').val(),o+=e+",",d+=r+",")}),a="["+e+(t+=" "+(o+="'")+" "+(d+="'"))+"]";break;default:i=jQuery(r).find(":input").serializeArray(),t="";""!=i&&jQuery(i).each(function(e,r){t+=" "+r.name+'="'+r.value+'"'}),a="["+e+" "+t+"]","arm_content"==e&&(a+="Content Goes Here[/"+e+"]")}return a}(r,jQuery("form."+jQuery(this).attr("id")))),jQuery(".popup_close_btn").trigger("click")}else alert("Invalid Shortcode");return!1}),jQuery(document).on("click",".arm_shortcode_form_insert_btn",function(e){var r,a,_,i,t=jQuery(this).attr("data-code");return""!=t?(r=jQuery("form."+jQuery(this).attr("id")),a="",_=jQuery("#arm_shortcode_form_type").val(),""!=(i=r.find(".arm_shortcode_form_main_opt :input").serializeArray())&&jQuery(i).each(function(e,r){if("change_password"==_&&"logged_in_message"==r.name)return!0;a+=" "+r.name+'="'+r.value+'"'}),"true"==r.find(".arm_shortcode_form_popup_opt:checked").val()&&""!=(i=r.find(".arm_shortcode_form_popup_options :input").serializeArray())&&jQuery(i).each(function(e,r){a+=" "+r.name+'="'+r.value+'"'}),o("["+t+a+"]"),jQuery(".popup_close_btn").trigger("click")):alert("Invalid Shortcode"),!1}),jQuery(document).on("click",".arm_shortcode_insert_setup_btn",function(e){var r,a,_,i=jQuery(this).attr("data-code");return""!=i?(r=jQuery("form."+jQuery(this).attr("id")),(a="")!=(_=r.find(".arm_shortcode_setup_main_opt :input").serializeArray())&&jQuery(_).each(function(e,r){a+=" "+r.name+'="'+r.value+'"'}),"true"==r.find(".arm_shortcode_setup_popup_opt:checked").val()&&""!=(_=r.find(".arm_shortcode_setup_popup_options :input").serializeArray())&&jQuery(_).each(function(e,r){a+=" "+r.name+'="'+r.value+'"'}),o("["+i+a+"]"),jQuery(".popup_close_btn").trigger("click")):alert("Invalid Shortcode"),!1}),jQuery(document).on("click",".arm_shortcode_insert_drip_rule_btn",function(e){var r,a="",_="",i="",t=jQuery(this).attr("data-code");return""!=t?("undefined"!=typeof tinyMCE&&jQuery.isFunction(tinyMCE.triggerSave)&&tinyMCE.triggerSave(),""!=(r=jQuery("form.arm_shortcode_drip_rule_form").serializeArray())&&(jQuery(r).each(function(e,r){"armdripcontent"==r.name?_=r.value:"armdripcontentelse"!=r.name&&(i+=" "+r.name+'="'+r.value+'"'),a="armdripcontentelse"==r.name&&""!=r.value?"["+t+" "+i+"]"+_+"[arm_drip_else]"+r.value+"[/"+t+"]":"["+t+" "+i+"]"+_+"[/"+t+"]"}),o(a),jQuery(".popup_close_btn").trigger("click"))):alert("Invalid Shortcode"),!1}),jQuery(document).on("click",".arm_shortcode_insert_rc_btn",function(e){var r,a="",_="",i="",t="",n=jQuery(this).attr("data-code");return""!=n?("undefined"!=typeof tinyMCE&&jQuery.isFunction(tinyMCE.triggerSave)&&tinyMCE.triggerSave(),""!=(r=jQuery(this).parents("form.arm_shortcode_rc_form").serializeArray())&&(jQuery(r).each(function(e,r){"armelse_message"==r.name?_=r.value:"armshortcodecontent"==r.name?a+=r.value:"plan"==r.name?t+=r.value+",":i+=" "+r.name+'="'+r.value+'"'}),""!=t&&(i=' plan="'+t+'" '+i),_="[armelse]\r\n"+_),o("["+n+" "+i+"]\r\n"+a+_+"[/"+n+"]"),jQuery(".popup_close_btn").trigger("click")):alert("Invalid Shortcode"),!1}),arm_selectbox_init()}(),jQuery(window).on("load",function(){"function"==typeof QTags&&null!=QTags&&(QTags.addButton("arm_underline","u","<u>","</u>","u","Underline tag",20),QTags.addButton("arm_paragraph","p","<p>","</p>","p","Paragraph tag",300),QTags.addButton("arm_span","span","<span>","</span>","p","Span tag",301),QTags.addButton("arm_hr","hr","<hr/>","","","Horizontal rule line",302),QTags.addButton("arm_pre","pre","<pre>","</pre>","","Preformatted text tag",303))}),jQuery(document).ready(function(){0<jQuery(".arm_paid_post_plan_subscription_cycle").length&&(armPaidPostPlanCycleSortableInit(),arm_selectbox_init())}),jQuery(document).on("change","input[name='paging']",function(){jQuery("#yes").is(":checked")?jQuery("#arm_paging_type_change").show():jQuery("#arm_paging_type_change").hide()}),jQuery(document).on("change","#arm_shortcode_form_type",function(e){var r=jQuery(this).val();return jQuery(".arm_shortcode_form_opts").addClass("arm_hidden"),jQuery(".arm_shortcode_form_options").addClass("arm_hidden"),jQuery(".arm_shortcode_form_add_btn").attr("disabled","disabled"),""==r?jQuery(".arm_shortcode_form_opts_no_type").removeClass("arm_hidden"):("edit_profile"==r?jQuery(".arm_shortcode_edit_profile_opts").removeClass("arm_hidden"):"arm_social_login"==r?jQuery(".arm_shortcode_arm_social_login_opts").removeClass("arm_hidden"):("change_password"==r?jQuery("#arm_logged_in_message_opt_wrapper").addClass("arm_hidden"):jQuery("#arm_logged_in_message_opt_wrapper").removeClass("arm_hidden"),"registration"==r?jQuery("#arm_assign_default_plan_opt_wrapper").removeClass("arm_hidden"):jQuery("#arm_assign_default_plan_opt_wrapper").addClass("arm_hidden"),jQuery(".arm_shortcode_form_select").removeClass("arm_hidden"),jQuery("#arm_shortcode_form_id").val(""),jQuery(".arm_shortcode_form_id_wrapper .arm_shortcode_form_id_li").addClass("field_inactive").hide(),jQuery(".arm_shortcode_form_id_wrapper").find("."+r).removeClass("field_inactive").show()),arm_selectbox_init()),!1}),jQuery(document).on("change","#arm_shortcode_form_id",function(e){var r;return""==jQuery(this).val()?(jQuery(".arm_shortcode_form_add_btn").attr("disabled","disabled"),jQuery(".arm_shortcode_form_options").addClass("arm_hidden")):(r=jQuery("#arm_shortcode_form_type").val(),jQuery(".arm_shortcode_form_add_btn").removeAttr("disabled"),jQuery(".arm_shortcode_form_options").removeClass("arm_hidden"),"change_password"==r&&(jQuery("#arm_logged_in_message_opt_wrapper").addClass("arm_hidden"),jQuery("#logged_in_message_input").addClass("arm_hidden"))),!1}),jQuery(document).on("change","#arm_shortcode_current_membership_setup_id",function(e){return""==jQuery(this).val()?jQuery(".arm_shortcode_insert_btn.arm_current_membership_shortcode").attr("disabled","disabled"):jQuery(".arm_shortcode_insert_btn.arm_current_membership_shortcode").removeAttr("disabled"),!1}),jQuery(document).on("change","#arm_shortcode_paid_post_current_membership_setup_id",function(e){return""==jQuery(this).val()?jQuery(".arm_shortcode_insert_btn.arm_paid_post_current_membership_shortcode").attr("disabled","disabled"):jQuery(".arm_shortcode_insert_btn.arm_paid_post_current_membership_shortcode").removeAttr("disabled"),!1}),jQuery(document).on("change","#arm_shortcode_gift_current_membership_setup_id",function(e){return""==jQuery(this).val()?jQuery(".arm_shortcode_insert_btn.arm_gift_current_membership_shortcode").attr("disabled","disabled"):jQuery(".arm_shortcode_insert_btn.arm_gift_current_membership_shortcode").removeAttr("disabled"),!1}),jQuery(document).on("change","#arm_user_plan_info",function(){return""==jQuery(this).val()?jQuery(".arm_shortcode_insert_btn.arm_user_planinfo_shortcode").attr("disabled","disabled"):jQuery(".arm_shortcode_insert_btn.arm_user_planinfo_shortcode").removeAttr("disabled"),!1}),jQuery(document).on("change","#arm_shortcode_close_account",function(e){return""==jQuery(this).val()?(jQuery(".arm_close_account_btn").attr("disabled","disabled"),jQuery(".arm_close_account_custom_css_textarea").addClass("arm_hidden")):(jQuery(".arm_close_account_btn").removeAttr("disabled"),jQuery(".arm_close_account_custom_css_textarea").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_conditional_redirection_condition",function(e){var r,a;""==jQuery(this).val()?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):(r=jQuery(".arm_conditional_redirection_redirecr_to").val(),a=jQuery("[name=arm_conditional_redirection_plans]").val(),""==r||null==a?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_btn").removeAttr("disabled"))}),jQuery(document).on("change","#arm_conditional_redirection_by_user_role_condition",function(e){var r,a;""==jQuery(this).val()?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):(r=jQuery(".arm_conditional_redirection_by_user_role_redirecr_to").val(),a=jQuery("[name=arm_conditional_redirection_by_user_role_roles]").val(),""==r||null==a?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_by_user_role_btn").removeAttr("disabled"))}),jQuery(document).on("change","[name=arm_conditional_redirection_plans]",function(e){var r,a;null==jQuery(this).val()?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):(r=jQuery("#arm_conditional_redirection_condition").val(),a=jQuery(".arm_conditional_redirection_redirecr_to").val(),""==r||""==a?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_btn").removeAttr("disabled"))}),jQuery(document).on("change","[name=arm_conditional_redirection_by_user_role_roles]",function(e){var r,a;null==jQuery(this).val()?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):(r=jQuery("#arm_conditional_redirection_by_user_role_condition").val(),a=jQuery(".arm_conditional_redirection_by_user_role_redirecr_to").val(),""==r||""==a?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_by_user_role_btn").removeAttr("disabled"))}),jQuery(document).on("blur",".arm_conditional_redirection_redirecr_to",function(e){var r,a;""==jQuery(this).val()?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):(r=jQuery("#arm_conditional_redirection_condition").val(),a=jQuery("[name=arm_conditional_redirection_plans]").val(),""==r||null==a?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_btn").removeAttr("disabled"))}),jQuery(document).on("blur",".arm_conditional_redirection_by_user_role_redirecr_to",function(e){var r,a;""==jQuery(this).val()?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):(r=jQuery("#arm_conditional_redirection_by_user_role_condition").val(),a=jQuery("[name=arm_conditional_redirection_by_user_role_roles]").val(),""==r||null==a?jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"):jQuery(".arm_conditional_redirection_by_user_role_btn").removeAttr("disabled"))}),jQuery(document).on("change","#arm_shortcode_form_link_type",function(e){return"link"==jQuery(this).val()?(jQuery(".arm_shortcode_form_link_opts").removeClass("arm_hidden"),jQuery(".arm_shortcode_form_button_opts").addClass("arm_hidden")):(jQuery(".arm_shortcode_form_link_opts").addClass("arm_hidden"),jQuery(".arm_shortcode_form_button_opts").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_social_login_network_type",function(e){var r=jQuery(this).val();return"facebook"==r?(jQuery("#fb_icon1").prop("checked",!0),jQuery(".arm_social_login_fb_icons #arm_social_login_fb1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_google_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"twitter"==r?(jQuery("#tw_icon1").prop("checked",!0),jQuery(".arm_social_login_tw_icons #arm_social_login_tw1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_google_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"linkedin"==r?(jQuery("#li_icon1").prop("checked",!0),jQuery(".arm_social_login_li_icons #arm_social_login_li1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_google_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"vk"==r?(jQuery("#vk_icon1").prop("checked",!0),jQuery(".arm_social_login_vk_icons #arm_social_login_vk1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"insta"==r?(jQuery("#insta_icon1").prop("checked",!0),jQuery(".arm_social_login_insta_icons #arm_social_login_insta1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"google"==r?(jQuery("#gp_icon1").prop("checked",!0),jQuery(".arm_social_login_gp_icons #arm_social_login_gp1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").addClass("arm_hidden")):"tumblr"==r&&(jQuery("#tu_icon1").prop("checked",!0),jQuery(".arm_social_login_tu_icons #arm_social_login_tu1 .iradio_minimal-red").addClass("checked"),jQuery(".arm_social_login_fb_icons").addClass("arm_hidden"),jQuery(".arm_social_login_tu_icons").removeClass("arm_hidden"),jQuery(".arm_social_login_tw_icons").addClass("arm_hidden"),jQuery(".arm_social_login_li_icons").addClass("arm_hidden"),jQuery(".arm_social_login_gp_icons").addClass("arm_hidden"),jQuery(".arm_social_login_vk_icons").addClass("arm_hidden"),jQuery(".arm_social_login_insta_icons").addClass("arm_hidden"),jQuery(".arm_social_login_google_icons").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_setup_link_type",function(e){return"link"==jQuery(this).val()?(jQuery(".arm_shortcode_setup_link_opts").removeClass("arm_hidden"),jQuery(".arm_shortcode_setup_button_opts").addClass("arm_hidden")):(jQuery(".arm_shortcode_setup_link_opts").addClass("arm_hidden"),jQuery(".arm_shortcode_setup_button_opts").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_action_button_type",function(e){var r=jQuery(this).val();return jQuery(".arm_shortcode_action_button_opts").addClass("arm_hidden"),(""==r?jQuery(".arm_shortcode_action_button_opts_no_type"):jQuery(".arm_shortcode_action_button_opts_"+r)).removeClass("arm_hidden"),!1}),jQuery(document).on("change","#arm_shortcode_logout_link_type",function(e){return"link"==jQuery(this).val()?(jQuery(".arm_shortcode_logout_link_opts").removeClass("arm_hidden"),jQuery(".arm_shortcode_logout_button_opts").addClass("arm_hidden")):(jQuery(".arm_shortcode_logout_link_opts").addClass("arm_hidden"),jQuery(".arm_shortcode_logout_button_opts").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_cancel_membership_link_type",function(e){return"link"==jQuery(this).val()?(jQuery(".arm_shortcode_cancel_membership_link_opts").removeClass("arm_hidden"),jQuery(".arm_shortcode_cancel_membership_button_opts").addClass("arm_hidden")):(jQuery(".arm_shortcode_cancel_membership_link_opts").addClass("arm_hidden"),jQuery(".arm_shortcode_cancel_membership_button_opts").removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_other_type",function(e){var r=jQuery(this).val();return jQuery(".arm_shortcode_other_opts").addClass("arm_hidden"),""==r?jQuery(".arm_shortcode_other_opts_no_type").removeClass("arm_hidden"):("arm_conditional_redirection"==r?jQuery(".arm_conditional_redirection_btn").attr("disabled","disabled"):"arm_conditional_redirection_by_user_role"==r&&jQuery(".arm_conditional_redirection_by_user_role_btn").attr("disabled","disabled"),jQuery(".arm_shortcode_other_opts_"+r).removeClass("arm_hidden")),!1}),jQuery(document).on("change","#arm_shortcode_username_type",function(e){var r=jQuery(this).val(),a=jQuery("#arm_shortcode_other_type").val();return"arm_usermeta"==r?jQuery(".arm_shortcode_other_opts_"+a+"_"+r).removeClass("arm_hidden"):""!=r&&"arm_usermeta"==r||jQuery(".arm_shortcode_other_opts_"+a+"_arm_usermeta").addClass("arm_hidden"),!1}),jQuery(document).on("change",".change_paging_options",function(e){return"infinite"==jQuery(this).val()?jQuery(".arm_activity_paging_options").removeClass("arm_hidden"):jQuery(".arm_activity_paging_options").addClass("arm_hidden"),!1}),jQuery(document).on("click",".arm_tab_link a",function(e){var r=jQuery(this).attr("href");return jQuery(".arm_tab_link").removeClass("arm_active_tab_link"),jQuery(this).parents(".arm_tab_link").addClass("arm_active_tab_link"),jQuery(".arm_tab_content").removeClass("arm_active_tab"),jQuery(r).addClass("arm_active_tab"),!1}),jQuery(document).on("click",".armif_tag_radio",function(){var e=jQuery(this).attr("data-tag");jQuery(".armif_tag_desc_block").addClass("arm_hide_block"),jQuery(".armif_tag_"+e).removeClass("arm_hide_block")}),jQuery(document).on("change",".arm_shortcode_armif_tags input[type=radio]",function(){var e=jQuery(this).val();jQuery(".arm_shortcode_armif_tags li").removeClass("activetag"),""!=e?(jQuery(this).parents("li").addClass("activetag"),jQuery(".arm_shortcode_insert_btn_armif").show("slow")):jQuery(".arm_shortcode_insert_btn_armif").hide("slow")}),jQuery(document).on("change",".form_popup_type_radio input",function(){"true"==jQuery(this).val()?(jQuery(this).parents("td").find(".form_popup_options").show(),jQuery("#arm_form_position_opt_wrapper").hide(),jQuery("#arm_form_position_opt_wrapper").removeClass("arm_shortcode_form_main_opt")):(jQuery(this).parents("td").find(".form_popup_options").hide(),jQuery("#arm_form_position_opt_wrapper").show(),jQuery("#arm_form_position_opt_wrapper").addClass("arm_shortcode_form_main_opt"))}),jQuery(document).on("change",".setup_popup_type_radio input",function(){"true"==jQuery(this).val()?jQuery(this).parents("td").find(".setup_popup_options").show():jQuery(this).parents("td").find(".setup_popup_options").hide()}),jQuery(document).on("change",".edit_form_popup_type_radio input",function(){"yes"==jQuery(this).val()?jQuery(".arm_edit_profile_cover_options").show():jQuery(".arm_edit_profile_cover_options").hide()}),jQuery(document).on("change",".change_subscription_radio input",function(){"true"==jQuery(this).val()?jQuery(".change_subscription_opt").show():jQuery(".change_subscription_opt").hide()}),jQuery(document).on("change",".renew_subscription_radio input",function(){"true"==jQuery(this).val()?jQuery(".renew_subscription_btn_options").show():jQuery(".renew_subscription_btn_options").hide()}),jQuery(document).on("change",".view_invoice_radio input",function(){"true"==jQuery(this).val()?jQuery(".view_invoice_btn_options").show():jQuery(".view_invoice_btn_options").hide()}),jQuery(document).on("change",".cancel_subscription_radio input",function(){"true"==jQuery(this).val()?jQuery(".cancel_subscription_btn_options").show():jQuery(".cancel_subscription_btn_options").hide()}),jQuery(document).on("change",".update_card_subscription_radio input",function(){"true"==jQuery(this).val()?jQuery(".update_card_subscription_btn_options").show():jQuery(".update_card_subscription_btn_options").hide()}),jQuery(document).on("click",".arm_selectbox",function(){isAdminEnqueued()||jQuery(this).find("dd ul").toggle()}),jQuery(document).on("click",".arm_selectbox dt",function(e){var r;isAdminEnqueued()||(0==(r=jQuery(this)).parent().find("dd ul").is(":visible")?(jQuery("dd ul").not(this).hide(),r.find("span:not(.arm_no_auto_complete)").hide(),r.find("input").show(),r.find("input").focus()):r.parent().find("dd ul").show(),r.parent().find("dd ul li:not(.field_inactive)").show())}),jQuery(document).on("keyup",".arm_selectbox dt input",function(e){var r;isAdminEnqueued()||(e.stopPropagation(),e=e.keyCode,-1===jQuery.inArray(e,[16,17,18,19,20,33,34,35,36,37,38,39,40,45,91,92,112,113,114,115,116,117,118,119,120,121,122,123,144,145])&&(jQuery(this).parent().parent().find("dd ul").scrollTop(),r=(r=jQuery(this).val()).toLowerCase(),jQuery(this).parent().parent().find("dd ul").show(),jQuery(this).parent().parent().find("dd ul li:not(.field_inactive)").each(function(e){-1!=jQuery(this).attr("data-label").toLowerCase().indexOf(r)?jQuery(this).show():jQuery(this).hide()})))}),jQuery(document).on("click",".arm_selectbox dd ul li:not(.field_inactive)",function(e){var r,a,_,i,t,n,o;isAdminEnqueued()||(jQuery(document).find(".arm_selectbox:active dd ul").hide(),r=jQuery(this).parents(".arm_selectbox"),a=""!=(a=jQuery(this).attr("data-label"))&&null!=a?a:jQuery(this).html(),_=jQuery(this).attr("data-value"),i=jQuery(this).attr("data-type"),n=(t=r.find("dd ul")).attr("data-id"),o=jQuery("input#"+n).val(),r.find("dt span").html(a).show(),r.find("dt input").val(a).hide(),jQuery("input#"+n).val(_),jQuery("input#"+n).attr("data-type",i),o!=_&&jQuery("input#"+n).trigger("change"),t.find("li:not(.field_inactive)").show())}),jQuery(document).bind("click",function(e){isAdminEnqueued()||jQuery(e.target).parents().hasClass("arm_selectbox")||(jQuery(".arm_selectbox dd ul").hide(),jQuery(".arm_selectbox dt span").show(),jQuery(".arm_selectbox dt input").hide(),jQuery(".arm_autocomplete").each(function(){""==jQuery(this).val()&&jQuery(this).val(jQuery(this).parent().find("span").html())}))}),jQuery(document).on("click",".arm_tabgroup_belt a",function(){var e=jQuery(this),r=e.attr("data-id");return jQuery(".arm_tabgroup_link").removeClass("arm_active"),e.parents(".arm_tabgroup_link").addClass("arm_active"),e.parents(".arm_tabgroups").find(".arm_tabgroup_content").removeClass("arm_show"),jQuery(".arm_tabgroup_content_buttons").removeClass("arm_show"),jQuery("#"+r).addClass("arm_show"),jQuery("#"+r+"_buttons").addClass("arm_show"),!1}),jQuery(document).on("change",".arm_shortcode_edit_profile_form",function(e){var r=jQuery("#arm_ajaxurl").val(),a=jQuery(this).val(),_=jQuery('input[name="arm_wp_nonce"]').val();""!==a&&jQuery.ajax({url:r,type:"POST",data:"action=arm_get_spf_in_tinymce&form_name="+a+"&_wpnonce="+_,dataType:"json",success:function(e){!1===e.error&&(jQuery("#arm_social_profile_fields_wrapper .arm_social_profile_field_item").html(e.content),armICheckInit())}})}),jQuery(document).ajaxComplete(function(e,r,a){var _,i;void 0!==a.data&&(_=/(tag\=arm_form)/gi.test(a.data),a=/(param(.*?)\=(.*?))/gi.test(a.data),_&&!a&&(i=jQuery("#vc_ui-panel-edit-element").find("#logged_in_message"),setTimeout(function(){""==i.val()&&i.val("You are already logged in")},100)))}),jQuery(document).on("change",'input[name="paid_post_type"]',function(){var e=jQuery(this).val(),r=jQuery(this).parents(".postbox");1==jQuery(this).is(":checked")&&("buy_now"==e?(r.find(".arm_paid_post_plan_one_time_duration").removeClass("hidden_section"),r.find(".arm_paid_post_one_time_amount").removeClass("hidden_section"),"forever"==r.find('input[name="paid_post_duration"]:checked').val()?r.find(".arm_paid_post_row.arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"):r.find(".arm_paid_post_row.arm_paid_post_plan_one_time_duration_value").removeClass("hidden_section"),r.find(".arm_paid_post_plan_subscription_cycle ").addClass("hidden_section")):"free"==e?(r.find(".arm_paid_post_plan_one_time_duration").addClass("hidden_section"),r.find(".arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"),r.find(".arm_paid_post_one_time_amount").addClass("hidden_section"),r.find(".arm_paid_post_plan_subscription_cycle ").addClass("hidden_section")):(r.find(".arm_paid_post_plan_one_time_duration").addClass("hidden_section"),r.find(".arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"),r.find(".arm_paid_post_one_time_amount").addClass("hidden_section"),r.find(".arm_paid_post_plan_subscription_cycle ").removeClass("hidden_section")))}),jQuery(document).on("change",'input[name="paid_post_duration"]',function(){var e=jQuery(this).val(),r=jQuery(this).parents(".postbox");1==jQuery(this).is(":checked")&&("forever"==e?r.find(".arm_paid_post_row.arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"):r.find(".arm_paid_post_row.arm_paid_post_plan_one_time_duration_value").removeClass("hidden_section"))}),jQuery(document).on("change","#arm_paid_plan_one_time_duration_type",function(){var e=jQuery(this).val(),r=jQuery(this).parents(".postbox");"D"==e?(r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_d").removeClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_w").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_m").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_y").addClass("hidden_section")):"W"==e?(r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_d").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_w").removeClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_m").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_y").addClass("hidden_section")):"M"==e?(r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_d").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_w").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_m").removeClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_y").addClass("hidden_section")):"Y"==e&&(r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_d").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_w").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_m").addClass("hidden_section"),r.find(".arm_paid_post_duration_dropdown.arm_paid_post_duration_y").removeClass("hidden_section"))}),jQuery(document).on("click","#arm_add_payment_cycle_recurring",function(){window.arm_add_subscription_plan_clicked=!0;var e=jQuery(this).parents(".postbox"),r=e.find("#arm_total_recurring_plan_cycles").val(),a=(parseInt(r),jQuery("#arm_total_recurring_plan_cycles_counter").val()),_=e.find("#arm_total_recurring_plan_cycles_order").val(),t='<li class="arm_plan_payment_cycle_li paid_subscription_options_recurring_payment_cycles_child_box" id="paid_subscription_options_recurring_payment_cycles_child_box'+a+'"><div class="arm_plan_payment_cycle_label"><label class="arm_plan_payment_cycle_label_text">'+ARMCYCLELABEL+'</label><div class="arm_plan_payment_cycle_label_input"><input type="hidden" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][cycle_key]" value="arm'+rand(5)+'"/><input type="text" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][cycle_label]" value="" class="paid_subscription_options_recurring_payment_cycle_label arm_paid_post_input_field" data-msg-required="'+LABELERROR+'"/></div></div>';for(t+='<div class="arm_plan_payment_cycle_amount"><label class="arm_plan_payment_cycle_amount_text">'+CYCLEAMOUNT+'</label><div class="arm_plan_payment_cycle_amount_input"><span class="arm_paid_post_plan_currency_symbol arm_paid_post_plan_currency_symbol_pre '+CURRENCYPREF+'">'+CURRENCYSYM+'</span><input type="text" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][cycle_amount]" value="" class="paid_subscription_options_recurring_payment_cycle_amount arm_paid_post_input_field" data-msg-required="'+AMOUNTERROR+'" onkeypress="javascript:return ArmNumberValidation(event, this)"/><span class="arm_paid_post_plan_currency_symbol arm_paid_post_plan_currency_symbol_post '+CURRENCYSUF+'">'+CURRENCYSYM+"</span></div></div>","undefined"!=typeof arm_custom_currency_payment_cycle_amount_field_paid_post&&(t+=arm_custom_currency_payment_cycle_amount_field_paid_post.replaceAll("ARMPCYCLEKEY",a)),t+='<div class="arm_plan_payment_cycle_billing_cycle"><label class="arm_plan_payment_cycle_billing_text">'+BILLINGCYCLE+'</label><div class="arm_plan_payment_cycle_billing_input"><input type="hidden" id="arm_ipc_billing'+a+'" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][billing_cycle]" value="1" /><dl class="arm_selectbox arm_margin_0 arm_width_60 arm_min_width_50"><dt><span>1</span><input type="text" style="display:none;" value="" class="arm_autocomplete"/><i class="armfa armfa-caret-down armfa-lg"></i></dt><dd><ul data-id="arm_ipc_billing'+a+'">',i=1;i<=90;i++)t+='<li data-label="'+i+'" data-value="'+i+'">'+i+"</li>";for(t+='</ul></dd></dl><input type="hidden" id="arm_ipc_billing_type'+a+'" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][billing_type]" value="D" />&nbsp;<dl class="arm_selectbox arm_margin_0 arm_min_width_75" style=" margin: 0px;"><dt class="arm_width_80"><span>'+DAY+'</span><input type="text" style="display:none;" value="" class="arm_autocomplete"/><i class="armfa armfa-caret-down armfa-lg"></i></dt><dd><ul data-id="arm_ipc_billing_type'+a+'"><li data-label="'+DAY+'" data-value="D">'+DAY+'</li><li data-label="'+MONTH+'" data-value="M">'+MONTH+'</li><li data-label="'+YEAR+'" data-value="Y">'+YEAR+'</li></ul></dd></dl></div></div><div class="arm_plan_payment_cycle_recurring_time"><label class="arm_plan_payment_cycle_recurring_text">'+RECURRINGTIME+'</label><input type="hidden" id="arm_ipc_recurring'+a+'" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][recurring_time]" value="infinite"/><dl class="arm_selectbox arm_margin_0 arm_width_100 arm_min_width_70"><dt><span>'+INFINITE+'</span><input type="text" style="display:none;" value="" class="arm_autocomplete"/><i class="armfa armfa-caret-down armfa-lg"></i></dt><dd><ul data-id="arm_ipc_recurring'+a+'"><li data-label="'+INFINITE+'" data-value="infinite">'+INFINITE+"</li>",i=2;i<=30;i++)t+='<li data-label="'+i+'" data-value="'+i+'">'+i+"</li>";t+='</ul></dd></dl></div></div><div class="arm_plan_payment_cycle_action_buttons"><div class="arm_plan_cycle_plus_icon arm_helptip_icon tipso_style arm_add_plan_icon" title="'+ADDCYCLE+'" id="arm_add_payment_cycle_recurring" data-field_index="'+a+'"></div><div class="arm_plan_cycle_minus_icon arm_helptip_icon tipso_style arm_add_plan_icon" title="'+REMOVECYCLE+'" id="arm_remove_recurring_payment_cycle" data-index="'+a+'"></div><div class="arm_plan_cycle_sortable_icon"></div></div><input type="hidden" name="arm_paid_post_subscription_plan_options[payment_cycles]['+a+'][payment_cycle_order]" value="'+_+'" class="arm_module_payment_cycle_order"></li>',e.find(".arm_plan_payment_cycle_ul").append(t),r++,_++,a++,e.find("#arm_total_recurring_plan_cycles").val(r),e.find("#arm_total_recurring_plan_cycles_order").val(_),e.find("#arm_total_recurring_plan_cycles_counter").val(a),arm_tooltip_init()}),jQuery(document).on("change","#arm_enable_paid_post",function(){var e=jQuery(this).parents(".postbox");jQuery(this).is(":checked")?e.find(".arm_paid_post_inner_container").removeClass("hidden_section"):e.find(".arm_paid_post_inner_container").addClass("hidden_section")}),jQuery(document).on("click","#arm_remove_recurring_payment_cycle",function(e){if(!isAdminEnqueued()){e.stopPropagation();e=jQuery(".paid_subscription_options_recurring_payment_cycle_label").length;if(1==e)return alert(EMESSAGE),!1;jQuery(this).attr("data-index");jQuery(this).closest("li").remove(),e--,jQuery("#arm_total_recurring_plan_cycles").val(e),jQuery(".tipso_bubble").remove();var e=jQuery(".arm_plan_payment_cycle_ul"),r=e.find("li.arm_plan_payment_cycle_li").index(),e=e.find("li.arm_plan_payment_cycle_li .arm_plan_cycle_no").attr("data-index");if(jQuery(".arm_plan_payment_cycle_ul li:nth-child("+(r+1)+")").find(".arm_plan_cycle_no").html(r+1),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+(r+1)+")").find(".arm_plan_cycle_no").attr("data-index",r),e<r)for(var a=r;0<a;a--)jQuery(".arm_plan_payment_cycle_ul li:nth-child("+a+")").find(".arm_plan_cycle_no").html(a),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+a+")").find(".arm_plan_cycle_no").attr("data-index",a-1);else for(a=r+2;a<=jQuery(".arm_plan_payment_cycle_ul li").length;a++)jQuery(".arm_plan_payment_cycle_ul li:nth-child("+a+")").find(".arm_plan_cycle_no").html(a),jQuery(".arm_plan_payment_cycle_ul li:nth-child("+a+")").find(".arm_plan_cycle_no").attr("data-index",a-1)}}),jQuery(document).on("change",'input[name="arm_enable_paid_post_alternate_content"]',function(){var e=jQuery(this).parents(".postbox");jQuery(this).is(":checked")?e.find(".arm_paid_post_row_alternate_content_row").removeClass("hidden_section"):e.find(".arm_paid_post_row_alternate_content_row").addClass("hidden_section")}),jQuery(document).on("click",".arm_form_shortcode_popup_link,.arm_restriction_shortcode_popup_link",function(){var e=jQuery(this).attr("data-editor-id");jQuery(".arm_tinymce_editor_id").val(!1),jQuery(".arm_tinymce_editor_id#arm_tinymce_editor_id-"+e).val(!0)}),jQuery(document).on("click",'form#post input[type="submit"]',function(){0<jQuery("#arm_enable_paid_post").length&&!0===jQuery("#arm_enable_paid_post").is(":checked")&&"publish"===jQuery(this).attr("id")&&(jQuery(".paid_subscription_options_recurring_payment_cycle_label,.paid_subscription_options_recurring_payment_cycle_amount").each(function(){jQuery(this).rules("add",{required:!0})}),jQuery(".paid_subscription_options_recurring_payment_cycle_amount").each(function(){jQuery(this).rules("add",{number:!0})}))}),jQuery(document).ready(function(){var e,r;0<jQuery("#arm_paid_post_metabox_wrapper").length&&(0<jQuery("form#post").length&&0<jQuery("#arm_enable_paid_post").length&&!0===jQuery("#arm_enable_paid_post").is(":checked")&&jQuery("#post").validate({errorClass:"error arm_invalid",validClass:"valid arm_valid",errorPlacement:function(e,r){e.appendTo(r.parent())},focusInvalid:!1,invalidHandler:function(e,r){r.numberOfInvalids()&&jQuery("html, body").animate({scrollTop:jQuery(r.errorList[0].element).offset().top-150},0)},rules:{plan_name:"required",arm_paid_post_plan:{number:!0,required:function(){return!!jQuery('#arm_paid_post_metabox_wrapper input[name="arm_enable_paid_post"]').is(":checked")}}},submitHandler:function(e){jQuery(e).hasClass("arm_already_clicked")||e.submit()}}),"buy_now"==(e=jQuery("#arm_paid_post_metabox_wrapper").find('input[name="paid_post_type"]:checked').val())?(jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration").removeClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_one_time_amount").removeClass("hidden_section"),void 0===(r=jQuery("#arm_paid_post_metabox_wrapper").find('input[name="paid_post_duration"]:checked').val())||"forever"==r?jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"):jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration_value").removeClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_subscription_cycle ").addClass("hidden_section")):"free"==e?(jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_one_time_amount").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_subscription_cycle ").addClass("hidden_section")):(jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_one_time_duration_value").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_one_time_amount").addClass("hidden_section"),jQuery("#arm_paid_post_metabox_wrapper").find(".arm_paid_post_plan_subscription_cycle ").removeClass("hidden_section"))),0<jQuery("#arm_drip_rule_metabox_wrapper").length&&0<jQuery("form#post").length&&0<jQuery("input#arm_enable_drip_rule_hidden").val()&&jQuery("form#post").validate({ignore:"",errorClass:"error arm_invalid",validClass:"valid arm_valid",errorPlacement:function(e,r){e.appendTo(r.parents(".arm_required_wrapper"))},focusInvalid:!1,invalidHandler:function(e,r){r.numberOfInvalids()&&jQuery("html, body").animate({scrollTop:jQuery(r.errorList[0].element).offset().top-150},0)},rules:{"rule_plans[]":{number:!0,required:function(){return 0<jQuery("#arm_drip_rule_metabox_wrapper input#arm_enable_drip_rule_hidden").val()}},"rule_options[days]":{number:!0,required:function(){return"days"==jQuery("#arm_add_drip_type").val()}},"rule_options[expire_days]":{number:!0,required:function(){return!0===jQuery("#arm_drip_expiration_days").is(":checked")}},"rule_options[post_publish]":{number:!0,required:function(){return"post_publish"==jQuery("#arm_add_drip_type").val()}},"rule_options[exp_post_publish]":{number:!0,required:function(){return!0===jQuery("#arm_drip_type_expire_post_publish").is(":checked")}},"rule_options[post_modify]":{number:!0,required:function(){return"post_modify"==jQuery("#arm_add_drip_type").val()}},"rule_options[exp_post_modify]":{number:!0,required:function(){return!0===jQuery("#arm_drip_type_expire_post_modify").is(":checked")}},"rule_options[from_date]":{date:!0,required:function(){return"dates"==jQuery("#arm_add_drip_type").val()}}},submitHandler:function(e){jQuery(e).hasClass("arm_already_clicked")||e.submit()}})}),jQuery(document).on("change",".arm_drip_type_input",function(){var e=jQuery(this).val(),r=jQuery(this).attr("data-drip_id"),a=jQuery("#arm_add_rule_item_type").val();jQuery(".arm_drip_type_options_instant.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_days.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_dates.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_post_publish.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_post_modify.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_to_dates.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(".arm_drip_type_options_to_dates.arm_drip_rule_"+r).addClass("hidden_section"),jQuery("#arm_drip_type_options_"+e+".arm_drip_rule_"+r).removeClass("hidden_section"),"dates"==e||"custom_content"==a?(jQuery(".arm_drip_enable_post_type_opts").addClass("hidden_section"),jQuery(".arm_drip_rule_enable_opts").is(":checked")&&jQuery(".arm_drip_rule_enable_opts").prop("checked",!1),"dates"==e&&(jQuery(".arm_drip_type_options_to_dates").removeClass("hidden_section"),jQuery(".arm_drip_type_options_to_dates").removeClass("hidden_section"))):jQuery(".arm_drip_enable_post_type_opts").removeClass("hidden_section")}),jQuery(document).ready(function(){var e=jQuery(".arm_dripped_ids").val();""!=e&&void 0!==e&&(e=e.split(","),jQuery.each(e,function(e,r){var a=jQuery(document).find("#arm_enable_drip_rule_"+r).is(":checked"),_=jQuery(document).find("#arm_add_drip_type_"+r).val();a&&""!=_?(jQuery(document).find("#rule_protection_checkbox").attr("checked","checked"),jQuery(document).find("#rule_protection_checkbox_hidden").val(1),jQuery(document).find(".arm_drip_metabox.arm_drip_rule_"+r).removeClass("hidden_section"),jQuery(document).find(".arm_drip_type_options_"+_+".arm_drip_rule_"+r).removeClass("hidden_section")):(jQuery(document).find(".arm_drip_metabox").addClass("hidden_section"),jQuery(document).find(".arm_drip_type_options_wrapper").addClass("hidden_section"))}),jQuery.isFunction(jQuery().datepicker)&&jQuery(".arm_datepicker").each(function(){var e=jQuery(this),r=(new Date,e.attr("data-cal_localization"),e.attr("data-date_field"),e.attr("data-arm_dateformat")),a=e.attr("data-show_timepicker");""!=r&&void 0!==r||(r="mm/dd/yy"),""!=a&&void 0!==a&&1==a&&(r+=" hh:mm A"),jQuery("#ui-datepicker-div").addClass("arm-ui-datepicker"),e.datepicker({dateFormat:r,changeMonth:!0,changeYear:!0}).on("dp.change",function(e){jQuery(this).trigger("input")})}))}),jQuery(document).on("change",".arm_enable_drip_rule",function(){var e=jQuery(this).is(":checked"),r=jQuery(this).attr("data-drip_id"),a=jQuery(document).find("#arm_add_drip_type_"+r).val();e?(jQuery(document).find("#arm_enable_drip_rule_hidden_"+r).val(1),jQuery(document).find("#rule_protection_checkbox").attr("checked","checked"),jQuery(document).find("#rule_protection_checkbox_hidden").val(1),jQuery(document).find(".arm_drip_metabox.arm_drip_rule_"+r).removeClass("hidden_section"),jQuery(document).find(".arm_drip_type_options_"+a+".arm_drip_rule_"+r).removeClass("hidden_section")):(jQuery(document).find(".arm_drip_metabox.arm_drip_rule_"+r).addClass("hidden_section"),jQuery(document).find(".arm_drip_type_options_wrapper").addClass("hidden_section"))}),jQuery(document).on("change",".arm_drip_expiration_drip_type_days",function(){jQuery(this).is(":checked")?jQuery(".arm_drip_expire_after_days").removeClass("hidden_section"):jQuery(".arm_drip_expire_after_days").addClass("hidden_section")}),jQuery(document).on("change",".arm_drip_type_expire_post_publish",function(){jQuery(this).is(":checked")?jQuery(".arm_drip_expire_post_publish").removeClass("hidden_section"):jQuery(".arm_drip_expire_post_publish").addClass("hidden_section")}),jQuery(document).on("change",".arm_drip_type_expire_post_modify",function(){jQuery(this).is(":checked")?jQuery(".arm_drip_expire_post_modify").removeClass("hidden_section"):jQuery(".arm_drip_expire_post_modify").addClass("hidden_section")}),jQuery(document).on("change",".arm_drip_expiration_drip_type_immediate",function(){jQuery(this).is(":checked")?jQuery(".arm_drip_expire_after_immediate").removeClass("hidden_section"):jQuery(".arm_drip_expire_after_immediate").addClass("hidden_section")});