!function(m){var v=[],e={options:{prependExistingHelpBlock:!1,sniffHtml:!0,preventSubmit:!0,submitError:!1,submitSuccess:!1,semanticallyStrict:!1,autoAdd:{helpBlocks:!0},filter:function(){return!0}},methods:{init:function(a){var c=m.extend(!0,{},e),a=(c.options=m.extend(!0,c.options,a),m.unique(this.map(function(){return m(this).parents("form")[0]}).toArray()));return m(a).bind("submit",function(a){var e=m(this),t=0,i=e.find("input,textarea,select").not("[type=submit],[type=image]").filter(c.options.filter);i.trigger("submit.validation").trigger("validationLostFocus.validation"),i.each(function(a,e){e=m(e).parents(".arm-control-group").first();e.hasClass("warning")&&(e.removeClass("warning").addClass("error"),t++)}),i.trigger("validationLostFocus.validation"),t?(c.options.preventSubmit&&a.preventDefault(),e.addClass("error"),m.isFunction(c.options.submitError)&&c.options.submitError(e,a,i.ARMjqBootstrapValidation("collectErrors",!0))):(e.removeClass("error"),m.isFunction(c.options.submitSuccess)&&c.options.submitSuccess(e,a))}),this.each(function(){for(var a,l=m(this),r=l.parents(".arm-control-group").first(),o=r.find(".arm-df__fc--validation").first(),d=l.parents("form").first(),t=[],e=(!o.length&&c.options.autoAdd&&c.options.autoAdd.helpBlocks&&(o=m('<div class="arm-df__fc--validation" />'),r.find(".arm-controls").after(o),v.push(o[0])),c.options.sniffHtml&&(a="",void 0!==l.attr("pattern")&&(a="Not in the expected format\x3c!-- data-validation-pattern-message to override --\x3e",l.data("validationPatternMessage")&&(a=l.data("validationPatternMessage")),l.data("validationPatternMessage",a),l.data("validationPatternRegex",l.attr("pattern"))),void 0===l.attr("max")&&void 0===l.attr("aria-valuemax")||(a="Too high: Maximum of '"+(n=void 0!==l.attr("max")?l.attr("max"):l.attr("aria-valuemax"))+"'\x3c!-- data-validation-max-message to override --\x3e",l.data("validationMaxMessage")&&(a=l.data("validationMaxMessage")),l.data("validationMaxMessage",a),l.data("validationMaxMax",n)),void 0===l.attr("min")&&void 0===l.attr("aria-valuemin")||(a="Too low: Minimum of '"+(n=void 0!==l.attr("min")?l.attr("min"):l.attr("aria-valuemin"))+"'\x3c!-- data-validation-min-message to override --\x3e",l.data("validationMinMessage")&&(a=l.data("validationMinMessage")),l.data("validationMinMessage",a),l.data("validationMinMin",n)),void 0!==l.attr("maxlength")&&(a="Too long: Maximum of '"+l.attr("maxlength")+"' characters\x3c!-- data-validation-maxlength-message to override --\x3e",l.data("validationMaxlengthMessage")&&(a=l.data("validationMaxlengthMessage")),l.data("validationMaxlengthMessage",a),l.data("validationMaxlengthMaxlength",l.attr("maxlength"))),void 0!==l.attr("minlength")&&(a="Too short: Minimum of '"+l.attr("minlength")+"' characters\x3c!-- data-validation-minlength-message to override --\x3e",l.data("validationMinlengthMessage")&&(a=l.data("validationMinlengthMessage")),l.data("validationMinlengthMessage",a),l.data("validationMinlengthMinlength",l.attr("minlength"))),void 0===l.attr("required")&&void 0===l.attr("aria-required")||(a=c.builtInValidators.required.message,l.data("validationRequiredMessage")&&(a=l.data("validationRequiredMessage")),l.data("validationRequiredMessage",a)),void 0!==l.attr("type")&&"number"===l.attr("type").toLowerCase()&&(a=c.builtInValidators.number.message,l.data("validationNumberMessage")&&(a=l.data("validationNumberMessage")),l.data("validationNumberMessage",a)),void 0!==l.attr("type")&&"email"===l.attr("type").toLowerCase()&&(a="Not a valid email address\x3c!-- data-validator-validemail-message to override --\x3e",l.data("validationValidemailMessage")?a=l.data("validationValidemailMessage"):l.data("validationEmailMessage")&&(a=l.data("validationEmailMessage")),l.data("validationValidemailMessage",a)),void 0!==l.attr("minchecked")&&(a="Not enough options checked; Minimum of '"+l.attr("minchecked")+"' required\x3c!-- data-validation-minchecked-message to override --\x3e",l.data("validationMincheckedMessage")&&(a=l.data("validationMincheckedMessage")),l.data("validationMincheckedMessage",a),l.data("validationMincheckedMinchecked",l.attr("minchecked"))),void 0!==l.attr("maxchecked")&&(a="Too many options checked; Maximum of '"+l.attr("maxchecked")+"' required\x3c!-- data-validation-maxchecked-message to override --\x3e",l.data("validationMaxcheckedMessage")&&(a=l.data("validationMaxcheckedMessage")),l.data("validationMaxcheckedMessage",a),l.data("validationMaxcheckedMaxchecked",l.attr("maxchecked")))),void 0!==l.data("validation")&&(t=l.data("validation").split(",")),m.each(l.data(),function(a,e){a=a.replace(/([A-Z])/g,",$1").split(",");"validation"===a[0]&&a[1]&&t.push(a[1])}),t),i=[];m.each(t,function(a,e){t[a]=u(e)}),t=m.unique(t),i=[],m.each(e,function(a,e){void 0!==l.data("validation"+e+"Shortcut")?m.each(l.data("validation"+e+"Shortcut").split(","),function(a,e){i.push(e)}):c.builtInValidators[e.toLowerCase()]&&"shortcut"===(e=c.builtInValidators[e.toLowerCase()]).type.toLowerCase()&&m.each(e.shortcut.split(","),function(a,e){e=u(e),i.push(e),t.push(e)})}),0<(e=i).length;);var n,s={};m.each(t,function(a,t){var i,n,e=void 0!==(o=l.data("validation"+t+"Message")),r=!1,o=o||"'"+t+"' validation failed \x3c!-- Add attribute 'data-validation-"+t.toLowerCase()+"-message' to input to change this message --\x3e";m.each(c.validatorTypes,function(a,e){void 0===s[a]&&(s[a]=[]),r||void 0===l.data("validation"+t+u(e.name))||(s[a].push(m.extend(!0,{name:u(e.name),message:o},e.init(l,t))),r=!0)}),!r&&c.builtInValidators[t.toLowerCase()]&&(i=m.extend(!0,{},c.builtInValidators[t.toLowerCase()]),e&&(i.message=o),"shortcut"===(n=i.type.toLowerCase())?r=!0:m.each(c.validatorTypes,function(a,e){void 0===s[a]&&(s[a]=[]),r||n!==a.toLowerCase()||(l.data("validation"+t+u(e.name),i[e.name.toLowerCase()]),s[n].push(m.extend(i,e.init(l,t))),r=!0)})),r||m.error("Cannot find validation info for '"+t+"'")}),o.data("original-contents",o.data("original-contents")?o.data("original-contents"):o.html()),o.data("original-role",o.data("original-role")?o.data("original-role"):o.attr("role")),r.data("original-classes",r.data("original-clases")?r.data("original-classes"):r.attr("class")),l.data("original-aria-invalid",l.data("original-aria-invalid")?l.data("original-aria-invalid"):l.attr("aria-invalid")),l.bind("validation.validation",function(a,e){var i=g(l),n=[];return m.each(s,function(t,a){(i||i.length||e&&e.includeEmpty||c.validatorTypes[t].blockSubmit&&e&&e.submitting)&&m.each(a,function(a,e){c.validatorTypes[t].validate(l,i,e)&&n.push(e.message)})}),n}),l.bind("getValidators.validation",function(){return s}),l.bind("submit.validation",function(){if("country"!=l.attr("name")||"country"==l.attr("name")&&void 0!==l.attr("required")&&!1!==l.attr("required")&&(""==l.attr("value")||"0"==l.attr("value")))return l.triggerHandler("change.validation",{submitting:!0})}),n="user_login"==l.attr("name")||"user_email"==l.attr("name")?["blur","change"]:["keyup","blur","click","keydown","keypress","change"],l.bind(n.join(".validation ")+".validation",function(a,i){var e,t=g(l),n=[];r.find("input,textarea,select").each(function(a,e){var t=n.length;m.each(m(e).triggerHandler("validation.validation",i),function(a,e){0==n.length&&n.push(e)}),n.length>t?m(e).attr("aria-invalid","true").addClass("arm_invalid"):(t=void 0!==(t=l.data("original-aria-invalid"))&&t,m(e).attr("aria-invalid",t).addClass("arm_invalid"),0==t&&m(e).removeClass("arm_invalid"))}),d.find("input,select,textarea").not(l).not('[name="'+l.attr("name")+'"]').trigger("validationLostFocus.validation"),(n=m.unique(n.sort())).length?(r.removeClass("success error").addClass("warning"),c.options.semanticallyStrict&&1===n.length?o.html(n[0]+(c.options.prependExistingHelpBlock?o.data("original-contents"):"")):(e='<div class="arm-df__fc--validation__wrap" style="display:block;"><div class="arm_error_box_arrow"></div>'+n.join('</div><div class="arm-df__fc--validation__wrap" style="display:block;"><div class="arm_error_box_arrow"></div>')+"</div>"+(c.options.prependExistingHelpBlock?o.data("original-contents"):""),o.html()!=e&&o.html(e))):(r.removeClass("warning error success"),0<t.length&&r.addClass("success"),o.html(o.data("original-contents"))),"blur"===a.type&&r.removeClass("success")}),l.bind("validationLostFocus.validation",function(){r.removeClass("success")})})},destroy:function(){},collectErrors:function(a){var i={};return this.each(function(a,e){var e=m(e),t=e.attr("name"),e=e.triggerHandler("validation.validation",{includeEmpty:!0});i[t]=m.extend(!0,e,i[t])}),m.each(i,function(a,e){0===e.length&&delete i[a]}),i},hasErrors:function(){var t=[];return this.each(function(a,e){t=t.concat(m(e).triggerHandler("getValidators.validation")?m(e).triggerHandler("validation.validation",{submitting:!0}):[])}),0<t.length},override:function(a){e=m.extend(!0,e,a)}},validatorTypes:{callback:{name:"callback",init:function(a,e){return{validatorName:e,callback:a.data("validation"+e+"Callback"),lastValue:a.val(),lastValid:!0,lastFinished:!0}},validate:function(a,e,t){return t.lastValue===e&&t.lastFinished?!t.lastValid:(!0===t.lastFinished&&(t.lastValue=e,t.lastValid=!0,t.lastFinished=!1,n=a,function(a,e){for(var t=Array.prototype.slice.call(arguments).splice(2),i=a.split("."),n=i.pop(),r=0;r<i.length;r++)e=e[i[r]];e[n].apply(this,t)}((i=t).callback,window,a,e,function(a){i.lastValue===a.value&&(i.lastValid=a.valid,a.message&&(i.message=a.message),i.lastFinished=!0,n.data("validation"+i.validatorName+"Message",i.message),setTimeout(function(){n.trigger("change.validation")},1))})),!1);var i,n}},ajax:{name:"ajax",init:function(a,e){return{validatorName:e,url:a.data("validation"+e+"Ajax"),lastValue:a.val(),lastValid:!0,lastFinished:!0}},validate:function(e,a,t){return""+t.lastValue==""+a&&!0===t.lastFinished?!1===t.lastValid:(!0===t.lastFinished&&(t.lastValue=a,t.lastValid=!0,t.lastFinished=!1,m.ajax({url:t.url,data:"value="+a+"&field="+e.attr("name"),dataType:"json",success:function(a){""+t.lastValue==""+a.value&&(t.lastValid=!!a.valid,a.message&&(t.message=a.message),t.lastFinished=!0,e.data("validation"+t.validatorName+"Message",t.message),setTimeout(function(){e.trigger("change.validation")},1))},failure:function(){t.lastValid=!0,t.message="ajax call failed",t.lastFinished=!0,e.data("validation"+t.validatorName+"Message",t.message),setTimeout(function(){e.trigger("change.validation")},1)}})),!1)}},regex:{name:"regex",init:function(a,e){return{regex:(a=a.data("validation"+e+"Regex"),new RegExp("^"+a+"$"))}},validate:function(a,e,t){return!t.regex.test(e)&&!t.negative||t.regex.test(e)&&t.negative}},required:{name:"required",init:function(a,e){return{}},validate:function(a,e,t){return!(0!==e.length||t.negative)||!!(0<e.length&&t.negative)},blockSubmit:!0},match:{name:"match",init:function(a,e){e=a.parents("form").first().find('[id="'+a.data("validation"+e+"Match")+'"]').first();return e.bind("validation.validation",function(){a.trigger("change.validation",{submitting:!0})}),{element:e}},validate:function(a,e,t){return e!==t.element.val()&&!t.negative||e===t.element.val()&&t.negative},blockSubmit:!0},max:{name:"max",init:function(a,e){return{max:a.data("validation"+e+"Max")}},validate:function(a,e,t){return parseFloat(e,10)>parseFloat(t.max,10)&&!t.negative||parseFloat(e,10)<=parseFloat(t.max,10)&&t.negative}},min:{name:"min",init:function(a,e){return{min:a.data("validation"+e+"Min")}},validate:function(a,e,t){return parseFloat(e)<parseFloat(t.min)&&!t.negative||parseFloat(e)>=parseFloat(t.min)&&t.negative}},maxlength:{name:"maxlength",init:function(a,e){return{maxlength:a.data("validation"+e+"Maxlength")}},validate:function(a,e,t){return 0<e.length&&e.length>t.maxlength&&!t.negative||e.length<=t.maxlength&&t.negative}},minlength:{name:"minlength",init:function(a,e){return{minlength:a.data("validation"+e+"Minlength")}},validate:function(a,e,t){return 0<e.length&&e.length<t.minlength&&!t.negative||e.length>=t.minlength&&t.negative}},maxchecked:{name:"maxchecked",init:function(a,e){var t=a.parents("form").first().find('[name="'+a.attr("name")+'"]');return t.bind("click.validation",function(){a.trigger("change.validation",{includeEmpty:!0})}),{maxchecked:a.data("validation"+e+"Maxchecked"),elements:t}},validate:function(a,e,t){return t.elements.filter(":checked").length>t.maxchecked&&!t.negative||t.elements.filter(":checked").length<=t.maxchecked&&t.negative},blockSubmit:!0},minchecked:{name:"minchecked",init:function(a,e){var t=a.parents("form").first().find('[name="'+a.attr("name")+'"]');return t.bind("click.validation",function(){a.trigger("change.validation",{includeEmpty:!0})}),{minchecked:a.data("validation"+e+"Minchecked"),elements:t}},validate:function(a,e,t){return t.elements.filter(":checked").length<t.minchecked&&!t.negative||t.elements.filter(":checked").length>=t.minchecked&&t.negative},blockSubmit:!0}},builtInValidators:{email:{name:"Email",type:"shortcut",shortcut:"validemail"},validemail:{name:"Validemail",type:"regex",regex:"[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}",message:"Not a valid email address\x3c!-- data-validator-validemail-message to override --\x3e"},passwordagain:{name:"Passwordagain",type:"match",match:"password",message:"Does not match the given password\x3c!-- data-validator-paswordagain-message to override --\x3e"},positive:{name:"Positive",type:"shortcut",shortcut:"number,positivenumber"},negative:{name:"Negative",type:"shortcut",shortcut:"number,negativenumber"},number:{name:"Number",type:"regex",regex:"([+-]?\\d+(\\.\\d*)?([eE][+-]?[0-9]+)?)?",message:"Must be a number\x3c!-- data-validator-number-message to override --\x3e"},integer:{name:"Integer",type:"regex",regex:"[+-]?\\d+",message:"No decimal places allowed\x3c!-- data-validator-integer-message to override --\x3e"},positivenumber:{name:"Positivenumber",type:"min",min:0,message:"Must be a positive number\x3c!-- data-validator-positivenumber-message to override --\x3e"},negativenumber:{name:"Negativenumber",type:"max",max:0,message:"Must be a negative number\x3c!-- data-validator-negativenumber-message to override --\x3e"},required:{name:"Required",type:"required",message:"This is required\x3c!-- data-validator-required-message to override --\x3e"},checkone:{name:"Checkone",type:"minchecked",minchecked:1,message:"Check at least one option\x3c!-- data-validation-checkone-message to override --\x3e"}}},u=function(a){return a.toLowerCase().replace(/(^|\s)([a-z])/g,function(a,e,t){return e+t.toUpperCase()})},g=function(a){var e=a.val(),t=a.attr("type");return"file"===t&&(e=a.parents(".armFileUploadWrapper").find(".arm_file_url").val()),"checkbox"===t?e=a.is(":checked")?e:"":"radio"===t?e=0<m('input[name="'+a.attr("name")+'"]:checked').length?e:"":"text"==t&&(0<a.parents(".arm-df__form-group_select").length&&0==e||0<a.parents(".arm-df__form-group_roles").length&&0==e)&&(e=""),e};m.fn.ARMjqBootstrapValidation=function(a){return e.methods[a]?e.methods[a].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof a&&a?(m.error("Method "+a+" does not exist on jQuery.ARMjqBootstrapValidation"),null):e.methods.init.apply(this,arguments)},m.ARMjqBootstrapValidation=function(a){m(":input").not("[type=image],[type=submit]").ARMjqBootstrapValidation.apply(this,arguments)}}(jQuery);