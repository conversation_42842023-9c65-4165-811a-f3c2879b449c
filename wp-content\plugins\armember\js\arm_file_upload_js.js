function arm_remove_cover(){var e,a=jQuery("#armRemoveCover").parents(".arm_template_container"),t=jQuery("#armRemoveCover").attr("data-cover");jQuery("#armRemoveCover").attr("data-default-cover");""!=t&&(a.css("opacity","0.5"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_remove_uploaded_file&file_name="+t+"&type=profile_cover&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(){e=jQuery("#armRemoveCover").attr("data-default-cover"),a.css("opacity","1"),jQuery(".arm_profile_picture_block").css("background-image","url("+e+"?"+Math.floor(100*Math.random())+")"),jQuery("#armRemoveCover").hide(),jQuery(".arm_delete_cover_popup").removeClass("armopen").slideUp()}}))}function arm_remove_profile(){var e=jQuery("#armRemoveProfilePic").attr("data-cover"),a=jQuery("#armRemoveProfilePic").parents(".arm_user_avatar").find("img");""!=e&&(a.css("opacity","0.5"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_remove_uploaded_file&file_name="+e+"&type=profile_pic&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery(".arm_delete_profile_popup").removeClass("armopen").slideUp(),a.attr("src",e),jQuery(".arm_profile_detail_tbl").find(".arm_user_avatar").find("img").attr("src",e);e=e.split("/"),e=e[Object.keys(e).length-1];return jQuery("#armRemoveProfilePic").attr("data-cover",e),a.parent().find("#armRemoveProfilePic").attr("data-cover",e),a.parent().find("#armRemoveProfilePic").hide(),jQuery("#armRemoveProfilePic").parent().hide(),jQuery("#armRemoveProfilePic").parent().addClass("arm_no_profile"),jQuery("#armRemoveProfilePic").hide(),!1}}))}function hideConfirmBoxCallbackprofile(){jQuery(".arm_delete_profile_popup").removeClass("armopen").slideUp()}function RandomString(e){for(var a=null!=e&&0!=e?e:5,t="",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",o=0;o<a;o++)t+=r.charAt(Math.floor(Math.random()*r.length));return t}jQuery(document).ready(function(e){function C(e){for(var a=null!=e&&0!=e?e:5,t="",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",o=0;o<a;o++)t+=r.charAt(Math.floor(Math.random()*r.length));return t}function Q(e,a){var t,r=jQuery(e).parents(".armFileUploadWrapper").find(".armFileMessages").attr("id");null!=r&&(t=document.getElementById(r),jQuery("#"+r).show(),t.innerHTML=a,jQuery(e).val(""),jQuery("#"+r).addClass("arm-df__fc--validation"))}function r(e){e.stopPropagation(),e.preventDefault();var a=e.target;"dragover"==e.type?jQuery(a).parents(".arm-ffw__file-upload-box").addClass("arm_dragover"):jQuery(a).parents(".arm-ffw__file-upload-box").removeClass("arm_dragover")}function t(e){r(e);var t=e.target,e="",e=void 0===t.files?t&&t.value?[{name:t.value.replace(/^.+\\/,"")}]:[]:t.files;validated_files=0,"null"!=e&&void 0!==e&&(jQuery.each(e,function(e,a){"null"!=a&&void 0!==a&&(is_valid=function(e,a){x=jQuery(a);var t=e.name.lastIndexOf("."),t=e.name.substring(t+1),r=(t=t.toLowerCase(),x.attr("data-avatar-type")),o=x.attr("data-file_size"),i=(x.attr("data-file_type"),1024*o*1024),n=x.attr("data-msg-invalid");isValid=1,r="cover"===r&&"profile"===r?["php","php2","php3","php4","php5","pl","py","jsp","asp","exe","cgi","gif","GIF"]:["php","php2","php3","php4","php5","pl","py","jsp","asp","exe","cgi"];-1<r.indexOf(t)&&(isValid=0);null==n&&(l=n=n.replace("{invalid_file}",e.name),n=invalidFileTypeError);var r=x.attr("accept"),d=[];null!=r&&(d=r.replace(/\./g,"").split(","),l=n=n.replace("{invalid_file}",e.name),-1===jQuery.inArray(t,d)&&(isValid=(s=jQuery(a).parents(".arm-df__form-field").find(".arm-df__fc--validation"),jQuery(a).val(""),jQuery(s).html("<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+l+"</div>"),jQuery(s).show(),0)));{var l,s;e.size>i&&(n=n.replace("{invalid_file}",e.name),l=(l=(l=n).replace("{SIZE}",o+"MB")).replace("{size}",o+"MB"),s=jQuery(a).parents(".arm-df__form-field").find(".arm-df__fc--validation"),jQuery(a).val(""),jQuery(s).html("<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+l+"</div>"),jQuery(s).show(),isValid=0)}return isValid}(a,t))&&(validated_files+=1)}),validated_files==e.length&&jQuery.each(e,function(e,a){"null"!=a&&void 0!==a&&(o(a,t),i(a,t))}))}function o(r,e){var a,e=jQuery(e),o=e.parents(".armFileUploadWrapper"),t=o.find(".arm-ffw__file-upload-box"),e=e.attr("data-file_type"),i=r.name.lastIndexOf(".");r.name.substring(i+1).toLowerCase();window.FileReader?"badges"==e||"social_icon"==e?o.find(".arm_old_file").html(""):(0==r.type.indexOf("image")?(a=new FileReader).onload=function(e){var a=r.name,t=(t=(t='<div class="arm_file_preview_container" id="'+(a=a.split(".")[0])+'" style="position:relative;">')+'<div class="arm_uploaded_file_info" id="arm_uploaded_file_info" style="position:relative;">'+('<img src="'+e.target.result+'"/>'))+"</div>"+('<div class="armFileRemoveContainer" data-id="'+a+'">x</div></div>');o.find(".arm_old_file.arm_field_file_display").append(t)}:(a=new FileReader).onload=function(e){var a=r.name,t='<div class="arm_file_preview_container" id="'+(a=a.split(".")[0])+'">',t=(t=(t+='<div class="arm_uploaded_file_info">')+('<img src="'+__ARMIMAGEURL+'/file_icon.png"/>')+('<span class="arm_uploaded_file_name">'+a+"</span>"))+"</div>"+('<div class="armFileRemoveContainer" data-id="'+a+'">x</div></div>');o.find(".arm_old_file.arm_field_file_display").append(t)},a.readAsDataURL(r)):o.find(".arm_old_file").html(""),t.find(".armbar").css("width","0%").show(),t.find(".armFileDragAreaText").hide()}function i(o,c){var i,p=!1,m=(x=jQuery(c)).attr("data-avatar-type"),u=(l=x.attr("data-update-meta"),x.parents(".armFileUploadWrapper")),f=u.find(".armFileBtn"),h=u.find(".armFileUploadContainer"),_=u.find(".arm-ffw__file-upload-box"),g=u.find(".armFileRemoveContainer"),v=u.find(".armFileUploadProgressBar"),y=u.find(".armFileUploadProgressInfo"),b=x.parents("form").attr("data-random-id"),n=x.attr("name"),d=x.attr("data-file_type"),e=o.name.lastIndexOf("."),w=(w=o.name.substring(e+1)).toLowerCase(),j=(-1<(i="cover"===m&&"profile"===m?["php","php3","php4","php5","pl","py","jsp","asp","exe","cgi","gif","GIF"]:["php","php3","php4","php5","pl","py","jsp","asp","exe","cgi"]).indexOf(w)&&(isValid=0),new XMLHttpRequest),e=new FileReader;e.onload=function(e){var l,s,e=e.target.result,e=Base64.decode(e.replace(/data\:image\/(.*?)\;base64\,/,"")),a=new RegExp("(\\<\\?php)","g"),t=new RegExp("\\<script","g"),r=new RegExp("\\<iframe","g");if(0==o.type.indexOf("image")&&(a.test(e)||t.test(e)||r.test(e)))return Q(c,"<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div> This file could not be processed due to security reason as this may contains malicious code.</div>"),x.val(""),x.attr("value",""),x.trigger("change"),setTimeout(function(){u.find(".arm_old_file.arm_field_file_display").html(""),_.find(".armFileDragAreaText").show()},200),!1;-1===jQuery.inArray(w,i)&&(j.upload?(a=C(5),t=o.name.lastIndexOf("."),r=o.name.substring(t+1),e=/.*(?=\.)/.exec(o.name),l=(t="badges"==d?"arm_badges_"+a+"_"+e:"social_icon"==d?"arm_icon_"+a+"_"+e:"armFile"+a+"_"+e).replace(/[^\w\s]/gi,"").replace(/ /g,"")+"."+r,s=t.replace(/[^\w\s]/gi,"").replace(/ /g,""),v.show(),v.find(".armbar").css("width","0%"),_.find(".armbar").css("width","0%").show(),u.find(".armFileMessages").html(""),y.html(""),j.upload.addEventListener("progress",function(e){e=parseFloat(0+e.loaded/e.total*100),e=Math.round(e);u.find(".armbar").css("width",e+"%"),v.find(".armbar").css("width",e+"%"),y.html('<span class="armFileName" style="float:left">'+l+'</span><span class="progressinfo_perc">'+e+"%</span>")},!1),j.addEventListener("load",function(e){},!1),jQuery("#arm_form"+b).find(".arm-df__form-field-wrap_submit .arm-df__form-control-submit-btn").attr("disabled","disabled"),u.find(".arm-ffw__file-upload-box").css("min-height","52px"),u.append('<div class="arm_loading_spinner_main" data_id="'+b+'"><div class="arm_loading_spinner"></div></div>'),a=new FormData,"badges"==d?(j.open("POST",__ARMAJAXURL,!0),a.append("action","arm_upload_badge")):"social_icon"==d?(j.open("POST",__ARMAJAXURL,!0),a.append("action","arm_upload_social_icon")):(j.open("POST",__ARMAJAXURL,!0),a.append("action","arm_upload_front")),j.setRequestHeader("FILEMETANAME",n),j.setRequestHeader("X_FILENAME",l),j.setRequestHeader("X-FILENAME",l),a.append("armfileselect",o),a.append("_wpnonce",jQuery('input[name="arm_wp_nonce"]').val()),j.send(a),p=!1,j.onreadystatechange=function(e){if(4==j.readyState&&200==j.status){var a,t,r=u.find("input.arm_file_url").val(),o=u.find("input.arm_file_url").attr("data-icon"),i=j.responseText,n=(-1==i.indexOf("http://")&&-1==i.indexOf("https://")||(i="//"+i.split("://")[1]),""!=r?(r=r+","+i,l=o+","+l):r=i,u.find(".arm_file_preview_container").attr("id",s),u.find(".armFileRemoveContainer").attr("data-id",s),jQuery("#arm_form"+b).find(".arm-df__form-field-wrap_submit .arm-df__form-control-submit-btn").removeAttr("disabled"),u.find('.arm_loading_spinner_main[data_id="'+b+'"]').remove(),u.find("input.arm_file_url").attr("data-icon",l),u.find("input.arm_file_url").val(r),u.find(".armUploadedFileName").val(l),r.split(","));if(1<n)for(var d=0;d<n.length;++d)u.find(".arm_image_file_preview").append('<img alt="Uploaded File" src="'+n[d]+'"/>');else u.find(".arm_image_file_preview").html('<img alt="Uploaded File" src="'+i+'"/>');h.hide(),f.hide(),g.show(),_.find(".armFileDragAreaText").hide(),setInterval(function(){v.hide(),y.find(".progressinfo_perc").hide()},1500),"cover"===m?0!=jQuery('.arm_crop_cover_div[data_id="'+b+'"]').length&&(jQuery('.arm_crop_cover_div[data_id="'+b+'"]').find("img").attr("src",""),jQuery('.arm_crop_cover_div[data_id="'+b+'"]').find("img").attr("src",i),o=jQuery('.arm_crop_cover_image[data_id="'+b+'"]'),a=0,M=new Cropper(o,{viewMode:4,aspectRatio:2.86875,dragCrop:!0,zoomable:!0,rotatable:!0,dashed:!1,cropBoxResizable:!1,dragMode:"move",zoomOnTouch:!1,crop:function(e){jQuery('.arm_crop_cover_image[data_id="'+b+'"]').attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==a&&(jQuery(".arm_clear_cover_button.arm_img_cover_setting").trigger("click"),jQuery('.arm_crop_cover_image[data_id="'+b+'"]').attr("data-coord","0,0,0,0"),a=1)},background:!1}),jQuery(".arm_img_cover_setting[data_id='"+b+"']").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(M){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=M.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&M.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=M[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&M.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":M=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==M&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),jQuery('.arm_crop_cover_div_wrapper[data_id="'+b+'"]').bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,zIndex:9999999,onClose:function(){h.show(),f.show(),v.hide(),v.find(".armbar").css("width","0%"),y.html(""),u.find("input:not(.wpnonce)").val(""),u.find("input.arm_file_url").attr("data-icon",""),x.val(""),x.attr("value",""),x.trigger("change"),u.find(".armFileMessages").html(""),u.find(".arm_image_file_preview").html(""),u.find(".arm_old_file").html(""),u.find(".arm_old_uploaded_file").html(""),g.hide(),_.find(".armFileDragAreaText").show(),_.find(".armbar").css("width","0%").hide();var e=u.attr("data-iframe");jQuery("#"+e+"_iframe_div").html(" ").append('<iframe id="'+e+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'),M.destroy()}})):"profile"===m&&0!=jQuery('.arm_crop_div[data_id="'+b+'"]').length&&(jQuery('.arm_crop_div[data_id="'+b+'"]').find("img").attr("src",""),jQuery('.arm_crop_div[data_id="'+b+'"]').find("img").attr("src",i),r=jQuery('.arm_crop_image[data_id="'+b+'"]'),t=0,M=new Cropper(r,{viewMode:4,aspectRatio:1,dragCrop:!1,zoomable:!0,rotatable:!0,dashed:!1,cropBoxResizable:!1,dragMode:"move",zoomOnTouch:!1,crop:function(e){jQuery('.arm_crop_image[data_id="'+b+'"]').attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==t&&(jQuery('.arm_clear_button.arm_img_setting[data_id="'+b+'"]').trigger("click"),jQuery('.arm_crop_image[data_id="'+b+'"]').attr("data-coord","0,0,0,0"),t=1)},background:!1}),jQuery(".arm_img_setting[data_id='"+b+"']").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(M){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=M.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&M.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=M[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&M.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":M=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==M&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),jQuery('.arm_crop_div_wrapper[data_id="'+b+'"]').bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,zIndex:9999999,onClose:function(){h.show(),f.show(),v.hide(),v.find(".armbar").css("width","0%"),y.html(""),u.find("input:not(.wpnonce)").val(""),u.find("input.arm_file_url").attr("data-icon",""),x.val(""),x.attr("value",""),x.trigger("change"),u.find(".armFileMessages").html(""),u.find(".arm_image_file_preview").html(""),u.find(".arm_old_file").html(""),u.find(".arm_old_uploaded_file").html(""),g.hide(),_.find(".armFileDragAreaText").show(),_.find(".armbar").css("width","0%").hide();var e=u.attr("data-iframe");jQuery("#"+e+"_iframe_div").html(" ").append('<iframe id="'+e+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'),M.destroy()}})),p=!1}else 200!=j.status&&(Q(c,"<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+fileUploadError+"</div>"),u.find('.arm_loading_spinner_main[data_id="'+b+'"]').remove(),x.val(""),x.attr("value",""),x.trigger("change"),h.show(),f.show(),v.hide(),v.find(".armbar").css("width","0%"),y.html(""),u.find("input").val(""),u.find("input.arm_file_url").attr("data-icon",""),u.find(".arm_old_file").html(""),u.find(".arm_old_uploaded_file").html(""),g.hide(),_.find(".armFileDragAreaText").show(),_.find(".armbar").css("width","0%").hide())}):Q(c,"<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+fileUploadError+"</div>")),p&&(u.find('.arm_loading_spinner_main[data_id="'+b+'"]').remove(),x.val(""),x.attr("value",""),x.trigger("change"),h.show(),f.show(),v.hide(),v.find(".armbar").css("width","0%"),y.html(""),u.find("input").val(""),u.find(".arm_old_file").html(""),u.find(".arm_old_uploaded_file").html(""),g.hide(),_.find(".armFileDragAreaText").show(),_.find(".armbar").css("width","0%").hide())},e.readAsDataURL(o)}function n(e){r(e),$this=e.target;var a=jQuery(e.target).attr("data-id"),t=jQuery("#"+a),a=e.target.files||e.dataTransfer.files;"null"!=a&&void 0!==a&&jQuery.each(a,function(e,a){o(a,t),i(a,t)})}var l="",x="",M="";jQuery(document).on("click","#arm_skip_avtr_crop_nav_front",function(){var a,e,t,r,o;""!=M&&null!=(a=jQuery(this).attr("data_id"))&&(jQuery(this).attr("disabled","disabled"),e=jQuery('input[name="arm_wp_nonce"]').val(),t=jQuery('.arm_crop_image[data_id="'+a+'"]').attr("src"),r=jQuery('.arm_crop_image[data_id="'+a+'"]').attr("data-rotate"),o=jQuery("#arm_crop_image[data_id='"+a+"']").attr("data-coord"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=profile&src="+t+"&update_meta="+l+"&rotate="+r+"&cord="+o+"&_wpnonce="+e,success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)).on("load",function(){x.parents(".arm-df__form-field").find(".arm_uploaded_file_info img").attr("src",e+"?"+Math.floor(100*Math.random()+1))});jQuery('.arm_crop_div_wrapper[data_id="'+a+'"]').bPopup().close(),setTimeout(function(){jQuery('.arm_crop_button[data_id="'+a+'"]').removeAttr("disabled")},500),jQuery("#arm_skip_avtr_crop_nav_front").removeAttr("disabled"),M.destroy(),jQuery('.arm_crop_image[data_id="'+a+'"]').attr("data-rotate",0)}}))}),jQuery(document).on("click","#arm_skip_cvr_crop_nav_front",function(){var e,a,t,r,o;""!=M&&null!=(e=jQuery(this).attr("data_id"))&&(jQuery(this).attr("disabled","disabled"),a=jQuery('input[name="arm_wp_nonce"]').val(),t=jQuery('.arm_crop_cover_image[data_id="'+e+'"]').attr("src"),r=jQuery('.arm_crop_cover_image[data_id="'+e+'"]').attr("data-coord"),o=jQuery('.arm_crop_cover_image[data_id="'+e+'"]').attr("data-rotate"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+t+"&cord="+r+"&update_meta="+l+"&rotate="+o+"&_wpnonce="+a,success:function(a){jQuery("<img />").attr("src",a+"?"+Math.floor(100*Math.random()+1)).on("load",function(){x.parents(".arm-df__form-field").find(".arm_uploaded_file_info img").attr("src",a+"?"+Math.floor(100*Math.random()+1)),jQuery(".arm_profile_picture_block").css("background-image","url("+a+"?"+Math.floor(100*Math.random())+")");var e=x.attr("id");jQuery(".arm_default_cover_photo_wrapper").hide(),jQuery(".arm_remove_default_cover_photo_wrapper").show(),jQuery("#"+e+"_hidden").val(a)});jQuery('.arm_crop_cover_div_wrapper[data_id="'+e+'"]').bPopup().close(),setTimeout(function(){jQuery('.arm_crop_cover_button[data_id="'+e+'"]').removeAttr("disabled")},500),jQuery("#arm_skip_cvr_crop_nav_front").removeAttr("disabled"),M.destroy(),jQuery('.arm_crop_cover_image[data_id="'+e+'"]').attr("data-rotate",0)}}))});jQuery(".arm_rotate_button").on("click",function(){var e=jQuery("#arm_crop_image").attr("data-rotate"),e=parseFloat(e)+90;e%=360,jQuery("#arm_crop_image").attr("data-rotate",e)}),jQuery("#arm_crop_div_wrapper_close, .arm_reset_button").on("click",function(){jQuery("#arm_crop_image").attr("data-rotate",0)}),jQuery(".arm_rotate_cover_button").on("click",function(){var e=jQuery("#arm_crop_cover_image").attr("data-rotate"),e=parseFloat(e)+90;e%=360,jQuery("#arm_crop_cover_image").attr("data-rotate",e)}),jQuery("#arm_crop_cover_div_wrapper_close, .arm_reset_cover_button").on("click",function(){jQuery("#arm_crop_cover_image").attr("data-rotate",0)}),window.File&&window.FileList&&window.FileReader&&(new XMLHttpRequest).upload&&jQuery(".armFileUploaderWrapper").each(function(e,a){var t=jQuery(this).attr("id");""!=t&&null!=t&&((t=document.getElementById(t)).addEventListener("dragover",r,!1),t.addEventListener("dragleave",r,!1),t.addEventListener("drop",n,!1),t.style.display="block")}),jQuery(document).on("click",".arm-df__file-upload-control:not(.armIEFileUpload)",function(e){if(void 0!==jQuery(this).attr("readonly"))return!1;var a=jQuery(this).attr("id");""!=a&&null!=a&&document.getElementById(a).addEventListener("change",t,!1)}),jQuery(document).on("click",".armIEFileUpload",function(e){if(void 0!==jQuery(this).attr("readonly"))return!1;var i,n,d,s,c,p,m,u,a,f,t,h,_,r,o,g,v=jQuery(this),y=v.attr("data-iframe"),l=jQuery("#"+y+"_iframe"),b="",w=(l.contents().find("#armfileselect").click(),l.contents().find("#armfileselect").val());""!=w&&void 0!==w&&(i=v.parents(".armFileUploadWrapper"),n=i.find(".armFileBtn"),d=i.find(".armFileUploadContainer"),s=i.find(".armFileRemoveContainer"),c=i.find(".armFileUploadProgressBar"),p=i.find(".armFileUploadProgressInfo"),m=v.parents("form").attr("data-random-id"),u=v.attr("data-file_size"),a=v.attr("data-file_type"),f=v.attr("data-avatar-type"),b=v.attr("data-update-meta"),t=1024*u*1024,null==(h=v.attr("data-msg-invalid"))&&(h=invalidFileTypeError),w=w.replace(/C:\\fakepath\\/i,""),r=C(6),o=w.lastIndexOf("."),o=w.substring(o+1),w=/.*(?=\.)/.exec(w),_=("badges"==a?"arm_badges_"+r+"_"+w:"social_icon"==a?"arm_icon_"+r+"_"+w:"armFile"+r+"_"+w).replace(/[^\w\s]/gi,"").replace(/ /g,"")+"."+o,r=o.toLowerCase(),w=[],null!=(o=v.attr("accept"))&&(w=o.replace(/\./g,"").split(","),-1===jQuery.inArray(r,w)&&(v.val(_),v.attr("value",_),v.trigger("change"))),-1===jQuery.inArray(r,["php","php3","php4","php5","pl","py","jsp","asp","exe","cgi"])&&("badges"==a?l.contents().find("form").attr("action",__ARMAJAXURL+"?action=arm_upload_badge&fname="+_+"&allow_size="+t):"social_icon"==a?l.contents().find("form").attr("action",__ARMAJAXURL+"?action=arm_upload_social_icon&fname="+_+"&allow_size="+t):l.contents().find("form").attr("action",__ARMAJAXURL+"?action=arm_upload_front&fname="+_+"&allow_size="+t),l.contents().find("form").submit(),i.find(".armUploadedFileName").val(_),d.hide(),n.hide(),i.find(".armFileMessages").html(""),c.show(),c.find(".armbar").css("width","0%"),p.html(""),c.find(".armbar").animate({width:"100%"},"slow"),p.html('<span class="armFileName" style="float:left">'+_+'</span><span class="progressinfo_perc">100%</span>'),s.show(),g=setInterval(function(){var a,t,l,e,r=jQuery("#"+y+"_iframe").contents().find(".uploaded").length,r=(v.val(_),v.attr("value",_),0<r&&(clearInterval(g),r=jQuery("#"+y+"_iframe").contents().find(".uploaded").html(),i.find(".armUploadedFileName").val(_),i.find("input.arm_file_url").attr("data-icon",_),i.find("input.arm_file_url").val(r),jQuery("#"+y+"_iframe_div").html(" ").append('<iframe id="'+y+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'),v.trigger("change"),setInterval(function(){c.hide(),p.find(".progressinfo_perc").hide()},1500),"profile"==f?(-1==r.indexOf("http://")&&-1==r.indexOf("https://")||(o="//"+r.split("://")[1]),0!=jQuery('.arm_crop_div[data_id="'+m+'"]').length&&(jQuery('.arm_crop_div[data_id="'+m+'"]').find("img").attr("src",""),jQuery('.arm_crop_div[data_id="'+m+'"]').find("img").attr("src",o),e=jQuery('.arm_crop_image[data_id="'+m+'"]'),a=0,l=new Cropper(e,{viewMode:4,aspectRatio:1,dragCrop:!1,zoomable:!0,rotatable:!0,dashed:!1,cropBoxResizable:!1,dragMode:"move",zoomOnTouch:!1,crop:function(e){jQuery("#arm_crop_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==a&&(jQuery('.arm_clear_button.arm_img_setting[data_id="'+m+'"]').trigger("click"),jQuery('#arm_crop_image[data_id="'+m+'"]').attr("data-coord","0,0,0,0"),a=1)},background:!1}),jQuery(".arm_rotate_button").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(l){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=l.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&l.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=l[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&l.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":l=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==l&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),jQuery('.arm_crop_div_wrapper[data_id="'+m+'"]').bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,zIndex:9999999,onClose:function(){l.destroy()}}),jQuery(document).on("click",'.arm_inht_front_usr_avtr label[data_id="'+m+'"]',function(){var e,a,t=jQuery(this).attr("data_id");null!=t&&(e=jQuery('input[name="arm_wp_nonce"]').val(),jQuery(this).attr("disabled","disabled"),a=jQuery("#arm_crop_image").attr("src"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=profile&src="+a+"&update_meta="+b+"&_wpnonce="+e,success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)).on("load",function(){v.parents(".arm-df__form-field").find(".arm_uploaded_file_info img").attr("src",e+"?"+Math.floor(100*Math.random()+1))});jQuery('.arm_crop_div_wrapper[data_id="'+t+'"]').bPopup().close(),jQuery('.arm_inht_front_usr_avtr label[data_id="'+m+'"]').removeAttr("disabled"),jQuery(this).removeAttr("disabled"),l.destroy()}}))}),document.getElementsByClassName("arm_crop_button")[0].onclick=function(){var e=jQuery('input[name="arm_wp_nonce"]').val(),a=jQuery("#arm_crop_image").attr("data-coord"),t=jQuery("#arm_crop_image").attr("src"),r=jQuery("#arm_crop_image").attr("data-rotate");jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=profile&src="+t+"&cord="+a+"&rotate="+r+"&_wpnonce="+e,success:function(e){jQuery("#arm_crop_div_wrapper").bPopup().close(),l.destroy(),jQuery("#arm_crop_image").attr("data-rotate",0)}})})):"cover"==f&&(-1==r.indexOf("http://")&&-1==r.indexOf("https://")||(o="//"+r.split("://")[1]),0!=jQuery('.arm_crop_cover_div[data_id="'+m+'"]').length&&(jQuery('.arm_crop_cover_div[data_id="'+m+'"]').find("img").attr("src",""),jQuery('.arm_crop_cover_div[data_id="'+m+'"]').find("img").attr("src",o),e=jQuery('.arm_crop_cover_image[data_id="'+m+'"]'),t=0,l=new Cropper(e,{viewMode:4,aspectRatio:2.86875,zoomable:!0,rotatable:!0,dashed:!1,dragMode:"move",cropBoxResizable:!1,background:!1,zoomOnTouch:!1,crop:function(e){jQuery("#arm_crop_cover_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==t&&(jQuery(".arm_clear_cover_button.arm_img_cover_setting").trigger("click"),jQuery("#arm_crop_cover_image").attr("data-coord","0,0,0,0"),t=1)}}),jQuery(".arm_img_cover_setting").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(l){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=l.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&l.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=l[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&l.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":l=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==l&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),jQuery('.arm_crop_cover_div_wrapper[data_id="'+m+'"]').bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,zIndex:9999999,onClose:function(){l.destroy()}}),jQuery(document).on("click",'.arm_inht_front_usr_cvr label[data_id="'+m+'"]',function(){var e,a,t;null!=jQuery(this).attr("data_id")&&(e=jQuery('input[name="arm_wp_nonce"]').val(),jQuery(this).attr("disabled","disabled"),a=jQuery("#arm_crop_cover_image").attr("src"),t=jQuery("#arm_crop_cover_image").attr("data-rotate"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+a+"&update_meta="+b+"&rotate="+t+"&_wpnonce="+e,success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)).on("load",function(){});jQuery('.arm_crop_cover_div_wrapper[data_id="'+m+'"]').bPopup().close(),jQuery('.arm_inht_front_usr_cvr label[data_id="'+m+'"]').removeAttr("disabled"),jQuery(this).removeAttr("disabled"),l.destroy(),jQuery("#arm_crop_cover_image").attr("data-rotate",0)}}))}),document.getElementsByClassName("arm_crop_cover_button")[0].onclick=function(){var e=jQuery('input[name="arm_wp_nonce"]').val(),a=jQuery("#arm_crop_cover_image").attr("data-coord"),t=jQuery("#arm_crop_cover_image").attr("src"),r=jQuery("#arm_crop_cover_image").attr("data-rotate");jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+t+"&cord="+a+"&rotate="+r+"&_wpnonce="+e,success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)).on("load",function(){jQuery(".arm_profile_picture_block").css("background-image","url("+e+"?"+Math.floor(100*Math.random())+")")});jQuery("#arm_crop_cover_div_wrapper").bPopup().close(),jQuery("#armRemoveCover").show(),jQuery("#arm_crop_cover_image").attr("src",""),l.destroy(),jQuery("#arm_crop_cover_image").attr("data-rotate",0)}})}))),jQuery("#"+y+"_iframe").contents().find(".error_upload").length),o=jQuery("#"+y+"_iframe").contents().find(".error_upload_size").length;(0<r||0<o)&&(clearInterval(g),0<r&&Q(v,"<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+fileUploadError+"</div>"),0<o&&(e=(e=(e=h+"("+file.name+")").replace("{SIZE}",u+"MB")).replace("{size}",u+"MB"),Q(v,"<div class='arm_error_msg arm-df__fc--validation__wrap' style='opacity: 1;display:block;'><div class='arm_error_box_arrow'></div>"+e+"</div>")),v.val(""),v.attr("value",""),v.trigger("change"),d.show(),n.show(),c.hide(),c.find(".armbar").css("width","0%"),p.html(""),i.find("input").val(""),i.find(".arm_old_file").html(""),i.find(".arm_old_uploaded_file").html(""),s.hide(),jQuery("#"+y+"_iframe_div").html(" ").append('<iframe id="'+y+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'))},500)))}),jQuery(document).on("click",".armFileRemoveContainer",function(e){var i=jQuery(this).parents(".armFileUploadWrapper"),n=jQuery(this).attr("data-id"),d=i.find(".armFileBtn"),l=i.find(".arm-df__file-upload-control"),a=l.attr("name"),s=i.find(".armFileUploadContainer"),c=i.find(".arm-ffw__file-upload-box"),p=i.find(".armFileUploadProgressBar"),m=i.find(".armFileUploadProgressInfo"),t=m.find(".armFileName").text(),u=i.find(".arm_file_url").val(),f=u,r="",o=i.find(".arm_file_url").attr("data-file_type");if("badges"===o||"directory_cover"===o)return jQuery(".armFileRemoveContainer").hide(),jQuery(".arm_old_file").hide(),jQuery(".armFileUploadProgressInfo").hide(),jQuery(".armFileUploadProgressBar").hide(),jQuery(".armFileUploadContainer").show(),jQuery("#arm_badges_icon").val(""),"directory_cover"===o&&(jQuery(".arm_default_cover_image_url").val(""),i.find(".arm_old_file").html(""),i.find(".arm_old_uploaded_file").html(""),i.find(".armFileUploadProgressInfo span").html(""),jQuery(".armFileUploadProgressInfo").show()),!1;for(var h=u.split(","),e=0;e<h.length;e++){var _=h[e];if(-1!=_.indexOf(n)){f=jQuery.grep(h,function(e){return e!=_}),r=_,t=_.substring(_.lastIndexOf("/")+1);break}}jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_remove_uploaded_file&file_name="+t+"&meta_name="+a+"&file_url="+r+"&type="+o+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){s.show(),d.show(),p.hide(),p.find(".armbar").css("width","0%"),m.html("");for(var a=u.split(","),t=0;t<a.length;t++){var r=a[t];if(-1!=r.indexOf(n)){f=jQuery.grep(a,function(e){return e!=r}),i.find("input.arm_file_url").val(f),l.attr("value",f),l.trigger("change");break}}i.find(".armFileMessages").html(""),i.find(".arm_old_file").find(".arm_file_preview_container#"+n).remove(),""!=f&&0!=i.find(".arm_old_file").find(".arm_file_preview_container").length||(i.find(".arm_old_file").html(""),c.find(".armFileDragAreaText").show()),c.find(".armbar").css("width","0%").hide();var o=i.attr("data-iframe");jQuery("#"+o+"_iframe_div").html(" ").append('<iframe id="'+o+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>')}})})}),function(e,a){if("object"==typeof exports&&"object"==typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var t,r=a();for(t in r)("object"==typeof exports?exports:e)[t]=r[t]}}(this,function(){return r=[function(e,a,t){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(a,"__esModule",{value:!0});var o=function(e,a,t){return a&&i(e.prototype,a),t&&i(e,t),e};function i(e,a){for(var t=0;t<a.length;t++){var r=a[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var n=r(t(1)),c=r(t(2)),d=r(t(3)),l=r(t(5)),s=r(t(6)),p=r(t(7)),m=r(t(8)),u=r(t(9)),f=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4)),h="cropper",_=h+"-hidden",g="error",v="load",y=/^data:/,b=void 0,t=(o(w,[{key:"init",value:function(){var e=this,a=e.element,t=a.tagName.toLowerCase(),r=void 0;if(!f.getData(a,h)){if(f.setData(a,h,e),"img"===t){if(e.isImg=!0,e.originalUrl=r=a.getAttribute("src"),!r)return;r=a.src}else"canvas"===t&&window.HTMLCanvasElement&&(r=a.toDataURL());e.load(r)}}},{key:"load",value:function(e){var a,t=this,r=t.options,o=t.element;e&&(t.url=e,t.imageData={},r.checkOrientation&&window.ArrayBuffer?y.test(e)?t.read(f.dataURLToArrayBuffer(e)):((a=new XMLHttpRequest).onerror=a.onabort=function(){t.clone()},a.onload=function(){t.read(a.response)},r.checkCrossOrigin&&f.isCrossOriginURL(e)&&o.crossOrigin&&(e=f.addTimestamp(e)),a.open("get",e),a.responseType="arraybuffer",a.send()):t.clone())}},{key:"read",value:function(e){var a=this.options,t=f.getOrientation(e),r=this.imageData,o=0,i=1,n=1;if(1<t)switch(this.url=f.arrayBufferToDataURL(e),t){case 2:i=-1;break;case 3:o=-180;break;case 4:n=-1;break;case 5:o=90,n=-1;break;case 6:o=90;break;case 7:o=90,i=-1;break;case 8:o=-90}a.rotatable&&(jQuery("#arm_crop_image").attr("data-rotate",o),jQuery("#arm_crop_cover_image").attr("data-rotate",o)),a.scalable&&(r.scaleX=i,r.scaleY=n),this.clone()}},{key:"clone",value:function(){var e=this,a=e.element,t=e.url,r=void 0,o=void 0,i=(e.options.checkCrossOrigin&&f.isCrossOriginURL(t)&&(o=(r=a.crossOrigin)?t:(r="anonymous",f.addTimestamp(t))),e.crossOrigin=r,e.crossOriginUrl=o,f.createElement("img"));r&&(i.crossOrigin=r),i.src=o||t,e.image=i,e.onStart=r=f.proxy(e.start,e),e.onStop=o=f.proxy(e.stop,e),e.isImg?a.complete?e.start():f.addListener(a,v,r):(f.addListener(i,v,r),f.addListener(i,g,o),f.addClass(i,"cropper-hide"),a.parentNode.insertBefore(i,a.nextSibling))}},{key:"start",value:function(e){var t=this,a=t.isImg?t.element:t.image;e&&(f.removeListener(a,v,t.onStart),f.removeListener(a,g,t.onStop)),f.getImageSize(a,function(e,a){f.extend(t.imageData,{naturalWidth:e,naturalHeight:a,aspectRatio:e/a}),t.loaded=!0,t.build()})}},{key:"stop",value:function(){var e=this.image;f.removeListener(e,v,this.onStart),f.removeListener(e,g,this.onStop),f.removeChild(e),this.image=null}},{key:"build",value:function(){var e,a,t,r,o,i=this,n=i.options,d=i.element,l=i.image,s=void 0;i.loaded&&(i.ready&&i.unbuild(),(o=f.createElement("div")).innerHTML=c.default,i.container=s=d.parentNode,i.cropper=o=f.getByClass(o,"cropper-container")[0],i.canvas=e=f.getByClass(o,"cropper-canvas")[0],i.dragBox=a=f.getByClass(o,"cropper-drag-box")[0],i.cropBox=t=f.getByClass(o,"cropper-crop-box")[0],i.viewBox=f.getByClass(o,"cropper-view-box")[0],i.face=r=f.getByClass(t,"cropper-face")[0],f.appendChild(e,l),f.addClass(d,_),s.insertBefore(o,d.nextSibling),i.isImg||f.removeClass(l,"cropper-hide"),i.initPreview(),i.bind(),n.aspectRatio=Math.max(0,n.aspectRatio)||NaN,n.viewMode=Math.max(0,Math.min(3,Math.round(n.viewMode)))||0,n.autoCrop?(i.cropped=!0,n.modal&&f.addClass(a,"cropper-modal")):f.addClass(t,_),n.guides||f.addClass(f.getByClass(t,"cropper-dashed"),_),n.center||f.addClass(f.getByClass(t,"cropper-center"),_),n.background&&f.addClass(o,"cropper-bg"),n.highlight||f.addClass(r,"cropper-invisible"),n.cropBoxMovable&&(f.addClass(r,"cropper-move"),f.setData(r,"action","all")),n.cropBoxResizable||(f.addClass(f.getByClass(t,"cropper-line"),_),f.addClass(f.getByClass(t,"cropper-point"),_)),i.setDragMode(n.dragMode),i.render(),i.ready=!0,i.setData(n.data),setTimeout(function(){f.isFunction(n.ready)&&f.addListener(d,"ready",n.ready,!0),f.dispatchEvent(d,"ready"),f.dispatchEvent(d,"crop",i.getData()),i.complete=!0},0))}},{key:"unbuild",value:function(){var e=this;e.ready&&(e.ready=!1,e.complete=!1,e.initialImageData=null,e.initialCanvasData=null,e.initialCropBoxData=null,e.containerData=null,e.canvasData=null,e.cropBoxData=null,e.unbind(),e.resetPreview(),e.previews=null,e.viewBox=null,e.cropBox=null,e.dragBox=null,e.canvas=null,e.container=null,f.removeChild(e.cropper),e.cropper=null)}}],[{key:"noConflict",value:function(){return window.Cropper=b,w}},{key:"setDefaults",value:function(e){f.extend(n.default,f.isPlainObject(e)&&e)}}]),w);function w(e,a){if(!(this instanceof w))throw new TypeError("Cannot call a class as a function");var t=this;t.element=e[0],t.options=f.extend({},n.default,f.isPlainObject(a)&&a),t.loaded=!1,t.ready=!1,t.complete=!1,t.rotated=!1,t.cropped=!1,t.disabled=!1,t.replaced=!1,t.limited=!1,t.wheeling=!1,t.isImg=!1,t.originalUrl="",t.canvasData=null,t.cropBoxData=null,t.previews=null,t.init()}f.extend(t.prototype,d.default),f.extend(t.prototype,l.default),f.extend(t.prototype,s.default),f.extend(t.prototype,p.default),f.extend(t.prototype,m.default),f.extend(t.prototype,u.default),"undefined"!=typeof window&&(b=window.Cropper,window.Cropper=t),a.default=t},function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default={viewMode:0,dragMode:"crop",aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!1,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!1,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null}},function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default='<div class="cropper-container"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-action="e"></span><span class="cropper-line line-n" data-action="n"></span><span class="cropper-line line-w" data-action="w"></span><span class="cropper-line line-s" data-action="s"></span><span class="cropper-point point-e" data-action="e"></span><span class="cropper-point point-n" data-action="n"></span><span class="cropper-point point-w" data-action="w"></span><span class="cropper-point point-s" data-action="s"></span><span class="cropper-point point-ne" data-action="ne"></span><span class="cropper-point point-nw" data-action="nw"></span><span class="cropper-point point-sw" data-action="sw"></span><span class="cropper-point point-se" data-action="se"></span></div></div>'},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var s=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4));a.default={render:function(){var e=this;e.initContainer(),e.initCanvas(),e.initCropBox(),e.renderCanvas(),e.cropped&&e.renderCropBox()},initContainer:function(){var e=this,a=e.options,t=e.element,r=e.container,o=e.cropper;s.addClass(o,"cropper-hidden"),s.removeClass(t,"cropper-hidden"),e.containerData=e={width:Math.max(r.offsetWidth,Number(a.minContainerWidth)||200),height:Math.max(r.offsetHeight,Number(a.minContainerHeight)||100)},s.setStyle(o,{width:e.width,height:e.height}),s.addClass(t,"cropper-hidden"),s.removeClass(o,"cropper-hidden")},initCanvas:function(){var e=this,a=e.options.viewMode,t=e.containerData,r=e.imageData,o=90===Math.abs(r.rotate),i=o?r.naturalHeight:r.naturalWidth,o=o?r.naturalWidth:r.naturalHeight,n=i/o,d=t.width,l=t.height,i=(t.height*n>t.width?3===a?d=t.height*n:l=t.width/n:3===a?l=t.width/n:d=t.height*n,{naturalWidth:i,naturalHeight:o,aspectRatio:n,width:d,height:l});i.oldLeft=i.left=(t.width-d)/2,i.oldTop=i.top=(t.height-l)/2,e.canvasData=i,e.limited=1===a||2===a,e.limitCanvas(!0,!0),e.initialImageData=s.extend({},r),e.initialCanvasData=s.extend({},i)},limitCanvas:function(e,a){var t=this,r=t.options,o=r.viewMode,i=t.containerData,n=(null==i&&(i=[]),t.canvasData),d=(n=null==n?[]:n).aspectRatio,l=t.cropBoxData,s=t.cropped&&l,c=void 0,p=void 0;e&&(c=Number(r.minCanvasWidth)||0,p=Number(r.minCanvasHeight)||0,1<o?(c=Math.max(c,i.width),p=Math.max(p,i.height),3===o&&(c<p*d?c=p*d:p=c/d)):0<o&&(c?c=Math.max(c,s?l.width:0):p?p=Math.max(p,s?l.height:0):s&&((c=l.width)<(p=l.height)*d?c=p*d:p=c/d)),c&&p?c<p*d?p=c/d:c=p*d:c?p=c/d:p&&(c=p*d),n.minWidth=c,n.minHeight=p,n.maxWidth=1/0,n.maxHeight=1/0),a&&(o?(e=i.width-n.width,r=i.height-n.height,n.minLeft=Math.min(0,e),n.minTop=Math.min(0,r),n.maxLeft=Math.max(0,e),n.maxTop=Math.max(0,r),s&&t.limited&&(n.minLeft=Math.min(l.left,l.left+(l.width-n.width)),n.minTop=Math.min(l.top,l.top+(l.height-n.height)),n.maxLeft=l.left,n.maxTop=l.top,2===o&&(n.width>=i.width&&(n.minLeft=Math.min(0,e),n.maxLeft=Math.max(0,e)),n.height>=i.height&&(n.minTop=Math.min(0,r),n.maxTop=Math.max(0,r))))):(n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=i.width,n.maxTop=i.height))},renderCanvas:function(e){var a,t=this,r=t.canvasData,o=(null==r&&(r=[]),t.imageData),i=o.rotate,n=void 0;t.rotated&&(t.rotated=!1,(a=(n=s.getRotatedSizes({width:o.width,height:o.height,degree:i})).width/n.height)!==r.aspectRatio&&(r.left-=(n.width-r.width)/2,r.top-=(n.height-r.height)/2,r.width=n.width,r.height=n.height,r.aspectRatio=a,r.naturalWidth=o.naturalWidth,r.naturalHeight=o.naturalHeight,i%180&&(n=s.getRotatedSizes({width:o.naturalWidth,height:o.naturalHeight,degree:i}),r.naturalWidth=n.width,r.naturalHeight=n.height),t.limitCanvas(!0,!1))),(r.width>r.maxWidth||r.width<r.minWidth)&&(r.left=r.oldLeft),(r.height>r.maxHeight||r.height<r.minHeight)&&(r.top=r.oldTop),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),t.limitCanvas(!1,!0),r.oldLeft=r.left=Math.min(Math.max(r.left,r.minLeft),r.maxLeft),r.oldTop=r.top=Math.min(Math.max(r.top,r.minTop),r.maxTop),s.setStyle(t.canvas,{width:r.width,height:r.height,left:r.left,top:r.top}),t.renderImage(),t.cropped&&t.limited&&t.limitCropBox(!0,!0),e&&t.output()},renderImage:function(e){var a,t=this.canvasData,r=(null==t&&(t=[]),this.imageData),o=void 0,i=(r.rotate&&(o={width:i=(a=s.getRotatedSizes({width:t.width,height:t.height,degree:r.rotate,aspectRatio:r.aspectRatio},!0)).width,height:a=a.height,left:(t.width-i)/2,top:(t.height-a)/2}),s.extend(r,o||{width:t.width,height:t.height,left:0,top:0}),s.getTransform(r));s.setStyle(this.image,{width:r.width,height:r.height,marginLeft:r.left,marginTop:r.top,WebkitTransform:i,msTransform:i,transform:i}),e&&this.output()},initCropBox:function(){var e=this,a=e.options,t=a.aspectRatio,a=Number(a.autoCropArea)||.8,r=e.canvasData,o={width:r.width,height:r.height};t&&(r.height*t>r.width?o.height=o.width/t:o.width=o.height*t),e.cropBoxData=o,e.limitCropBox(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.width=Math.max(o.minWidth,o.width*a),o.height=Math.max(o.minHeight,o.height*a),o.oldLeft=o.left=r.left+(r.width-o.width)/2,o.oldTop=o.top=r.top+(r.height-o.height)/2,e.initialCropBoxData=s.extend({},o)},limitCropBox:function(e,a){var t=this,r=t.options,o=r.aspectRatio,i=t.containerData,n=t.canvasData,d=t.cropBoxData,t=t.limited,l=void 0,s=void 0,c=void 0,p=void 0;null==d&&(d=[]),null==i&&(i=[]),e&&(l=Number(r.minCropBoxWidth)||0,s=Number(r.minCropBoxHeight)||0,l=Math.min(l,i.width),s=Math.min(s,i.height),c=Math.min(i.width,(t?n:i).width),p=Math.min(i.height,(t?n:i).height),o&&(l&&s?l<s*o?s=l/o:l=s*o:l?s=l/o:s&&(l=s*o),c<p*o?p=c/o:c=p*o),d.minWidth=Math.min(l,c),d.minHeight=Math.min(s,p),d.maxWidth=c,d.maxHeight=p),a&&(t?(d.minLeft=Math.max(0,n.left),d.minTop=Math.max(0,n.top),d.maxLeft=Math.min(i.width,n.left+n.width)-d.width,d.maxTop=Math.min(i.height,n.top+n.height)-d.height):(d.minLeft=0,d.minTop=0,d.maxLeft=i.width-d.width,d.maxTop=i.height-d.height))},renderCropBox:function(){var e=this,a=e.options,t=e.containerData,r=e.cropBoxData;((r=null==r?[]:r).width>r.maxWidth||r.width<r.minWidth)&&(r.left=r.oldLeft),(r.height>r.maxHeight||r.height<r.minHeight)&&(r.top=r.oldTop),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),e.limitCropBox(!1,!0),r.oldLeft=r.left=Math.min(Math.max(r.left,r.minLeft),r.maxLeft),r.oldTop=r.top=Math.min(Math.max(r.top,r.minTop),r.maxTop),a.movable&&a.cropBoxMovable&&s.setData(e.face,"action",r.width===(t=null==t?[]:t).width&&r.height===t.height?"move":"all"),s.setStyle(e.cropBox,{width:r.width,height:r.height,left:r.left,top:r.top}),e.cropped&&e.limited&&e.limitCanvas(!0,!0),e.disabled||e.output()},output:function(){this.preview(),this.complete&&s.dispatchEvent(this.element,"crop",this.getData())}}},function(e,a){"use strict";function t(e){return M.call(e).slice(8,-1).toLowerCase()}function v(e){return"number"==typeof e&&!isNaN(e)}function o(e){return void 0===e}function n(e){return"object"===(void 0===e?"undefined":f(e))&&null!==e}function i(e){return"function"===t(e)}function d(e){return Array.isArray?Array.isArray(e):"array"===t(e)}function l(e){return e="string"==typeof e?e.trim?e.trim():e.replace(C,"$1"):e}function s(a,t){if(a&&i(t)){var e=void 0;if(d(a)||v(a.length))for(var r=a.length,e=0;e<r&&!1!==t.call(a,a[e],e,a);e++);else n(a)&&Object.keys(a).forEach(function(e){t.call(a,a[e],e,a)})}return a}function c(e,a){var t;v((e=null==e?[]:e).length)?s(e,function(e){c(e,a)}):e.classList?e.classList.add(a):(t=l(e.className))?t.indexOf(a)<0&&(e.className=t+" "+a):e.className=a}function p(e,a){v((e=null==e?[]:e).length)?s(e,function(e){p(e,a)}):e.classList?e.classList.remove(a):0<=e.className.indexOf(a)&&(e.className=e.className.replace(a,""))}function r(e){return e.replace(_,"$1-$2").toLowerCase()}function m(a,e,t){var r=l(e).split(w);1<r.length?s(r,function(e){m(a,e,t)}):a.removeEventListener?a.removeEventListener(e,t,!1):a.detachEvent&&a.detachEvent("on"+e,t)}function y(e){return document.createElement(e)}function b(e,a){var t=Math.abs(e.degree)%180,t=(90<t?180-t:t)*Math.PI/180,r=Math.sin(t),t=Math.cos(t),o=e.width,i=e.height,e=e.aspectRatio,n=void 0,d=void 0,d=a?(n=o/(t+r/e))/e:(n=o*t+i*r,o*r+i*t);return{width:n,height:d}}function u(e,a,t){var r="",o=a;for(t+=a;o<t;o++)r+=L(e.getUint8(o));return r}Object.defineProperty(a,"__esModule",{value:!0});var f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},h=(a.typeOf=t,a.isNumber=v,a.isUndefined=o,a.isObject=n,a.isPlainObject=function(e){if(!n(e))return!1;try{var a=e.constructor,t=a.prototype;return a&&t&&R.call(t,"isPrototypeOf")}catch(e){return!1}},a.isFunction=i,a.isArray=d,a.toArray=function(e,a){return a=0<=a?a:0,Array.from?Array.from(e).slice(a):A.call(e,a)},a.trim=l,a.each=s,a.extend=function t(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];var o=!0===a[0],i=o?a[1]:a[0];return 1<a.length&&(a.shift(),null==i&&(i=[]),a.forEach(function(a){n(a)&&Object.keys(a).forEach(function(e){o&&n(i[e])?t(!0,i[e],a[e]):i[e]=a[e]})})),i},a.proxy=function(r,o){for(var e=arguments.length,i=Array(2<e?e-2:0),a=2;a<e;a++)i[a-2]=arguments[a];return function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return r.apply(o,i.concat(a))}},a.setStyle=function(e,a){var t=(e=null==e?[]:e).style;null==t&&(t=[]),s(a,function(e,a){j.test(a)&&v(e)&&(e+="px"),t[a]=e})},a.hasClass=function(e,a){return e.classList?e.classList.contains(a):-1<e.className.indexOf(a)},a.addClass=c,a.removeClass=p,a.toggleClass=function a(e,t,r){v(e.length)?s(e,function(e){a(e,t,r)}):(r?c:p)(e,t)},a.hyphenate=r,a.getData=function(e,a){return n(e[a])?e[a]:e.dataset?e.dataset[a]:e.getAttribute("data-"+r(a))},a.setData=function(e,a,t){n(t)?e[a]=t:e.dataset?e.dataset[a]=t:e.setAttribute("data-"+r(a),t)},a.removeData=function(e,a){n(e[a])?delete e[a]:e.dataset?delete e.dataset[a]:e.removeAttribute("data-"+r(a))},a.removeListener=m,a.dispatchEvent=function(e,a,t){var r;return e.dispatchEvent?(r=void 0,i(Event)&&i(CustomEvent)?r=o(t)?new Event(a,{bubbles:!0,cancelable:!0}):new CustomEvent(a,{detail:t,bubbles:!0,cancelable:!0}):o(t)?(r=document.createEvent("Event")).initEvent(a,!0,!0):(r=document.createEvent("CustomEvent")).initCustomEvent(a,!0,!0,t),e.dispatchEvent(r)):!e.fireEvent||e.fireEvent("on"+a)},a.getEvent=function(e){var a,t=e||window.event;return t.target||(t.target=t.srcElement||document),!v(t.pageX)&&v(t.clientX)&&(a=(e=e.target.ownerDocument||document).documentElement,e=e.body,t.pageX=t.clientX+((a&&a.scrollLeft||e&&e.scrollLeft||0)-(a&&a.clientLeft||e&&e.clientLeft||0)),t.pageY=t.clientY+((a&&a.scrollTop||e&&e.scrollTop||0)-(a&&a.clientTop||e&&e.clientTop||0))),t},a.getOffset=function(e){var a=document.documentElement;return{left:(e=e.getBoundingClientRect()).left+((window.scrollX||a&&a.scrollLeft||0)-(a&&a.clientLeft||0)),top:e.top+((window.scrollY||a&&a.scrollTop||0)-(a&&a.clientTop||0))}},a.getTouchesCenter=function(e){var a=e.length,t=0,r=0;return a&&(s(e,function(e){t+=e.pageX,r+=e.pageY}),t/=a,r/=a),{pageX:t,pageY:r}},a.getByTag=function(e,a){return e.getElementsByTagName(a)},a.getByClass=function(e,a){return e.getElementsByClassName?e.getElementsByClassName(a):e.querySelectorAll("."+a)},a.createElement=y,a.appendChild=function(e,a){e.appendChild(a)},a.removeChild=function(e){e.parentNode&&e.parentNode.removeChild(e)},a.empty=function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},a.isCrossOriginURL=function(e){return(e=e.match(g))&&(e[1]!==location.protocol||e[2]!==location.hostname||e[3]!==location.port)},a.addTimestamp=function(e){var a="timestamp="+(new Date).getTime();return e+(-1===e.indexOf("?")?"?":"&")+a},a.getImageSize=function(e,a){var t;e.naturalWidth&&!x?a(e.naturalWidth,e.naturalHeight):((t=y("img")).onload=function(){a(this.width,this.height)},t.src=e.src)},a.getTransform=function(e){var a=[],t=e.rotate,r=e.scaleX,e=e.scaleY;return v(t)&&0!==t&&a.push("rotate("+t+"deg)"),v(r)&&1!==r&&a.push("scaleX("+r+")"),v(e)&&1!==e&&a.push("scaleY("+e+")"),a.length?a.join(" "):"none"},a.getRotatedSizes=b,a.getSourceCanvas=function(e,a){var t,r=y("canvas"),o=r.getContext("2d"),i=0,n=0,d=a.naturalWidth,l=a.naturalHeight,s=a.rotate,c=a.scaleX,a=a.scaleY,p=v(c)&&v(a)&&(1!==c||1!==a),m=v(s)&&0!==s,u=m||p,f=d*Math.abs(c),h=l*Math.abs(a),_=void 0,g=void 0;return p&&(_=f/2,g=h/2),m&&(_=(f=(t=b({width:f,height:h,degree:s})).width)/2,g=(h=t.height)/2),r.width=f,r.height=h,u&&(i=-d/2,n=-l/2,o.save(),o.translate(_,g)),m&&o.rotate(s*Math.PI/180),p&&o.scale(c,a),o.drawImage(e,Math.floor(i),Math.floor(n),Math.floor(d),Math.floor(l)),u&&o.restore(),r},a.getStringFromCharCode=u,a.getOrientation=function(e){var a,t=new DataView(e),r=t.byteLength,o=void 0,i=void 0,n=void 0,d=void 0,l=void 0,s=void 0;if(255===t.getUint8(0)&&216===t.getUint8(1))for(l=2;l<r;){if(255===t.getUint8(l)&&225===t.getUint8(l+1)){n=l;break}l++}if(n&&(e=n+10,"Exif"===u(t,n+4,4)&&((i=18761===(a=t.getUint16(e)))||19789===a)&&42===t.getUint16(e+2,i)&&8<=(a=t.getUint32(e+4,i))&&(d=e+a)),d)for(r=t.getUint16(d,i),s=0;s<r;s++)if(274===t.getUint16(l=d+12*s+2,i)){l+=8,o=t.getUint16(l,i),x&&t.setUint16(l,1,i);break}return o},a.dataURLToArrayBuffer=function(e){for(var e=e.replace(h,""),a=atob(e),t=a.length,e=new ArrayBuffer(t),r=new Uint8Array(e),o=void 0,o=0;o<t;o++)r[o]=a.charCodeAt(o);return e},a.arrayBufferToDataURL=function(e){for(var a=new Uint8Array(e),t=a.length,r="",o=void 0,o=0;o<t;o++)r+=L(a[o]);return"data:image/jpeg;base64,"+btoa(r)},/^data:([^;]+);base64,/),_=/([a-z\d])([A-Z])/g,g=/^(https?:)\/\/([^:\/\?#]+):?(\d*)/i,w=/\s+/,j=/^(width|height|left|top|marginLeft|marginTop)$/,C=/^\s+(.*)\s+$/,Q=window.navigator,x=Q&&/(Macintosh|iPhone|iPod|iPad).*AppleWebKit/i.test(Q.userAgent),Q=Object.prototype,M=Q.toString,R=Q.hasOwnProperty,A=Array.prototype.slice,L=String.fromCharCode;a.addListener=function a(r,o,i,e){var t=l(o).split(w),n=i;1<t.length?s(t,function(e){a(r,e,i)}):(e&&(i=function(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return m(r,o,i),n.apply(r,a)}),r.addEventListener?r.addEventListener(o,i,!1):r.attachEvent&&r.attachEvent("on${type}",i))}},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var u=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4)),f="preview";a.default={initPreview:function(){var e=this,a=e.options.preview,t=u.createElement("img"),r=e.crossOrigin,o=r?e.crossOriginUrl:e.url;r&&(t.crossOrigin=r),t.src=o,u.appendChild(e.viewBox,t),e.image2=t,a&&(t=document.querySelectorAll(a),e.previews=t,u.each(t,function(e){var a=u.createElement("img");u.setData(e,f,{width:e.offsetWidth,height:e.offsetHeight,html:e.innerHTML}),r&&(a.crossOrigin=r),a.src=o,a.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',u.empty(e),u.appendChild(e,a)}))},resetPreview:function(){u.each(this.previews,function(e){var a=u.getData(e,f);u.setStyle(e,{width:a.width,height:a.height}),e.innerHTML=a.html,u.removeData(e,f)})},preview:function(){var e=this,a=e.imageData,t=e.canvasData,r=e.cropBoxData,n=(r=null==r?[]:r).width,d=r.height,l=a.width,s=a.height,c=r.left-(t=null==t?[]:t).left-a.left,p=r.top-t.top-a.top,r=u.getTransform(a),m={WebkitTransform:r,msTransform:r,transform:r};e.cropped&&!e.disabled&&(u.setStyle(e.image2,u.extend({width:l,height:s,marginLeft:-c,marginTop:-p},m)),u.each(e.previews,function(e){var a=u.getData(e,f),t=a.width,a=a.height,r=t,o=a,i=1;n&&(o=d*(i=t/n)),d&&a<o&&(r=n*(i=a/d),o=a),u.setStyle(e,{width:r,height:o}),u.setStyle(u.getByTag(e,"img")[0],u.extend({width:l*i,height:s*i,marginLeft:-c*i,marginTop:-p*i},m))}))}}},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var o=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4)),i="mousedown touchstart pointerdown MSPointerDown",n="mousemove touchmove pointermove MSPointerMove",d="mouseup touchend touchcancel pointerup pointercancel MSPointerUp MSPointerCancel",l="wheel mousewheel DOMMouseScroll",s="dblclick",c="cropstart",p="cropmove",m="cropend";a.default={bind:function(){var e=this,a=e.options,t=e.element,r=e.cropper;o.isFunction(a.cropstart)&&o.addListener(t,c,a.cropstart),o.isFunction(a.cropmove)&&o.addListener(t,p,a.cropmove),o.isFunction(a.cropend)&&o.addListener(t,m,a.cropend),o.isFunction(a.crop)&&o.addListener(t,"crop",a.crop),o.isFunction(a.zoom)&&o.addListener(t,"zoom",a.zoom),o.addListener(r,i,e.onCropStart=o.proxy(e.cropStart,e)),a.zoomable&&a.zoomOnWheel&&o.addListener(r,l,e.onWheel=o.proxy(e.wheel,e)),a.toggleDragModeOnDblclick&&o.addListener(r,s,e.onDblclick=o.proxy(e.dblclick,e)),o.addListener(document,n,e.onCropMove=o.proxy(e.cropMove,e)),o.addListener(document,d,e.onCropEnd=o.proxy(e.cropEnd,e)),a.responsive&&o.addListener(window,"resize",e.onResize=o.proxy(e.resize,e))},unbind:function(){var e=this,a=e.options,t=e.element,r=e.cropper;o.isFunction(a.cropstart)&&o.removeListener(t,c,a.cropstart),o.isFunction(a.cropmove)&&o.removeListener(t,p,a.cropmove),o.isFunction(a.cropend)&&o.removeListener(t,m,a.cropend),o.isFunction(a.crop)&&o.removeListener(t,"crop",a.crop),o.isFunction(a.zoom)&&o.removeListener(t,"zoom",a.zoom),o.removeListener(r,i,e.onCropStart),a.zoomable&&a.zoomOnWheel&&o.removeListener(r,l,e.onWheel),a.toggleDragModeOnDblclick&&o.removeListener(r,s,e.onDblclick),o.removeListener(document,n,e.onCropMove),o.removeListener(document,d,e.onCropEnd),a.responsive&&o.removeListener(window,"resize",e.onResize)}}},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.REGEXP_ACTIONS=void 0;var d=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4)),l=a.REGEXP_ACTIONS=/^(e|w|s|n|se|sw|ne|nw|all|crop|move|zoom)$/;a.default={resize:function(){var t,r,o,e=this,a=e.options.restore,i=e.container,n=e.containerData;!e.disabled&&n&&(t=i.offsetWidth/n.width,o=r=void 0,1==t&&i.offsetHeight===n.height||(a&&(r=e.getCanvasData(),o=e.getCropBoxData()),e.render(),a&&(e.setCanvasData(d.each(r,function(e,a){r[a]=e*t})),e.setCropBoxData(d.each(o,function(e,a){o[a]=e*t})))))},dblclick:function(){this.disabled||this.setDragMode(d.hasClass(this.dragBox,"cropper-crop")?"move":"crop")},wheel:function(e){var a=this,e=d.getEvent(e),t=Number(a.options.wheelZoomRatio)||.1,r=1;a.disabled||(e.preventDefault(),a.wheeling||(a.wheeling=!0,setTimeout(function(){a.wheeling=!1},50),e.deltaY?r=0<e.deltaY?1:-1:e.wheelDelta?r=-e.wheelDelta/120:e.detail&&(r=0<e.detail?1:-1),a.zoom(-r*t,e)))},cropStart:function(e){var a,t=this,r=t.options,e=d.getEvent(e),o=e.touches,i=void 0,n=void 0;if(!t.disabled){if(o){if(1<(a=o.length)){if(!r.zoomable||!r.zoomOnTouch||2!==a)return;i=o[1],t.startX2=i.pageX,t.startY2=i.pageY,n="zoom"}i=o[0]}n=n||d.getData(e.target,"action"),l.test(n)&&!1!==d.dispatchEvent(t.element,"cropstart",{originalEvent:e,action:n})&&(e.preventDefault(),t.action=n,t.cropping=!1,t.startX=(i||e).pageX,t.startY=(i||e).pageY,"crop"===n&&(t.cropping=!0,d.addClass(t.dragBox,"cropper-modal")))}},cropMove:function(e){var a,t=this,r=t.options,e=d.getEvent(e),o=e.touches,i=t.action,n=void 0;if(!t.disabled){if(o){if(1<(a=o.length)){if(!r.zoomable||!r.zoomOnTouch||2!==a)return;n=o[1],t.endX2=n.pageX,t.endY2=n.pageY}n=o[0]}i&&!1!==d.dispatchEvent(t.element,"cropmove",{originalEvent:e,action:i})&&(e.preventDefault(),t.endX=(n||e).pageX,t.endY=(n||e).pageY,t.change(e.shiftKey,"zoom"===i?e:null))}},cropEnd:function(e){var a=this,t=a.options,e=d.getEvent(e),r=a.action;a.disabled||r&&(e.preventDefault(),a.cropping&&(a.cropping=!1,d.toggleClass(a.dragBox,"cropper-modal",a.cropped&&t.modal)),a.action="",d.dispatchEvent(a.element,"cropend",{originalEvent:e,action:r}))}}},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var j=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4)),C="se",Q="sw",x="ne",M="nw";a.default={change:function(e,a){var t,r=this,o=r.options,i=r.containerData,n=r.canvasData,d=r.cropBoxData,l=o.aspectRatio,s=r.action,c=d.width,p=d.height,m=d.left,u=d.top,f=m+c,h=u+p,_=0,g=0,v=i.width,y=i.height,b=!0,w=(!l&&e&&(l=c&&p?c/p:1),r.limited&&(_=d.minLeft,g=d.minTop,v=_+Math.min(i.width,n.left+n.width),y=g+Math.min(i.height,n.top+n.height)),{x:r.endX-r.startX,y:r.endY-r.startY});switch(l&&(w.X=w.y*l,w.Y=w.x/l),s){case"all":m+=w.x,u+=w.y;break;case"e":if(0<=w.x&&(v<=f||l&&(u<=g||y<=h))){b=!1;break}c+=w.x,l&&(p=c/l,u-=w.Y/2),c<0&&(s="w",c=0);break;case"n":if(w.y<=0&&(u<=g||l&&(m<=_||v<=f))){b=!1;break}p-=w.y,u+=w.y,l&&(c=p*l,m+=w.X/2),p<0&&(s="s",p=0);break;case"w":if(w.x<=0&&(m<=_||l&&(u<=g||y<=h))){b=!1;break}c-=w.x,m+=w.x,l&&(p=c/l,u+=w.Y/2),c<0&&(s="e",c=0);break;case"s":if(0<=w.y&&(y<=h||l&&(m<=_||v<=f))){b=!1;break}p+=w.y,l&&(c=p*l,m-=w.X/2),p<0&&(s="n",p=0);break;case x:if(l){if(w.y<=0&&(u<=g||v<=f)){b=!1;break}p-=w.y,u+=w.y,c=p*l}else!(0<=w.x)||f<v?c+=w.x:w.y<=0&&u<=g&&(b=!1),(!(w.y<=0)||g<u)&&(p-=w.y,u+=w.y);c<0&&p<0?(s=Q,c=p=0):c<0?(s=M,c=0):p<0&&(s=C,p=0);break;case M:if(l){if(w.y<=0&&(u<=g||m<=_)){b=!1;break}p-=w.y,u+=w.y,c=p*l,m+=w.X}else!(w.x<=0)||_<m?(c-=w.x,m+=w.x):w.y<=0&&u<=g&&(b=!1),(!(w.y<=0)||g<u)&&(p-=w.y,u+=w.y);c<0&&p<0?(s=C,c=p=0):c<0?(s=x,c=0):p<0&&(s=Q,p=0);break;case Q:if(l){if(w.x<=0&&(m<=_||y<=h)){b=!1;break}c-=w.x,m+=w.x,p=c/l}else!(w.x<=0)||_<m?(c-=w.x,m+=w.x):0<=w.y&&y<=h&&(b=!1),(!(0<=w.y)||h<y)&&(p+=w.y);c<0&&p<0?(s=x,c=p=0):c<0?(s=C,c=0):p<0&&(s=M,p=0);break;case C:if(l){if(0<=w.x&&(v<=f||y<=h)){b=!1;break}p=(c+=w.x)/l}else!(0<=w.x)||f<v?c+=w.x:0<=w.y&&y<=h&&(b=!1),(!(0<=w.y)||h<y)&&(p+=w.y);c<0&&p<0?(s=M,c=p=0):c<0?(s=Q,c=0):p<0&&(s=x,p=0);break;case"move":r.move(w.x,w.y),b=!1;break;case"zoom":r.zoom(function(e,a,t,r){e=Math.sqrt(e*e+a*a);return(Math.sqrt(t*t+r*r)-e)/e}(Math.abs(r.startX-r.startX2),Math.abs(r.startY-r.startY2),Math.abs(r.endX-r.endX2),Math.abs(r.endY-r.endY2)),a),r.startX2=r.endX2,r.startY2=r.endY2,b=!1;break;case"crop":if(!w.x||!w.y){b=!1;break}t=j.getOffset(r.cropper),m=r.startX-t.left,u=r.startY-t.top,c=d.minWidth,p=d.minHeight,0<w.x?s=0<w.y?C:x:w.x<0&&(m-=c,s=0<w.y?Q:M),w.y<0&&(u-=p),r.cropped||(j.removeClass(r.cropBox,"cropper-hidden"),r.cropped=!0,r.limited&&r.limitCropBox(!0,!0))}b&&(d.width=c,d.height=p,d.left=m,d.top=u,r.action=s,r.renderCropBox()),r.startX=r.endX,r.startY=r.endY}}},function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var _=function(e){if(e&&e.__esModule)return e;var a={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(a[t]=e[t]);return a.default=e,a}(t(4));a.default={crop:function(){var e=this;return e.ready&&!e.disabled&&(e.cropped||(e.cropped=!0,e.limitCropBox(!0,!0),e.options.modal&&_.addClass(e.dragBox,"cropper-modal"),_.removeClass(e.cropBox,"cropper-hidden")),e.setCropBoxData(e.initialCropBoxData)),e},reset:function(){var e=this;return e.ready&&!e.disabled&&(e.imageData=_.extend({},e.initialImageData),e.canvasData=_.extend({},e.initialCanvasData),e.cropBoxData=_.extend({},e.initialCropBoxData),e.renderCanvas(),e.cropped&&e.renderCropBox()),e},clear:function(){var e=this;return e.cropped&&!e.disabled&&(_.extend(e.cropBoxData,{left:0,top:0,width:0,height:0}),e.cropped=!1,e.renderCropBox(),e.limitCanvas(),e.renderCanvas(),_.removeClass(e.dragBox,"cropper-modal"),_.addClass(e.cropBox,"cropper-hidden")),e},replace:function(a,e){var t=this;return!t.disabled&&a&&(t.isImg&&(t.element.src=a),e?(t.url=a,t.image.src=a,t.ready&&(t.image2.src=a,_.each(t.previews,function(e){_.getByTag(e,"img")[0].src=a}))):(t.isImg&&(t.replaced=!0),t.options.data=null,t.load(a))),t},enable:function(){return this.ready&&(this.disabled=!1,_.removeClass(this.cropper,"cropper-disabled")),this},disable:function(){return this.ready&&(this.disabled=!0,_.addClass(this.cropper,"cropper-disabled")),this},destroy:function(){var e=this,a=e.element,t=e.image;return e.loaded?(e.isImg&&e.replaced&&(a.src=e.originalUrl),e.unbuild(),_.removeClass(a,"cropper-hidden")):e.isImg?_.removeListener(a,"load",e.start):t&&_.removeChild(t),_.removeData(a,"cropper"),e},move:function(e,a){var t=this.canvasData;return this.moveTo(_.isUndefined(e)?e:t.left+Number(e),_.isUndefined(a)?a:t.top+Number(a))},moveTo:function(e,a){var t=this,r=t.canvasData,o=!1;return _.isUndefined(a)&&(a=e),e=Number(e),a=Number(a),t.ready&&!t.disabled&&t.options.movable&&(_.isNumber(e)&&(r.left=e,o=!0),_.isNumber(a)&&(r.top=a,o=!0),o&&t.renderCanvas(!0)),t},zoom:function(e,a){var t=this.canvasData;return e=Number(e),this.zoomTo(t.width*(e=e<0?1/(1-e):1+e)/t.naturalWidth,a)},zoomTo:function(e,a){var t=this,r=t.options,o=t.canvasData,i=o.width,n=o.height,d=o.naturalWidth,l=o.naturalHeight;if(0<=(e=Number(e))&&t.ready&&!t.disabled&&r.zoomable){if(r=l*e,!1===_.dispatchEvent(t.element,"zoom",{originalEvent:a,oldRatio:i/d,ratio:(l=d*e)/d}))return t;a?(e=_.getOffset(t.cropper),d=a.touches?_.getTouchesCenter(a.touches):{pageX:a.pageX,pageY:a.pageY},o.left-=(l-i)*((d.pageX-e.left-o.left)/i),o.top-=(r-n)*((d.pageY-e.top-o.top)/n)):(o.left-=(l-i)/2,o.top-=(r-n)/2),o.width=l,o.height=r,t.renderCanvas(!0)}return t},rotate:function(e){return this.rotateTo((this.imageData.rotate||0)+Number(e))},rotateTo:function(e){var a=this;return e=Number(e),_.isNumber(e)&&a.ready&&!a.disabled&&a.options.rotatable&&(a.imageData.rotate=e%360,a.rotated=!0,a.renderCanvas(!0)),a},scale:function(e,a){var t=this,r=t.imageData,o=!1;return _.isUndefined(a)&&(a=e),e=Number(e),a=Number(a),t.ready&&!t.disabled&&t.options.scalable&&(_.isNumber(e)&&(r.scaleX=e,o=!0),_.isNumber(a)&&(r.scaleY=a,o=!0),o&&t.renderImage(!0)),t},scaleX:function(e){var a=this.imageData.scaleY;return this.scale(e,_.isNumber(a)?a:1)},scaleY:function(e){var a=this.imageData.scaleX;return this.scale(_.isNumber(a)?a:1,e)},getData:function(t){var r,e=this,a=e.options,o=e.imageData,i=e.canvasData,n=e.cropBoxData,d=void 0;return e.ready&&e.cropped?(d={x:n.left-i.left,y:n.top-i.top,width:n.width,height:n.height},r=o.width/o.naturalWidth,_.each(d,function(e,a){e/=r,d[a]=t?Math.round(e):e})):d={x:0,y:0,width:0,height:0},a.rotatable&&(d.rotate=o.rotate||0),a.scalable&&(d.scaleX=o.scaleX||1,d.scaleY=o.scaleY||1),d},setData:function(e){var a=this,t=a.options,r=a.imageData,o=a.canvasData,i={},n=void 0,d=void 0;return _.isFunction(e)&&(e=e.call(a.element)),a.ready&&!a.disabled&&_.isPlainObject(e)&&(t.rotatable&&_.isNumber(e.rotate)&&e.rotate!==r.rotate&&(r.rotate=e.rotate,a.rotated=n=!0),t.scalable&&(_.isNumber(e.scaleX)&&e.scaleX!==r.scaleX&&(r.scaleX=e.scaleX,d=!0),_.isNumber(e.scaleY)&&e.scaleY!==r.scaleY&&(r.scaleY=e.scaleY,d=!0)),n?a.renderCanvas():d&&a.renderImage(),t=r.width/r.naturalWidth,_.isNumber(e.x)&&(i.left=e.x*t+o.left),_.isNumber(e.y)&&(i.top=e.y*t+o.top),_.isNumber(e.width)&&(i.width=e.width*t),_.isNumber(e.height)&&(i.height=e.height*t),a.setCropBoxData(i)),a},getContainerData:function(){return this.ready?this.containerData:{}},getImageData:function(){return this.loaded?this.imageData:{}},getCanvasData:function(){var a=this.canvasData,t={};return this.ready&&_.each(["left","top","width","height","naturalWidth","naturalHeight"],function(e){t[e]=a[e]}),t},setCanvasData:function(e){var a=this,t=a.canvasData,r=t.aspectRatio;return _.isFunction(e)&&(e=e.call(a.element)),a.ready&&!a.disabled&&_.isPlainObject(e)&&(_.isNumber(e.left)&&(t.left=e.left),_.isNumber(e.top)&&(t.top=e.top),_.isNumber(e.width)?(t.width=e.width,t.height=e.width/r):_.isNumber(e.height)&&(t.height=e.height,t.width=e.height*r),a.renderCanvas(!0)),a},getCropBoxData:function(){var e=this.cropBoxData,a=void 0;return(a=this.ready&&this.cropped?{left:e.left,top:e.top,width:e.width,height:e.height}:a)||{}},setCropBoxData:function(e){var a=this,t=a.cropBoxData,r=a.options.aspectRatio,o=void 0,i=void 0;return _.isFunction(e)&&(e=e.call(a.element)),a.ready&&a.cropped&&!a.disabled&&_.isPlainObject(e)&&(_.isNumber(e.left)&&(t.left=e.left),_.isNumber(e.top)&&(t.top=e.top),_.isNumber(e.width)&&(o=!0,t.width=e.width),_.isNumber(e.height)&&(i=!0,t.height=e.height),r&&(o?t.height=t.width/r:i&&(t.width=t.height*r)),a.renderCropBox()),a},getCroppedCanvas:function(e){var a=this;if(!a.ready||!window.HTMLCanvasElement)return null;if(!a.cropped)return _.getSourceCanvas(a.image,a.imageData);_.isPlainObject(e)||(e={});var t,r,o,i,n,d,l=a.getData(),s=l.width,c=l.height,p=s/c,m=void 0,u=void 0,f=void 0,p=(_.isPlainObject(e)&&(m=e.width,u=e.height,m?(u=m/p,f=m/s):u&&(m=u*p,f=u/c)),Math.floor(m||s)),m=Math.floor(u||c),u=_.createElement("canvas"),h=u.getContext("2d"),s=(u.width=p,u.height=m,e.fillColor&&(h.fillStyle=e.fillColor,h.fillRect(0,0,p,m)),e=_.getSourceCanvas(a.image,a.imageData),p=e.width,m=e.height,a=a.canvasData,e=[e],t=l.x+a.naturalWidth*(Math.abs(l.scaleX||1)-1)/2,a=l.y+a.naturalHeight*(Math.abs(l.scaleY||1)-1)/2,d=n=i=o=r=l=void 0,t<=-s||p<t?t=l=o=n=0:t<=0?(o=-t,t=0,l=n=Math.min(p,s+t)):t<=p&&(o=0,l=n=Math.min(s,p-t)),l<=0||a<=-c||m<a?a=r=i=d=0:a<=0?(i=-a,a=0,r=d=Math.min(m,c+a)):a<=m&&(i=0,r=d=Math.min(c,m-a)),e.push(Math.floor(t),Math.floor(a),Math.floor(l),Math.floor(r)),f&&(o*=f,i*=f,n*=f,d*=f),0<n&&0<d&&e.push(Math.floor(o),Math.floor(i),Math.floor(n),Math.floor(d)),e);return h.drawImage.apply(h,function(e){if(Array.isArray(e)){for(var a=0,t=Array(e.length);a<e.length;a++)t[a]=e[a];return t}return Array.from(e)}(s)),u},setAspectRatio:function(e){var a=this,t=a.options;return a.disabled||_.isUndefined(e)||(t.aspectRatio=Math.max(0,e)||NaN,a.ready&&(a.initCropBox(),a.cropped&&a.renderCropBox())),a},setDragMode:function(e){var a,t,r=this,o=r.options,i=r.dragBox,n=r.face;return r.loaded&&!r.disabled&&(t=o.movable&&"move"===e,_.setData(i,"action",e=(a="crop"===e)||t?e:"none"),_.toggleClass(i,"cropper-crop",a),_.toggleClass(i,"cropper-move",t),o.cropBoxMovable||(_.setData(n,"action",e),_.toggleClass(n,"cropper-crop",a),_.toggleClass(n,"cropper-move",t))),r}}}],o={},t.m=r,t.c=o,t.p="",t(0);function t(e){if(o[e])return o[e].exports;var a=o[e]={exports:{},id:e,loaded:!1};return r[e].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}var r,o}),function(U){U.fn.bPopup=function(e,a){function t(){Q.modal&&U('<div class="b-modal '+u+'"></div>').css({backgroundColor:Q.modalColor,position:"fixed",top:0,right:0,bottom:0,left:0,opacity:0,zIndex:Q.zIndex+O}).appendTo(Q.appendTo).fadeTo(Q.speed,Q.opacity),p(),x.data("bPopup",Q).data("id",u).css({left:"slideIn"==Q.transition||"slideBack"==Q.transition?"slideBack"==Q.transition?M.scrollLeft()+L:-1*(y+w):l(!(!Q.follow[0]&&_||g)),position:Q.positionStyle||"absolute",top:"slideDown"==Q.transition||"slideUp"==Q.transition?"slideUp"==Q.transition?M.scrollTop()+A:v+-1*b:s(!(!Q.follow[1]&&h||g)),"z-index":Q.zIndex+O+1}).each(function(){Q.appending&&U(this).appendTo(Q.appendTo)}),n(!0)}function r(){return Q.modal&&U(".b-modal."+x.data("id")).fadeTo(Q.speed,0,function(){U(this).remove()}),Q.scrollBar||U("html").css("overflow","auto"),U(".b-modal."+u).off("click"),M.off("keydown."+u),R.off("."+u).data("bPopup",0<R.data("bPopup")-1?R.data("bPopup")-1:null),x.undelegate(".bClose, ."+Q.closeClass,"click."+u,r).data("bPopup",null),clearTimeout(C),n(),!1}function o(a){A=R.height(),L=R.width(),((f=m()).x||f.y)&&(clearTimeout(j),j=setTimeout(function(){p(),a=a||Q.followSpeed;var e={};f.x&&(e.left=Q.follow[0]?l(!0):"auto"),f.y&&(e.top=Q.follow[1]?s(!0):"auto"),x.dequeue().each(function(){g?U(this).css({left:y,top:v}):U(this).animate(e,a,Q.followEasing)})},50))}function i(e){var a=e.width(),t=e.height(),r={};Q.contentContainer.css({height:t,width:a}),t>=x.height()&&(r.height=x.height()),a>=x.width()&&(r.width=x.width()),b=x.outerHeight(!0),w=x.outerWidth(!0),p(),Q.contentContainer.css({height:"auto",width:"auto"}),r.left=l(!(!Q.follow[0]&&_||g)),r.top=s(!(!Q.follow[1]&&h||g)),x.animate(r,250,function(){e.show(),f=m()})}function n(a){function e(e){x.css({display:"block",opacity:1}).animate(e,Q.speed,Q.easing,function(){d(a)})}switch(!a&&Q.transitionClose||Q.transition){case"slideIn":e({left:a?l(!(!Q.follow[0]&&_||g)):M.scrollLeft()-(w||x.outerWidth(!0))-200});break;case"slideBack":e({left:a?l(!(!Q.follow[0]&&_||g)):M.scrollLeft()+L+200});break;case"slideDown":e({top:a?s(!(!Q.follow[1]&&h||g)):M.scrollTop()-(b||x.outerHeight(!0))-200});break;case"slideUp":e({top:a?s(!(!Q.follow[1]&&h||g)):M.scrollTop()+A+200});break;default:x.stop().fadeTo(Q.speed,a?1:0,function(){d(a)})}}function d(e){e?(R.data("bPopup",O),x.delegate(".bClose, ."+Q.closeClass,"click."+u,r),Q.modalClose&&U(".b-modal."+u).css("cursor","pointer").on("click",r),k||!Q.follow[0]&&!Q.follow[1]||R.on("scroll."+u,function(){var e;(f.x||f.y)&&(e={},f.x&&(e.left=Q.follow[0]?l(!g):"auto"),f.y&&(e.top=Q.follow[1]?s(!g):"auto"),x.dequeue().animate(e,Q.followSpeed,Q.followEasing))}).on("resize."+u,function(){o()}),Q.escClose&&M.on("keydown."+u,function(e){27==e.which&&r()}),c(a),Q.autoClose&&(C=setTimeout(r,Q.autoClose))):(x.hide(),c(Q.onClose),Q.loadUrl&&(Q.contentContainer.empty(),x.css({height:"auto",width:"auto"})))}function l(e){return e?y+M.scrollLeft():y}function s(e){return e?v+M.scrollTop():v}function c(e,a){U.isFunction(e)&&e.call(x,a)}function p(){v=h?Q.position[1]:Math.max(0,(A-x.outerHeight(!0))/2-Q.amsl),y=_?Q.position[0]:(L-x.outerWidth(!0))/2,f=m()}function m(){return{x:L>x.outerWidth(!0),y:A>x.outerHeight(!0)}}U.isFunction(e)&&(a=e,e=null);var u,f,h,_,g,v,y,b,w,j,C,Q=U.extend({},U.fn.bPopup.defaults,e),x=(Q.scrollBar||U("html").css("overflow","hidden"),this),M=U(document),R=U(window),A=R.height(),L=R.width(),k=/OS 6(_\d)+/i.test(navigator.userAgent),O=0;return x.close=function(){r()},x.reposition=function(e){o(e)},x.each(function(){U(this).data("bPopup")||(c(Q.onOpen),O=(R.data("bPopup")||0)+1,u="__b-popup"+O+"__",h="auto"!==Q.position[1],_="auto"!==Q.position[0],g="fixed"===Q.positionStyle,b=x.outerHeight(!0),w=x.outerWidth(!0),(Q.loadUrl?function(){switch(Q.contentContainer=U(Q.contentContainer||x),Q.content){case"iframe":var e=U('<iframe class="b-iframe" '+Q.iframeAttr+"></iframe>");e.appendTo(Q.contentContainer),b=x.outerHeight(!0),w=x.outerWidth(!0),t(),e.attr("src",Q.loadUrl),c(Q.loadCallback);break;case"image":t(),U("<img />").load(function(){c(Q.loadCallback),i(U(this))}).attr("src",Q.loadUrl).hide().appendTo(Q.contentContainer);break;default:t(),U('<div class="b-ajax-wrapper"></div>').load(Q.loadUrl,Q.loadData,function(e,a,t){c(Q.loadCallback,a),i(U(this))}).hide().appendTo(Q.contentContainer)}}:t)())})},U.fn.bPopup.defaults={amsl:50,appending:!0,appendTo:"body",autoClose:!1,closeClass:"b-close",content:"ajax",contentContainer:!1,easing:"swing",escClose:!0,follow:[!0,!0],followEasing:"swing",followSpeed:500,iframeAttr:'scrolling="no" frameborder="0"',loadCallback:!1,loadData:!1,loadUrl:!1,modal:!0,modalClose:!0,modalColor:"#000",onClose:!1,onOpen:!1,opacity:.7,position:["auto","auto"],positionStyle:"absolute",scrollBar:!0,speed:250,transition:"fadeIn",transitionClose:!1,zIndex:9997}}(jQuery),jQuery(document).ready(function(){function a(e){var s=e.target;"null"!=(e=void 0===s.files?s&&s.value?[{name:s.value.replace(/^.+\\/,"")}]:[]:s.files)&&void 0!==e&&jQuery.each(e,function(e,a){var t,r=s,o=jQuery(r),i=(o.attr("name"),jQuery(r).attr("data-type")),n=jQuery(r).parents(".arm_template_container"),d=(o.attr("class"),o.attr("data-file_size"),new XMLHttpRequest),r=(a.type,a.name.lastIndexOf(".")),l=(r=(r=a.name.substring(r+1)).toLowerCase(),o.attr("data-msg-invalid"));null==l&&(l=invalidFileTypeError),-1===jQuery.inArray(r,["png","jpg","jpeg","bmp"])?(alert(l),o.val("")):d.upload?(n.css("opacity","0.5"),r=function(e){for(var a=null!=e&&0!=e?e:5,t="",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",o=0;o<a;o++)t+=r.charAt(Math.floor(Math.random()*r.length));return t}(6),l=a.name.lastIndexOf("."),l=a.name.substring(l+1),t=/.*(?=\.)/.exec(a.name),r=("armCover"+r+"_"+t).replace(/[^\w\s]/gi,"").replace(/ /g,"")+"."+l,t=new FormData,"cover"===i?t.append("action","arm_upload_cover"):t.append("action","arm_upload_profile"),"cover"!==i&&"profile"!==i||d.open("POST",__ARMAJAXURL,!0),d.setRequestHeader("FILEMETANAME",i),d.setRequestHeader("X_FILENAME",r),d.setRequestHeader("X-FILENAME",r),t.append("armfileselect",a),t.append("_wpnonce",jQuery("input[name='arm_wp_nonce']").val()),d.send(t),d.onreadystatechange=function(){if(4!=d.readyState||200!=d.status)return 200!=d.status&&(n.css("opacity","1"),alert(fileUploadError),o.val("")),!1;var a,l,t,e,s,r=d.responseText;n.css("opacity","1"),"cover"===i?(-1==r.indexOf("http://")&&-1==r.indexOf("https://")||(r="//"+r.split("://")[1]),0!=jQuery("#arm_crop_cover_div").length?(jQuery("#arm_crop_cover_div").find("img").attr("src",""),jQuery("#arm_crop_cover_div").find("img").attr("src",r),a=0,e=jQuery("#arm_crop_cover_image"),l=new Cropper(e,{viewMode:4,aspectRatio:2.86875,zoomable:!0,rotatable:!0,dashed:!1,dragMode:"move",cropBoxResizable:!1,background:!1,zoomOnTouch:!1,crop:function(e){jQuery("#arm_crop_cover_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==a&&(jQuery(".arm_clear_cover_button.arm_img_cover_setting").trigger("click"),jQuery("#arm_crop_cover_image").attr("data-coord","0,0,0,0"),a=1)}}),jQuery(".arm_img_cover_setting").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(l){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=l.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&l.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=l[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&l.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":l=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==l&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),document.getElementById("arm_skip_cvr_crop_nav_front").onclick=function(){var e=jQuery("#arm_crop_cover_image").attr("src"),a=(jQuery("input[name='arm_wp_nonce']").val(),jQuery("#arm_crop_cover_image").attr("data-rotate")),t=jQuery("#arm_crop_cover_image").attr("data-coord");jQuery(this).attr("disabled","disabled"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+e+"&rotate="+a+"&cord="+t+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)),jQuery(".arm_profile_picture_block").css("background-image","url("+e+"?"+Math.floor(100*Math.random())+")"),jQuery("#arm_crop_cover_div_wrapper").bPopup().close(),jQuery("#armRemoveCover").show();e=r.lastIndexOf("/")+1,e=r.substr(e);jQuery("#armRemoveCover").attr("data-cover",e),jQuery("#arm_crop_cover_image").attr("src",""),l.destroy(),jQuery("#arm_crop_cover_image").attr("data-rotate",0),jQuery("#arm_skip_cvr_crop_nav_front").removeAttr("disabled")}})},jQuery("#arm_crop_cover_div_wrapper").bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!0,!0],escClose:!1,modalClose:!1,onClose:function(){l.destroy()}})):jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+r+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)),jQuery(".arm_profile_picture_block").css("background-image","url("+e+"?"+Math.floor(100*Math.random())+")"),jQuery("#arm_crop_cover_div_wrapper").bPopup().close(),jQuery("#armRemoveCover").show();e=r.lastIndexOf("/")+1,e=r.substr(e);jQuery("#armRemoveCover").attr("data-cover",e)}})):"profile"===i&&(-1==r.indexOf("http://")&&-1==r.indexOf("https://")||(r="//"+r.split("://")[1]),0!=jQuery("#arm_crop_div").length?(jQuery("#arm_crop_div").find("img").attr("src",""),jQuery("#arm_crop_div").find("img").attr("src",r),t=0,e=jQuery("#arm_crop_image"),s=new Cropper(e,{viewMode:4,aspectRatio:1,dragCrop:!1,zoomable:!0,rotatable:!0,dashed:!1,dragMode:"move",cropBoxResizable:!1,zoomOnTouch:!1,crop:function(e){jQuery("#arm_crop_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==t&&(jQuery(".arm_clear_button.arm_img_setting").trigger("click"),jQuery("#arm_crop_image").attr("data-coord","0,0,0,0"),t=1)},background:!1}),jQuery(".arm_img_setting").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(s){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=s.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&s.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=s[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&s.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":s=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==s&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),document.getElementById("arm_skip_avtr_crop_nav_front").onclick=function(){var e=jQuery("#arm_crop_image").attr("src"),a=jQuery("#arm_crop_image").attr("data-rotate"),t=jQuery("#arm_crop_image").attr("data-coord");jQuery(this).attr("disabled","disabled"),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=profile&src="+e+"&rotate="+a+"&cord="+t+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery(".arm_user_avatar").find("img").remove();var a=jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)),a=(jQuery(".arm_user_avatar").append(a),jQuery("#armRemoveProfilePic").parent().show(),jQuery(".arm_user_avatar").find("#armRemoveProfilePic").show(),e.split("/")),e=a[Object.keys(a).length-1];jQuery(".arm_user_avatar").find("#armRemoveProfilePic").attr("data-cover",e),jQuery("#armRemoveProfilePic").parent().removeClass("arm_no_profile"),jQuery("#arm_crop_div_wrapper").bPopup().close(),jQuery("#arm_crop_image").attr("src",""),s.destroy(),jQuery("#arm_crop_image").attr("data-rotate",0),jQuery("#arm_skip_avtr_crop_nav_front").removeAttr("disabled")}})},jQuery("#arm_crop_div_wrapper").bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,onClose:function(){s.destroy()}})):jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=profile&src="+r+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery(".arm_user_avatar").find("img").remove();var a=jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)),a=(jQuery(".arm_user_avatar").append(a),jQuery("#armRemoveProfilePic").parent().show(),jQuery(".arm_user_avatar").find("#armRemoveProfilePic").show(),e.split("/")),e=a[Object.keys(a).length-1];jQuery(".arm_user_avatar").find("#armRemoveProfilePic").attr("data-cover",e),jQuery("#armRemoveProfilePic").parent().removeClass("arm_no_profile")}}))}):(alert(fileUploadError),o.val(""))})}jQuery(".armCoverUpload").each(function(){var e=jQuery(this).attr("id");""!=e&&null!=e&&document.getElementById(e).addEventListener("change",a,!1)}),jQuery(".armRemoveCover:not(#armRemoveProfilePic)").click(function(){jQuery(".arm_delete_cover_popup").addClass("armopen").slideDown()})}),jQuery(document).on("click","#armRemoveProfilePic",function(){jQuery(".arm_delete_profile_popup").addClass("armopen").slideDown()}),jQuery(document).on("click",".armIEFileUpload_profile",function(){var r=jQuery(this),o=r.attr("data-iframe"),e=jQuery("#"+o+"_iframe"),a=(e.contents().find("#armfileselect").click(),e.contents().find("#armfileselect").val()),i=r.attr("data-type");if(""!=a&&void 0!==a){var n,d,s=r.parents(".armFileUploadWrapper"),c=(s.find(".armFileBtn"),s.find(".armFileUploadContainer"),s.find(".armFileRemoveContainer"),s.find(".armFileUploadProgressBar"),s.find(".armFileUploadProgressInfo"),r.attr("data-file_size")),t=1024*c*1024,a=(null==r.attr("data-msg-invalid")&&invalidFileTypeError,a.replace(/C:\\fakepath\\/i,"")),l=RandomString(6),p=a.lastIndexOf("."),p=a.substring(p+1),a=/.*(?=\.)/.exec(a),m=r.attr("data-upload-url"),u=("armFile"+l+"_"+a).replace(/[^\w\s]/gi,"").replace(/ /g,"")+"."+p,l=p.toLowerCase(),a=r.attr("accept");if(null!=a&&(p=a.replace(/\./g,"").split(","),-1===jQuery.inArray(l,p)))return r.val(u),r.attr("value",u),r.trigger("change"),!1;-1===jQuery.inArray(l,["php","php3","php4","php5","pl","py","jsp","asp","exe","cgi","gif"])&&("cover"===i?(e.contents().find("form").attr("action",__ARMAJAXURL+"?action=arm_upload_cover&fname="+u+"&allow_size="+t),e.contents().find("form").submit(),s.find(".armUploadedFileName").val(u),n=m+"/"+u,d=setInterval(function(){var a,l,e=jQuery("#"+o+"_iframe").contents().find(".uploaded").length,e=(r.val(u),r.attr("value",u),0<e&&(clearInterval(d),jQuery("#"+o+"_iframe").contents().find(".uploaded").html(),s.find(".armUploadedFileName").val(u),jQuery("#"+o+"_iframe_div").html(" ").append('<iframe id="'+o+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'),r.trigger("change")?(-1==n.indexOf("http://")&&-1==n.indexOf("https://")||(n="//"+n.split("://")[1]),jQuery("#arm_crop_cover_div").find("img").attr("src",n),jQuery("#arm_crop_cover_div_wrapper").bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!0,!0],escClose:!1,modalClose:!1,onClose:function(){l.destroy()}}),a=0,e=jQuery("#arm_crop_cover_image"),l=new Cropper(e,{viewMode:4,aspectRatio:2.86875,zoomable:!0,rotatable:!0,dashed:!1,dragMode:"move",cropBoxResizable:!1,zoomOnTouch:!1,background:!1,crop:function(e){jQuery("#arm_crop_cover_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==a&&(jQuery(".arm_clear_cover_button.arm_img_cover_setting").trigger("click"),jQuery("#arm_crop_cover_image").attr("data-coord","0,0,0,0"),a=1)}}),jQuery(".arm_img_cover_setting").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(l){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=l.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&l.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=l[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&l.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":l=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==l&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),document.getElementsByClassName("arm_crop_cover_button")[0].onclick=function(){var e=jQuery("#arm_crop_cover_image").attr("data-coord"),a=jQuery("#arm_crop_cover_image").attr("src"),t=jQuery("#arm_crop_cover_image").attr("data-rotate");jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_crop_iamge&type=cover&src="+a+"&cord="+e+"&rotate="+t+"&_wpnonce="+jQuery("input[name='arm_wp_nonce']").val(),success:function(e){jQuery("<img />").attr("src",e+"?"+Math.floor(100*Math.random()+1)).on("load",function(){jQuery(".arm_profile_picture_block").css("background-image","url("+e+"?"+Math.floor(100*Math.random())+")")}),jQuery("#arm_crop_cover_div_wrapper").bPopup().close(),jQuery("#armRemoveCover").show(),jQuery("#arm_crop_cover_image").attr("src",""),l.destroy(),jQuery("#arm_crop_cover_image").attr("data-rotate",0)}})}):(jQuery("#arm_crop_cover_div").find("img").remove(),jQuery(".arm_profile_picture_block").css("background-image",""),-1==n.indexOf("http://")&&-1==n.indexOf("https://")||(n="//"+n.split("://")[1]),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_update_user_meta&type="+i+"&image_url="+n,dataType:"json",success:function(){}}),jQuery("<img />").attr("src",n+"?"+Math.floor(100*Math.random()+1)).on("load",function(){jQuery(".arm_profile_picture_block").css("background-image","url("+n+"?"+Math.floor(100*Math.random())+")")}),jQuery("#armRemoveCover").show(),e=n.lastIndexOf("/")+1,e=n.substr(e),jQuery("#armRemoveCover").attr("data-cover",e))),jQuery("#"+o+"_iframe").contents().find(".error_upload").length),t=jQuery("#"+o+"_iframe").contents().find(".error_upload_size").length;(0<e||0<t)&&(clearInterval(d),0<e&&alert(fileUploadError),0<t)&&(e=(e=(e=fileSizeError).replace("{SIZE}",c+"MB")).replace("{size}",c+"MB"),alert(e))},500)):"profile"===i&&(e.contents().find("form").attr("action",__ARMAJAXURL+"?action=arm_upload_profile&fname="+u+"&allow_size="+t),e.contents().find("form").submit(),s.find(".armUploadedFileName").val(u),n=m+"/"+u,d=setInterval(function(){var a,l,e=jQuery("#"+o+"_iframe").contents().find(".uploaded").length,e=(r.val(u),r.attr("value",u),0<e&&(clearInterval(d),jQuery("#"+o+"_iframe").contents().find(".uploaded").html(),s.find(".armUploadedFileName").val(u),jQuery("#"+o+"_iframe_div").html(" ").append('<iframe id="'+o+'_iframe" src="'+__ARMVIEWURL+'/iframeupload.php"></iframe>'),r.trigger("change")?(-1==n.indexOf("http://")&&-1==n.indexOf("https://")||(n="//"+n.split("://")[1]),jQuery("#arm_crop_div").find("img").attr("src",n),a=0,e=jQuery("#arm_crop_image"),l=new Cropper(e,{viewMode:4,aspectRatio:1,dragCrop:!1,zoomable:!0,rotatable:!0,dashed:!1,dragMode:"move",cropBoxResizable:!1,zoomOnTouch:!1,crop:function(e){jQuery("#arm_crop_image").attr("data-coord",Math.round(e.detail.x)+","+Math.round(e.detail.y)+","+Math.round(e.detail.width)+","+Math.round(e.detail.height)),0==a&&(jQuery(".arm_clear_button.arm_img_setting").trigger("click"),jQuery("#arm_crop_image").attr("data-coord","0,0,0,0"),a=1)},background:!1}),jQuery(".arm_rotate_button").on("click",function(e){var a,t,r,o,i=e||window.event,n=i.target||i.srcElement,d=4;if(l){for(;n!==this&&!n.getAttribute("data-method");)n=n.parentNode;if(o={method:n.getAttribute("data-method"),target:n.getAttribute("data-target"),option:n.getAttribute("data-option")||void 0,secondOption:n.getAttribute("data-second-option")||void 0},a=l.cropped,o.method){if(void 0!==o.target&&(r=document.querySelector(o.target),!n.hasAttribute("data-option")&&o.target&&r))try{o.option=JSON.parse(r.value)}catch(i){console.log(i.message)}switch(o.method){case"reset":case"rotate":a&&0<d&&l.clear();break;case"getCroppedCanvas":try{o.option=JSON.parse(o.option)}catch(i){console.log(i.message)}"image/jpeg"===uploadedImageType&&(o.option||(o.option={}),o.option.fillColor="#fff")}switch(t=l[o.method](o.option,o.secondOption),o.method){case"rotate":a&&0<d&&l.crop();break;case"scaleX":case"scaleY":n.setAttribute("data-option",-o.option);break;case"getCroppedCanvas":t&&(jQuery("#getCroppedCanvasModal").modal().find(".modal-body").html(t),download.disabled||(download.download=uploadedImageName,download.href=t.toDataURL(uploadedImageType)));break;case"destroy":l=null,uploadedImageURL&&(URL.revokeObjectURL(uploadedImageURL),uploadedImageURL="",image.src=originalImageURL)}if("object"==typeof t&&t!==l&&r)try{r.value=JSON.stringify(t)}catch(i){console.log(i.message)}}}}),document.getElementsByClassName("arm_crop_button")[0].onclick=function(){return!1},jQuery("#arm_crop_div_wrapper").bPopup({opacity:.5,closeClass:"arm_popup_close_btn",follow:[!1,!1],escClose:!1,modalClose:!1,onClose:function(){l.destroy()}})):(jQuery(".arm_user_avatar").find("img").remove(),-1==n.indexOf("http://")&&-1==n.indexOf("https://")||(n="//"+n.split("://")[1]),jQuery.ajax({type:"POST",url:__ARMAJAXURL,data:"action=arm_update_user_meta&type="+i+"&image_url="+n,dataType:"json",success:function(){}}),jQuery("<img />").attr("src",n+"?"+Math.floor(100*Math.random()+1)).on("load",function(){jQuery(".arm_user_avatar").append(jQuery(this)),jQuery("#armRemoveProfilePic").parent().show(),jQuery(".arm_user_avatar").find("#armRemoveProfilePic").show();var e=(void 0).split("/"),e=e[Object.keys(e).length-1];jQuery(".arm_user_avatar").find("#armRemoveProfilePic").attr("data-cover",e),jQuery("#armRemoveProfilePic").parent().removeClass("arm_no_profile")}))),jQuery("#"+o+"_iframe").contents().find(".error_upload").length),t=jQuery("#"+o+"_iframe").contents().find(".error_upload_size").length;(0<e||0<t)&&(clearInterval(d),0<e&&alert(fileUploadError),0<t)&&(e=(e=(e=fileSizeError).replace("{SIZE}",c+"MB")).replace("{size}",c+"MB"),alert(e))})))}});