<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit7f2698061e7f89cf64b098ce68caa4a0
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Stripe\\' => 7,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Stripe\\' => 
        array (
            0 => __DIR__ . '/..' . '/stripe/stripe-php/lib',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit7f2698061e7f89cf64b098ce68caa4a0::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit7f2698061e7f89cf64b098ce68caa4a0::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
