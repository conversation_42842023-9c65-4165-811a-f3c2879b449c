<?php
/**
 * @since 2.5.0
 * @version 2.0
 */

// Function to get genres safely
function dooplay_get_genres_for_options() {
    // Ensure taxonomy is registered
    if (!taxonomy_exists('genres')) {
        return array();
    }

    $all_genres = get_terms(array(
        'taxonomy' => 'genres',
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    $genres_for_select = array();
    if (!empty($all_genres) && !is_wp_error($all_genres)) {
        foreach ($all_genres as $genre) {
            $genres_for_select[$genre->term_id] = $genre->name . ' (' . $genre->count . ' posts)';
        }
    }

    // If no genres found, add a default message
    if (empty($genres_for_select)) {
        $genres_for_select = array(
            '' => 'No genres found. Please add genres first.'
        );
    }

    return $genres_for_select;
}

// Get genres for select field
$genres_for_select = dooplay_get_genres_for_options();

// Callback function for dynamic genre loading
function dooplay_get_genres_for_select_callback() {
    $all_genres = get_terms(array(
        'taxonomy' => 'genres',
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    $options = array();
    if (!empty($all_genres) && !is_wp_error($all_genres)) {
        foreach ($all_genres as $genre) {
            $options[$genre->term_id] = $genre->name . ' (' . $genre->count . ' posts)';
        }
    }

    return $options;
}

CSF::createSection(DOO_OPTIONS,
    array(
        'title'  => __d('Genres Menu'),
        'parent' => 'homepage',
        'icon'   => 'fa fa-tags',
        'fields' => array(
            array(
                'id'      => 'genresmenu_title',
                'type'    => 'text',
                'title'   => __d('Module Title'),
                'default' => __d('Browse Genres'),
                'subtitle'    => __d('Add title to show above genres menu')
            ),
            array(
                'id'      => 'genresmenu_show_count',
                'type'    => 'switcher',
                'title'   => __d('Show Count'),
                'subtitle' => __d('Show number of posts in each genre'),
                'default' => true
            ),
            array(
                'id'      => 'genresmenu_layout',
                'type'    => 'select',
                'title'   => __d('Display Layout'),
                'subtitle' => __d('Choose how to display genres'),
                'options' => array(
                    'all' => __d('Show All Genres (Default Order)'),
                    'selected' => __d('Show Selected Genres Only'),
                    'excluded' => __d('Show All Except Selected')
                ),
                'default' => 'all'
            ),
            array(
                'id'      => 'genresmenu_selected_genres',
                'type'    => 'textarea',
                'title'   => __d('Genre IDs'),
                'subtitle' => __d('Enter genre IDs separated by commas (e.g., 1,2,3). <a href="' . get_site_url() . '/genre_ids_helper.php" target="_blank">View Genre IDs Helper</a> | <a href="' . admin_url('edit-tags.php?taxonomy=genres') . '" target="_blank">Manage Genres</a>'),
                'placeholder' => __d('e.g., 1,2,3,4'),
                'dependency' => array('genresmenu_layout', 'any', 'selected,excluded'),
                'attributes' => array(
                    'rows' => 3,
                    'style' => 'width: 100%; max-width: 400px;'
                ),
                'default' => ''
            ),
            array(
                'id'      => 'genresmenu_order',
                'type'    => 'select',
                'title'   => __d('Order By'),
                'subtitle' => __d('How to order the genres'),
                'options' => array(
                    'name' => __d('Name (A-Z)'),
                    'count' => __d('Post Count (Most to Least)'),
                    'id' => __d('Date Added (Newest First)')
                ),
                'default' => 'name'
            ),
            array(
                'id'      => 'genresmenu_initial_show',
                'type'    => 'number',
                'title'   => __d('Initial Show Count'),
                'subtitle' => __d('Number of genres to show initially (rest will be hidden behind "See More" button)'),
                'default' => 8,
                'unit' => 'genres',
                'attributes' => array(
                    'min' => 4,
                    'max' => 20,
                    'step' => 1
                )
            ),
            array(
                'type'    => 'notice',
                'style'   => 'info',
                'content' => __d('<strong>How it works:</strong><br>
                • <strong>Show All Genres:</strong> Displays all genres<br>
                • <strong>Show Selected Genres Only:</strong> Shows only the genres you select<br>
                • <strong>Show All Except Selected:</strong> Shows all genres except the ones you select<br>
                • <strong>Manage Genres:</strong> Go to <a href="' . admin_url('edit-tags.php?taxonomy=genres') . '" target="_blank">Appearance > Genres</a> to add, edit or delete genres.')
            )
        )
    )
);
