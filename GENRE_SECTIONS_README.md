# জন্রা সেকশন মডিউল - DeshiFlix

## বিবরণ
এই মডিউলটি আপনাকে হোম পেজে জন্রা নাম দিয়ে আলাদা আলাদা সেকশন যুক্ত করার সুবিধা দেয়। আপনি ম্যানুয়ালি জন্রা সিলেক্ট করে প্রতিটি জন্রার জন্য আলাদা সেকশন তৈরি করতে পারবেন।

## ফিচারসমূহ

### ✅ মূল ফিচার
- **ম্যানুয়াল জন্রা সিলেকশন**: আপনি যে জন্রাগুলো দেখাতে চান সেগুলো সিলেক্ট করুন
- **৩টি লেআউট অপশন**: Grid, Carousel, এবং Tabs
- **কাস্টমাইজেবল ডিজাইন**: কলাম সংখ্যা, আইটেম সংখ্যা ইত্যাদি
- **রেসপন্সিভ ডিজাইন**: সব ডিভাইসে সুন্দর দেখায়
- **AJAX লোড মোর**: আর<PERSON> কন্টেন্ট লোড করার সুবিধা

### 🎨 লেআউট অপশন
1. **Grid Layout**: গ্রিড আকারে প্রতিটি জন্রার সেকশন
2. **Carousel Layout**: স্লাইডার আকারে কন্টেন্ট
3. **Tabs Layout**: ট্যাব আকারে জন্রা সুইচ করার সুবিধা

### ⚙️ কনফিগারেশন অপশন
- মডিউল টাইটেল ও সাবটাইটেল
- প্রতি সেকশনে আইটেম সংখ্যা (৪-২০)
- কলাম সংখ্যা (২-৮)
- কন্টেন্ট টাইপ (Movies, TV Shows, Episodes)
- সর্টিং অপশন (Latest, Alphabetical, Rating, Views, Random)
- জন্রা অর্ডারিং (Custom, Alphabetical, By Count, Random)

## ইনস্টলেশন ও সেটআপ

### ১. ফাইল স্ট্রাকচার
নিম্নলিখিত ফাইলগুলো তৈরি/আপডেট করা হয়েছে:

```
wp-content/themes/dooplay/
├── inc/
│   ├── parts/modules/genre-sections.php (নতুন মডিউল)
│   ├── csf/options.genre_sections.php (অ্যাডমিন অপশন)
│   └── doo_options.php (আপডেট)
├── assets/
│   ├── css/genre-sections.css (স্টাইল)
│   └── js/genre-sections.js (জাভাস্ক্রিপ্ট)
└── functions.php (আপডেট)
```

### ২. অ্যাডমিন সেটআপ

#### স্টেপ ১: মডিউল এনাবল করুন
1. WordPress অ্যাডমিন → Appearance → DooPlay Options
2. **Homepage Modules** → **Customize** সেকশনে যান
3. **Homepage modules** এ **Genre Sections** কে **Enabled** সেকশনে drag করুন

#### স্টেপ ২: জন্রা সেকশন কনফিগার করুন
1. **Homepage Modules** → **Genre Sections** এ যান
2. **Enable Genre Sections** চালু করুন
3. আপনার পছন্দমতো সেটিংস করুন:

**বেসিক সেটিংস:**
- **Module Title**: "Browse by Genre" (বা আপনার পছন্দের টাইটেল)
- **Module Subtitle**: "Discover content by your favorite genres"
- **Layout Style**: Grid/Carousel/Tabs থেকে একটি বেছে নিন

**কন্টেন্ট সেটিংস:**
- **Items per Section**: প্রতি সেকশনে কতটি আইটেম দেখাবেন (৮ রেকমেন্ডেড)
- **Columns per Row**: প্রতি রোতে কতটি কলাম (৪ রেকমেন্ডেড)
- **Content Types**: Movies, TV Shows, Episodes থেকে বেছে নিন
- **Content Sorting**: Latest First রেকমেন্ডেড

**জন্রা সিলেকশন:**
- **Select Genres**: যে জন্রাগুলো দেখাতে চান সেগুলো সিলেক্ট করুন
- **Genre Order**: Custom Order রেকমেন্ডেড

#### স্টেপ ৩: সেভ করুন
সব সেটিংস করার পর **Save Changes** বাটনে ক্লিক করুন।

## ব্যবহারের নির্দেশনা

### জন্রা সিলেক্ট করা
1. **Select Genres** ফিল্ডে ক্লিক করুন
2. ড্রপডাউন থেকে আপনার পছন্দের জন্রাগুলো সিলেক্ট করুন
3. একাধিক জন্রা সিলেক্ট করতে পারবেন
4. সিলেক্ট করা জন্রাগুলো হোম পেজে আলাদা সেকশন হিসেবে দেখাবে

### লেআউট পরিবর্তন
- **Grid**: প্রতিটি জন্রার জন্য আলাদা সেকশন
- **Carousel**: স্লাইডার আকারে কন্টেন্ট
- **Tabs**: ট্যাব আকারে জন্রা সুইচ

### কাস্টমাইজেশন
- **Columns**: ২-৮ কলাম পর্যন্ত সেট করতে পারবেন
- **Items**: ৪-২০ আইটেম পর্যন্ত প্রতি সেকশনে
- **Sorting**: বিভিন্ন সর্টিং অপশন

## CSS কাস্টমাইজেশন

### কালার পরিবর্তন
```css
/* প্রাইমারি কালার */
:root {
    --primary-color: #fff;
    --accent-color: #e74c3c;
    --text-muted: #999;
    --section-bg: rgba(255,255,255,0.05);
    --border-color: rgba(255,255,255,0.1);
}
```

### টাইটেল স্টাইল
```css
.genre-sections-title {
    font-size: 2.5rem;
    color: #your-color;
}
```

### সেকশন ব্যাকগ্রাউন্ড
```css
.genre-section {
    background: your-background;
    border-radius: 12px;
}
```

## ট্রাবলশুটিং

### সমস্যা: জন্রা সেকশন দেখাচ্ছে না
**সমাধান:**
1. চেক করুন মডিউল এনাবল আছে কিনা
2. Homepage modules এ Genre Sections enabled আছে কিনা
3. কমপক্ষে একটি জন্রা সিলেক্ট করা আছে কিনা

### সমস্যা: কন্টেন্ট লোড হচ্ছে না
**সমাধান:**
1. সিলেক্ট করা জন্রায় কন্টেন্ট আছে কিনা চেক করুন
2. Content Types সেটিং চেক করুন
3. WordPress cache ক্লিয়ার করুন

### সমস্যা: স্টাইল ঠিক দেখাচ্ছে না
**সমাধান:**
1. Browser cache ক্লিয়ার করুন
2. CSS ফাইল সঠিকভাবে লোড হচ্ছে কিনা চেক করুন
3. Theme এর অন্য CSS এর সাথে conflict আছে কিনা দেখুন

## উদাহরণ সেটআপ

### জনপ্রিয় জন্রা সেটআপ
```
Selected Genres:
- Action (৫০ items)
- Comedy (৩৫ items)  
- Drama (৪২ items)
- Horror (২৮ items)
- Romance (৩১ items)
- Thriller (৩৯ items)

Layout: Grid
Items per Section: 8
Columns: 4
Content Types: Movies, TV Shows
Sorting: Latest First
```

### ট্যাব লেআউট সেটআপ
```
Layout: Tabs
Selected Genres: Action, Comedy, Drama, Horror
Items per Section: 12
Columns: 6
Content Sorting: By Rating
```

## সাপোর্ট

কোনো সমস্যা হলে:
1. প্রথমে ট্রাবলশুটিং সেকশন দেখুন
2. WordPress error log চেক করুন
3. Browser console এ error আছে কিনা দেখুন

## আপডেট নোট

### ভার্সন ১.০
- প্রাথমিক রিলিজ
- ৩টি লেআউট অপশন
- সম্পূর্ণ কাস্টমাইজেশন সুবিধা
- AJAX লোড মোর ফিচার
- রেসপন্সিভ ডিজাইন
