<?php
// Test file to demonstrate premium download feature
// This simulates how the premium feature works

// Mock function to simulate ARMember check
function mock_is_user_premium($simulate_premium = false) {
    return $simulate_premium;
}

// Simulate premium status (change this to test different scenarios)
$simulate_premium_user = isset($_GET['premium']) ? (bool)$_GET['premium'] : false;
$is_premium = mock_is_user_premium($simulate_premium_user);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Download Feature Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .status-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #dee2e6;
        }
        
        .premium-notification {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }
        
        .download-links-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 1px solid rgba(255,255,255,0.2);
            table-layout: fixed;
        }
        
        .download-links-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 18px 12px;
            text-align: center;
            font-weight: 700;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .download-links-table td {
            padding: 20px 12px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            vertical-align: middle;
            font-size: 14px;
            text-align: center;
            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
        }
        
        .download-links-table tr:hover td {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .download-btn, .play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 11px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 2px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049, #2e7d32);
            color: white;
            border: 2px solid transparent;
        }
        
        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
            border-color: #4CAF50;
        }
        
        .premium-download {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00) !important;
            color: #000 !important;
            font-weight: 700;
            position: relative;
            overflow: hidden;
        }
        
        .premium-download::after {
            content: '⭐';
            position: absolute;
            top: 2px;
            right: 4px;
            font-size: 10px;
            animation: sparkle 2s infinite;
        }
        
        .premium-download:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6) !important;
            border-color: #FFD700 !important;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        
        .play-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2, #0D47A1);
            color: white;
            border: 2px solid transparent;
        }
        
        .play-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
            border-color: #2196F3;
        }
        
        .quality-badge {
            background: linear-gradient(135deg, #FF5722, #E64A19, #BF360C);
            color: white;
            padding: 8px 12px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
            letter-spacing: 0.5px;
        }
        
        .language-flag {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 12px;
            background: linear-gradient(135deg, #9C27B0, #7B1FA2, #4A148C);
            color: white;
            padding: 8px 12px;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
            letter-spacing: 0.5px;
        }
        
        .size-info {
            background: linear-gradient(135deg, #FF9800, #F57C00, #E65100);
            color: white;
            padding: 8px 14px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 13px;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
            letter-spacing: 0.5px;
            min-width: 60px;
            word-wrap: break-word;
        }
        
        .premium-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            color: #000;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
            letter-spacing: 0.5px;
            animation: glow 2s ease-in-out infinite alternate;
        }

        .premium-badge-locked {
            background: linear-gradient(135deg, #dc3545, #c82333, #bd2130);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4);
            letter-spacing: 0.5px;
            animation: pulse 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4); }
            to { box-shadow: 0 4px 16px rgba(255, 215, 0, 0.8); }
        }

        @keyframes pulse {
            from { box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4); }
            to { box-shadow: 0 4px 16px rgba(220, 53, 69, 0.8); }
        }

        .premium-locked {
            background: linear-gradient(135deg, #6c757d, #5a6268, #495057) !important;
            color: white !important;
        }

        .premium-locked:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4) !important;
            border-color: #6c757d !important;
        }
        
        .toggle-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .toggle-links a {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .toggle-links .regular {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
        }
        
        .toggle-links .premium {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
        }
        
        .toggle-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Premium Download Feature Demo</h1>
        
        <div class="toggle-links">
            <a href="?premium=0" class="regular">
                <i class="fas fa-user"></i> View as Regular User
            </a>
            <a href="?premium=1" class="premium">
                <i class="fas fa-crown"></i> View as Premium User
            </a>
        </div>
        
        <div class="status-info">
            <strong>Current Status:</strong> 
            <?php if ($is_premium): ?>
                <span style="color: #FFD700;"><i class="fas fa-crown"></i> Premium User</span>
            <?php else: ?>
                <span style="color: #6c757d;"><i class="fas fa-user"></i> Regular User</span>
            <?php endif; ?>
        </div>
        
        <?php if ($is_premium): ?>
        <div class="premium-notification">
            <i class="fas fa-crown" style="margin-right: 8px; color: #FF8C00;"></i>
            Premium Member: You have access to direct download links without any delay!
            <i class="fas fa-star" style="margin-left: 8px; color: #FF8C00;"></i>
        </div>
        <?php endif; ?>
        
        <table class="download-links-table">
            <thead>
                <tr>
                    <th>Quality</th>
                    <th>Language</th>
                    <th>Size</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $download_links = [
                    ['quality' => '4K', 'language' => 'English', 'size' => '8.5 GB', 'url' => 'https://example.com/movie-4k.mp4', 'is_premium' => true],
                    ['quality' => '1080p', 'language' => 'English', 'size' => '2.5 GB', 'url' => 'https://example.com/movie-1080p.mp4', 'is_premium' => false],
                    ['quality' => '720p', 'language' => 'English', 'size' => '1.2 GB', 'url' => 'https://example.com/movie-720p.mp4', 'is_premium' => true],
                    ['quality' => '480p', 'language' => 'English', 'size' => '800 MB', 'url' => 'https://example.com/movie-480p.mp4', 'is_premium' => false],
                ];
                
                foreach ($download_links as $index => $link):
                    // Check if this specific link is marked as premium
                    $is_link_premium = $link['is_premium'];

                    // Only show premium badge if this link is specifically marked as premium
                    $premium_badge = '';
                    if ($is_link_premium) {
                        if ($is_premium) {
                            // User is premium - show unlocked badge
                            $premium_badge = '<span class="premium-badge">PREMIUM</span>';
                        } else {
                            // User is not premium - show locked badge
                            $premium_badge = '<span class="premium-badge-locked"><i class="fas fa-lock" style="margin-right: 4px; font-size: 8px;"></i>PREMIUM</span>';
                        }
                    }
                ?>
                <tr>
                    <td>
                        <span class="quality-badge"><?php echo $link['quality']; ?></span>
                        <?php echo $premium_badge; ?>
                    </td>
                    <td><span class="language-flag"><?php echo $link['language']; ?></span></td>
                    <td><span class="size-info"><?php echo $link['size']; ?></span></td>
                    <td>
                        <?php if ($is_link_premium): ?>
                            <?php if ($is_premium): ?>
                                <!-- Premium user accessing premium link - direct download -->
                                <a href="<?php echo $link['url']; ?>" class="download-btn premium-download" target="_blank" rel="nofollow">
                                    <i class="fas fa-download"></i> Direct Download
                                </a>
                            <?php else: ?>
                                <!-- Regular user accessing premium link - show locked message -->
                                <a href="#" class="download-btn premium-locked" onclick="alert('This is a Premium link! Upgrade your membership to access direct downloads without delay.'); return false;">
                                    <i class="fas fa-lock" style="margin-right: 4px; font-size: 10px;"></i> Premium Only
                                </a>
                            <?php endif; ?>
                        <?php else: ?>
                            <?php if ($is_premium): ?>
                                <!-- Premium user accessing regular link - still gets direct download benefit -->
                                <a href="<?php echo $link['url']; ?>" class="download-btn" target="_blank" rel="nofollow">
                                    <i class="fas fa-download"></i> Direct Download
                                </a>
                            <?php else: ?>
                                <!-- Regular user accessing regular link - normal short link process -->
                                <a href="#" class="download-btn" onclick="alert('Regular User: Redirecting to short link with 10 second delay...'); return false;">
                                    <i class="fas fa-download"></i> Download
                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                        <a href="#" class="play-btn">
                            <i class="fas fa-play"></i> Play Now
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>How it works:</h3>
            <ul>
                <li><strong>Individual Link Control:</strong> Each link can be individually marked as premium or regular</li>
                <li><strong>Premium Links:</strong> Show premium badge and require membership for direct access</li>
                <li><strong>Regular Links:</strong> No premium badge, but premium users still get direct download benefit</li>
                <li><strong>Mixed Content:</strong> You can have both premium and regular links in the same post</li>
                <li><strong>ARMember Integration:</strong> Automatically detects user's membership status</li>
            </ul>

            <h4>Link Behavior:</h4>
            <ul>
                <li>🌟 <strong>Premium Links + Premium User:</strong> Golden badge + Direct download</li>
                <li>🔒 <strong>Premium Links + Regular User:</strong> Locked badge + Upgrade message</li>
                <li>⚡ <strong>Regular Links + Premium User:</strong> No badge + Direct download (premium benefit)</li>
                <li>📥 <strong>Regular Links + Regular User:</strong> No badge + Normal download (10s delay)</li>
            </ul>

            <h4>Benefits:</h4>
            <ul>
                <li>🎯 <strong>Flexible Control:</strong> Mark only specific high-quality links as premium</li>
                <li>💰 <strong>Revenue Optimization:</strong> Premium users get value on all links</li>
                <li>🔍 <strong>Clear Indication:</strong> Users know exactly what requires premium</li>
                <li>⚖️ <strong>Balanced Experience:</strong> Free users still get access to regular links</li>
            </ul>
        </div>
    </div>
</body>
</html>
