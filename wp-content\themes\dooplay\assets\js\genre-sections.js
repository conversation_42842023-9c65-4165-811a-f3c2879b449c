/**
 * Genre Sections Module JavaScript
 * @since 2.5.0
 * @version 1.0
 */

(function($) {
    'use strict';

    // Initialize Genre Sections
    function initGenreSections() {
        initTabs();
        initCarousels();
        initLazyLoading();
        initAnimations();
    }

    // Initialize Tab Functionality
    function initTabs() {
        $('.genre-tabs-nav .genre-tab a').on('click', function(e) {
            e.preventDefault();
            
            const $this = $(this);
            const $tab = $this.parent();
            const $tabsNav = $tab.parent();
            const $tabsContent = $tabsNav.siblings('.genre-tabs-content');
            const targetId = $this.attr('href');
            
            // Remove active class from all tabs
            $tabsNav.find('.genre-tab').removeClass('active');
            $tabsContent.find('.genre-tab-content').removeClass('active');
            
            // Add active class to clicked tab
            $tab.addClass('active');
            $(targetId).addClass('active');
            
            // Smooth scroll to active tab if needed
            const tabOffset = $this.offset().left;
            const navWidth = $tabsNav.width();
            const tabWidth = $this.outerWidth();
            
            if (tabOffset + tabWidth > navWidth) {
                $tabsNav.animate({
                    scrollLeft: $tabsNav.scrollLeft() + tabOffset - navWidth + tabWidth + 20
                }, 300);
            } else if (tabOffset < 0) {
                $tabsNav.animate({
                    scrollLeft: $tabsNav.scrollLeft() + tabOffset - 20
                }, 300);
            }
        });
    }

    // Initialize Carousel Functionality
    function initCarousels() {
        $('.genre-carousel').each(function() {
            const $carousel = $(this);
            const genreId = $carousel.attr('id').replace('genre-carousel-', '');
            
            // Check if owl carousel is available
            if (typeof $.fn.owlCarousel !== 'undefined') {
                $carousel.owlCarousel({
                    items: 6,
                    autoPlay: $carousel.data('autoplay') || false,
                    autoPlayTimeout: $carousel.data('speed') || 5000,
                    stopOnHover: true,
                    pagination: false,
                    navigation: true,
                    navigationText: [
                        '<i class="fas fa-chevron-left"></i>',
                        '<i class="fas fa-chevron-right"></i>'
                    ],
                    responsive: {
                        0: { items: 2 },
                        480: { items: 3 },
                        768: { items: 4 },
                        992: { items: 5 },
                        1200: { items: 6 }
                    },
                    afterInit: function() {
                        $carousel.find('.owl-controls').addClass('genre-carousel-controls');
                    }
                });
            } else {
                // Fallback: Simple horizontal scroll
                initSimpleCarousel($carousel);
            }
        });
    }

    // Simple Carousel Fallback
    function initSimpleCarousel($carousel) {
        const $wrapper = $('<div class="simple-carousel-wrapper"></div>');
        const $container = $('<div class="simple-carousel-container"></div>');
        const $prevBtn = $('<button class="carousel-btn prev"><i class="fas fa-chevron-left"></i></button>');
        const $nextBtn = $('<button class="carousel-btn next"><i class="fas fa-chevron-right"></i></button>');
        
        $carousel.wrap($wrapper);
        $carousel.wrap($container);
        $carousel.parent().before($prevBtn).after($nextBtn);
        
        const itemWidth = 220; // Item width + margin
        let currentPosition = 0;
        const maxPosition = ($carousel.find('.carousel-item').length - 4) * itemWidth;
        
        $prevBtn.on('click', function() {
            currentPosition = Math.max(0, currentPosition - itemWidth * 2);
            $carousel.css('transform', `translateX(-${currentPosition}px)`);
            updateButtons();
        });
        
        $nextBtn.on('click', function() {
            currentPosition = Math.min(maxPosition, currentPosition + itemWidth * 2);
            $carousel.css('transform', `translateX(-${currentPosition}px)`);
            updateButtons();
        });
        
        function updateButtons() {
            $prevBtn.toggleClass('disabled', currentPosition <= 0);
            $nextBtn.toggleClass('disabled', currentPosition >= maxPosition);
        }
        
        updateButtons();
    }

    // Initialize Lazy Loading
    function initLazyLoading() {
        if (typeof window.IntersectionObserver !== 'undefined') {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.dataset.src;
                        
                        if (src) {
                            img.src = src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        }
                    }
                });
            });

            document.querySelectorAll('.genre-sections-module img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    // Initialize Animations
    function initAnimations() {
        if (typeof window.IntersectionObserver !== 'undefined') {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.genre-section').forEach(section => {
                animationObserver.observe(section);
            });
        }
    }

    // AJAX Load More Content
    function loadMoreContent(genreId, page = 2) {
        const $section = $(`.genre-section[data-genre="${genreId}"]`);
        const $loadMoreBtn = $section.find('.load-more-btn');
        
        $loadMoreBtn.addClass('loading').text('Loading...');
        
        $.ajax({
            url: dooplay_ajax_url || '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'load_more_genre_content',
                genre_id: genreId,
                page: page,
                nonce: dooplay_nonce || ''
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $section.find('.genre-content-grid, .genre-carousel').append(response.data.html);
                    
                    if (!response.data.has_more) {
                        $loadMoreBtn.remove();
                    } else {
                        $loadMoreBtn.removeClass('loading').text('Load More').data('page', page + 1);
                    }
                    
                    // Reinitialize lazy loading for new content
                    initLazyLoading();
                } else {
                    $loadMoreBtn.removeClass('loading').text('Load More');
                }
            },
            error: function() {
                $loadMoreBtn.removeClass('loading').text('Load More');
            }
        });
    }

    // Handle Load More Clicks
    $(document).on('click', '.load-more-btn', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        const genreId = $btn.data('genre');
        const page = $btn.data('page') || 2;
        
        loadMoreContent(genreId, page);
    });

    // Smooth Scroll for See All Links
    $(document).on('click', '.genre-see-all[href^="#"]', function(e) {
        e.preventDefault();
        
        const target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Keyboard Navigation for Tabs
    $(document).on('keydown', '.genre-tabs-nav .genre-tab a', function(e) {
        const $tabs = $(this).closest('.genre-tabs-nav').find('.genre-tab a');
        const currentIndex = $tabs.index(this);
        
        switch(e.which) {
            case 37: // Left arrow
                e.preventDefault();
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                $tabs.eq(prevIndex).click().focus();
                break;
            case 39: // Right arrow
                e.preventDefault();
                const nextIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                $tabs.eq(nextIndex).click().focus();
                break;
        }
    });

    // Initialize on Document Ready
    $(document).ready(function() {
        initGenreSections();
    });

    // Reinitialize on AJAX content load
    $(document).on('dooplay_content_loaded', function() {
        initGenreSections();
    });

    // Expose functions globally if needed
    window.GenreSections = {
        init: initGenreSections,
        loadMore: loadMoreContent
    };

})(jQuery);
