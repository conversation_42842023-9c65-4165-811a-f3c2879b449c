/**
 * Live TV Player JavaScript
 * @since 2.5.0
 */

(function($) {
    'use strict';

    // Initialize Live TV functionality
    $(document).ready(function() {
        initializeLiveTVPlayer();
        initializeChannelCards();
        initializePlayerControls();
    });

    // Initialize Live TV Player
    function initializeLiveTVPlayer() {
        // Check if HLS.js is supported
        if (Hls.isSupported()) {
            window.liveTVHls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90
            });
        }
    }

    // Initialize Channel Cards
    function initializeChannelCards() {
        $('.live-tv-channel-card').on('click', function() {
            const streamUrl = $(this).data('stream');
            const channelTitle = $(this).find('.channel-title').text();
            
            if (streamUrl) {
                playChannel(streamUrl, channelTitle);
            }
        });
    }

    // Play Channel
    function playChannel(streamUrl, channelTitle) {
        const modal = $('#live-tv-player-modal');
        const player = $('#live-tv-player')[0];
        const titleElement = $('#player-channel-title');
        
        // Update modal title
        titleElement.text(channelTitle);
        
        // Show modal
        modal.fadeIn(300);
        
        // Load stream
        if (Hls.isSupported()) {
            if (window.liveTVHls) {
                window.liveTVHls.destroy();
            }
            
            window.liveTVHls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 600,
                maxBufferSize: 60 * 1000 * 1000,
                maxBufferHole: 0.5,
                highBufferWatchdogPeriod: 2,
                nudgeOffset: 0.1,
                nudgeMaxRetry: 3,
                maxFragLookUpTolerance: 0.25,
                liveSyncDurationCount: 3,
                liveMaxLatencyDurationCount: Infinity,
                liveDurationInfinity: false,
                enableSoftwareAES: true,
                manifestLoadingTimeOut: 10000,
                manifestLoadingMaxRetry: 1,
                manifestLoadingRetryDelay: 1000,
                levelLoadingTimeOut: 10000,
                levelLoadingMaxRetry: 4,
                levelLoadingRetryDelay: 1000,
                fragLoadingTimeOut: 20000,
                fragLoadingMaxRetry: 6,
                fragLoadingRetryDelay: 1000,
                startFragPrefetch: false,
                testBandwidth: true
            });
            
            window.liveTVHls.loadSource(streamUrl);
            window.liveTVHls.attachMedia(player);
            
            window.liveTVHls.on(Hls.Events.MANIFEST_PARSED, function() {
                player.play().catch(function(error) {
                    console.log('Auto-play prevented:', error);
                    showPlayButton();
                });
            });
            
            window.liveTVHls.on(Hls.Events.ERROR, function(event, data) {
                console.error('HLS Error:', data);
                handleStreamError(data);
            });
            
        } else if (player.canPlayType('application/vnd.apple.mpegurl')) {
            // Native HLS support (Safari)
            player.src = streamUrl;
            player.play().catch(function(error) {
                console.log('Auto-play prevented:', error);
                showPlayButton();
            });
        } else {
            // Fallback for unsupported browsers
            showUnsupportedMessage();
        }
        
        // Add loading indicator
        showLoadingIndicator();
        
        // Remove loading indicator when video starts playing
        $(player).on('loadstart playing', function() {
            hideLoadingIndicator();
        });
    }

    // Initialize Player Controls
    function initializePlayerControls() {
        // Close modal
        $('#close-player, .live-tv-modal').on('click', function(e) {
            if (e.target === this) {
                closePlayer();
            }
        });
        
        // Prevent modal close when clicking on content
        $('.modal-content').on('click', function(e) {
            e.stopPropagation();
        });
        
        // Fullscreen toggle
        $('#player-fullscreen').on('click', function() {
            toggleFullscreen();
        });
        
        // Volume control
        $('#player-volume').on('click', function() {
            toggleMute();
        });
        
        // Keyboard controls
        $(document).on('keydown', function(e) {
            if ($('#live-tv-player-modal').is(':visible')) {
                switch(e.keyCode) {
                    case 27: // Escape
                        closePlayer();
                        break;
                    case 32: // Space
                        e.preventDefault();
                        togglePlayPause();
                        break;
                    case 70: // F
                        toggleFullscreen();
                        break;
                    case 77: // M
                        toggleMute();
                        break;
                }
            }
        });
    }

    // Close Player
    function closePlayer() {
        const modal = $('#live-tv-player-modal');
        const player = $('#live-tv-player')[0];
        
        // Stop playback
        player.pause();
        player.src = '';
        
        // Destroy HLS instance
        if (window.liveTVHls) {
            window.liveTVHls.destroy();
            window.liveTVHls = null;
        }
        
        // Hide modal
        modal.fadeOut(300);
        
        // Exit fullscreen if active
        if (document.fullscreenElement) {
            document.exitFullscreen();
        }
    }

    // Toggle Fullscreen
    function toggleFullscreen() {
        const playerContainer = $('#live-tv-player-container')[0];
        
        if (!document.fullscreenElement) {
            if (playerContainer.requestFullscreen) {
                playerContainer.requestFullscreen();
            } else if (playerContainer.webkitRequestFullscreen) {
                playerContainer.webkitRequestFullscreen();
            } else if (playerContainer.msRequestFullscreen) {
                playerContainer.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    // Toggle Play/Pause
    function togglePlayPause() {
        const player = $('#live-tv-player')[0];
        
        if (player.paused) {
            player.play();
        } else {
            player.pause();
        }
    }

    // Toggle Mute
    function toggleMute() {
        const player = $('#live-tv-player')[0];
        const volumeBtn = $('#player-volume i');
        
        player.muted = !player.muted;
        
        if (player.muted) {
            volumeBtn.removeClass('fa-volume-up').addClass('fa-volume-mute');
        } else {
            volumeBtn.removeClass('fa-volume-mute').addClass('fa-volume-up');
        }
    }

    // Show Loading Indicator
    function showLoadingIndicator() {
        const container = $('#live-tv-player-container');
        
        if (!container.find('.loading-indicator').length) {
            container.append(`
                <div class="loading-indicator">
                    <div class="spinner"></div>
                    <p>Loading channel...</p>
                </div>
            `);
        }
    }

    // Hide Loading Indicator
    function hideLoadingIndicator() {
        $('.loading-indicator').remove();
    }

    // Show Play Button
    function showPlayButton() {
        const container = $('#live-tv-player-container');
        
        if (!container.find('.manual-play-btn').length) {
            container.append(`
                <div class="manual-play-btn">
                    <button class="play-btn-large">
                        <i class="fas fa-play"></i>
                        <span>Click to Play</span>
                    </button>
                </div>
            `);
            
            $('.play-btn-large').on('click', function() {
                const player = $('#live-tv-player')[0];
                player.play();
                $(this).parent().remove();
            });
        }
    }

    // Show Unsupported Message
    function showUnsupportedMessage() {
        const container = $('#live-tv-player-container');
        container.html(`
            <div class="unsupported-message">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Browser Not Supported</h3>
                <p>Your browser doesn't support live streaming. Please try using a modern browser like Chrome, Firefox, or Safari.</p>
            </div>
        `);
    }

    // Handle Stream Errors
    function handleStreamError(data) {
        console.error('Stream error:', data);
        
        const container = $('#live-tv-player-container');
        let errorMessage = 'Unable to load the stream. ';
        
        switch(data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
                errorMessage += 'Network connection issue.';
                break;
            case Hls.ErrorTypes.MEDIA_ERROR:
                errorMessage += 'Media format not supported.';
                break;
            case Hls.ErrorTypes.OTHER_ERROR:
                errorMessage += 'Unknown error occurred.';
                break;
            default:
                errorMessage += 'Please try again later.';
        }
        
        container.html(`
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Stream Error</h3>
                <p>${errorMessage}</p>
                <button class="retry-btn" onclick="location.reload()">
                    <i class="fas fa-redo"></i>
                    Retry
                </button>
            </div>
        `);
    }

    // Add CSS for loading and error states
    $('<style>').text(`
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 10;
        }
        
        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #e74c3c;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .manual-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        
        .play-btn-large {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 10px;
            font-size: 1.2em;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .play-btn-large:hover {
            background: #c0392b;
        }
        
        .error-message,
        .unsupported-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            background: rgba(0,0,0,0.8);
            padding: 30px;
            border-radius: 10px;
            z-index: 10;
        }
        
        .error-message i,
        .unsupported-message i {
            font-size: 3em;
            margin-bottom: 15px;
            color: #e74c3c;
        }
        
        .retry-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .retry-btn:hover {
            background: #c0392b;
        }
    `).appendTo('head');

})(jQuery);
