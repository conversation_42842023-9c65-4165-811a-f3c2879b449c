<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Download Feature Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .premium-notification {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }
        
        .download-links-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 1px solid rgba(255,255,255,0.2);
            table-layout: fixed;
        }
        
        .download-links-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 18px 12px;
            text-align: center;
            font-weight: 700;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        
        .download-links-table td {
            padding: 20px 12px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            vertical-align: middle;
            font-size: 14px;
            text-align: center;
            background: linear-gradient(135deg, #fafafa 0%, #f5f7fa 100%);
        }
        
        .download-links-table tr:hover td {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .download-btn, .play-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 11px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 2px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049, #2e7d32);
            color: white;
            border: 2px solid transparent;
        }
        
        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
            border-color: #4CAF50;
        }
        
        .premium-download {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00) !important;
            color: #000 !important;
            font-weight: 700;
            position: relative;
            overflow: hidden;
        }
        
        .premium-download::after {
            content: '⭐';
            position: absolute;
            top: 2px;
            right: 4px;
            font-size: 10px;
            animation: sparkle 2s infinite;
        }
        
        .premium-download:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6) !important;
            border-color: #FFD700 !important;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        
        .play-btn {
            background: linear-gradient(135deg, #2196F3, #1976D2, #0D47A1);
            color: white;
            border: 2px solid transparent;
        }
        
        .play-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
            border-color: #2196F3;
        }
        
        .quality-badge {
            background: linear-gradient(135deg, #FF5722, #E64A19, #BF360C);
            color: white;
            padding: 8px 12px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
            letter-spacing: 0.5px;
        }
        
        .language-flag {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 12px;
            background: linear-gradient(135deg, #9C27B0, #7B1FA2, #4A148C);
            color: white;
            padding: 8px 12px;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
            letter-spacing: 0.5px;
        }
        
        .size-info {
            background: linear-gradient(135deg, #FF9800, #F57C00, #E65100);
            color: white;
            padding: 8px 14px;
            border-radius: 25px;
            font-weight: 700;
            font-size: 13px;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
            letter-spacing: 0.5px;
            min-width: 60px;
            word-wrap: break-word;
        }
        
        .premium-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            color: #000;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
            letter-spacing: 0.5px;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4); }
            to { box-shadow: 0 4px 16px rgba(255, 215, 0, 0.8); }
        }
        
        .toggle-btn {
            background: linear-gradient(135deg, #673AB7, #512DA8);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin-bottom: 20px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(103, 58, 183, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Premium Download Feature Demo</h1>
        
        <button class="toggle-btn" onclick="togglePremium()">
            <i class="fas fa-crown"></i> Toggle Premium Status
        </button>
        
        <div id="premium-notification" class="premium-notification" style="display: none;">
            <i class="fas fa-crown" style="margin-right: 8px; color: #FF8C00;"></i>
            Premium Member: You have access to direct download links without any delay!
            <i class="fas fa-star" style="margin-left: 8px; color: #FF8C00;"></i>
        </div>
        
        <table class="download-links-table">
            <thead>
                <tr>
                    <th>Quality</th>
                    <th>Language</th>
                    <th>Size</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="quality-badge">1080p</span><span class="premium-badge" id="premium-badge-1" style="display: none;">PREMIUM</span></td>
                    <td><span class="language-flag">English</span></td>
                    <td><span class="size-info">2.5 GB</span></td>
                    <td>
                        <a href="#" class="download-btn" id="download-btn-1" onclick="handleDownload(1)">
                            <i class="fas fa-download"></i> <span id="download-text-1">Download</span>
                        </a>
                        <a href="#" class="play-btn">
                            <i class="fas fa-play"></i> Play Now
                        </a>
                    </td>
                </tr>
                <tr>
                    <td><span class="quality-badge">720p</span><span class="premium-badge" id="premium-badge-2" style="display: none;">PREMIUM</span></td>
                    <td><span class="language-flag">English</span></td>
                    <td><span class="size-info">1.2 GB</span></td>
                    <td>
                        <a href="#" class="download-btn" id="download-btn-2" onclick="handleDownload(2)">
                            <i class="fas fa-download"></i> <span id="download-text-2">Download</span>
                        </a>
                        <a href="#" class="play-btn">
                            <i class="fas fa-play"></i> Play Now
                        </a>
                    </td>
                </tr>
                <tr>
                    <td><span class="quality-badge">480p</span><span class="premium-badge" id="premium-badge-3" style="display: none;">PREMIUM</span></td>
                    <td><span class="language-flag">English</span></td>
                    <td><span class="size-info">800 MB</span></td>
                    <td>
                        <a href="#" class="download-btn" id="download-btn-3" onclick="handleDownload(3)">
                            <i class="fas fa-download"></i> <span id="download-text-3">Download</span>
                        </a>
                        <a href="#" class="play-btn">
                            <i class="fas fa-play"></i> Play Now
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        let isPremium = false;
        
        function togglePremium() {
            isPremium = !isPremium;
            updatePremiumStatus();
        }
        
        function updatePremiumStatus() {
            const notification = document.getElementById('premium-notification');
            const downloadBtns = document.querySelectorAll('.download-btn');
            const downloadTexts = document.querySelectorAll('[id^="download-text-"]');
            const premiumBadges = document.querySelectorAll('[id^="premium-badge-"]');
            
            if (isPremium) {
                notification.style.display = 'block';
                downloadBtns.forEach(btn => {
                    btn.classList.add('premium-download');
                });
                downloadTexts.forEach(text => {
                    text.textContent = 'Direct Download';
                });
                premiumBadges.forEach(badge => {
                    badge.style.display = 'inline';
                });
            } else {
                notification.style.display = 'none';
                downloadBtns.forEach(btn => {
                    btn.classList.remove('premium-download');
                });
                downloadTexts.forEach(text => {
                    text.textContent = 'Download';
                });
                premiumBadges.forEach(badge => {
                    badge.style.display = 'none';
                });
            }
        }
        
        function handleDownload(linkId) {
            if (isPremium) {
                alert('Premium User: Direct download started! No delay, no ads.');
            } else {
                alert('Regular User: Redirecting to short link with 10 second delay...');
            }
        }
    </script>
</body>
</html>
